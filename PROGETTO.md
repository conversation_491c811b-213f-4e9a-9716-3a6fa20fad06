# Portale Agenti LuccaCarta

**Scopo del Progetto:**

Il progetto "Portale Agenti LuccaCarta" è una piattaforma web progettata per facilitare e ottimizzare il lavoro degli agenti di LuccaCarta. L'obiettivo principale è fornire agli agenti uno strumento centralizzato per gestire le loro attività quotidiane, come la gestione dei clienti, la creazione e l'invio di ordini, la consultazione di listini e cataloghi, e il monitoraggio delle scadenze.

**Funzionalità Principali:**

*   **Gestione Clienti:** Gli agenti possono visualizzare, aggiungere, modificare e gestire le informazioni dei propri clienti. Questo include dati anagrafici, indirizzi, contatti e altre informazioni rilevanti.
*   **Gestione Ordini:** La piattaforma permette agli agenti di creare nuovi ordini, modificare quelli esistenti, visualizzare lo storico degli ordini e inviarli a LuccaCarta.
*   **Listini e Cataloghi:** Gli agenti hanno accesso ai listini prezzi aggiornati e ai cataloghi prodotti, facilitando la preparazione di preventivi e ordini.
*   **Scadenzario:** Il portale offre una sezione per monitorare le scadenze dei pagamenti dei clienti, aiutando gli agenti a tenere traccia delle situazioni finanziarie.
*   **Integrazione con il Sistema AS400:** Il portale è collegato al sistema AS400 di LuccaCarta, permettendo la sincronizzazione di dati come clienti, prodotti e ordini. Questo garantisce che le informazioni siano sempre aggiornate e coerenti.
*   **Gestione Utenti:** Gli amministratori possono gestire gli account degli agenti, assegnando ruoli e permessi.
*   **Reportistica:** Il sistema fornisce report di base per monitorare l'attività degli agenti e lo stato degli ordini.
*   **Comunicazione:** Il portale facilita la comunicazione tra gli agenti e LuccaCarta, con notifiche e messaggi.

**Benefici:**

*   **Efficienza:** Centralizzando le attività, il portale riduce il tempo e lo sforzo necessari per la gestione degli ordini e dei clienti.
*   **Aggiornamenti in Tempo Reale:** Grazie all'integrazione con AS400, gli agenti hanno sempre accesso a informazioni aggiornate.
*   **Migliore Organizzazione:** Il portale aiuta gli agenti a tenere traccia delle loro attività, migliorando l'organizzazione e la produttività.
*   **Comunicazione Facilitata:** La piattaforma semplifica la comunicazione tra gli agenti e LuccaCarta.
*   **Riduzione degli Errori:** L'automazione di alcuni processi riduce il rischio di errori nella gestione degli ordini e dei dati.

**Destinatari:**

*   Agenti di LuccaCarta
*   Amministratori di LuccaCarta

## Funzionalità di Sincronizzazione Automatica (Cron Jobs)

Il portale include un sistema di sincronizzazione automatica che mantiene aggiornati i dati tra il portale web e il sistema AS400 di LuccaCarta attraverso job programmati che vengono eseguiti ogni notte.

### Funzionalità Attive (Configurate nel Crontab)

Le seguenti funzionalità sono attualmente attive e vengono eseguite automaticamente secondo la programmazione del crontab:

#### 1. Sincronizzazione Codici Cliente (`syncCodiciCliente`)
- **Orario di esecuzione:** 04:10 ogni giorno
- **Funzione:** Cerca i clienti nel portale che non hanno un codice cliente assegnato e li abbina ai clienti presenti nel sistema JG utilizzando partita IVA o codice fiscale
- **Processo:**
  - Identifica clienti senza codice nel portale
  - Cerca corrispondenze in JG tramite P.IVA o CF
  - Aggiorna il codice cliente nel portale quando trova una corrispondenza

#### 2. Sincronizzazione Listini (`syncListini`)
- **Orario di esecuzione:** 04:15 ogni giorno
- **Funzione:** Aggiorna completamente il listino prezzi dal sistema JG
- **Processo:**
  - Svuota la tabella listino locale
  - Importa tutti i prodotti con prezzi, descrizioni e unità di misura da JG
  - Gestisce prodotti sospesi/non più disponibili
  - Aggiorna il file Excel del listino

#### 3. Sincronizzazione Ordini (`syncOrdini`)
- **Orario di esecuzione:** 04:20 ogni giorno
- **Funzione:** Sincronizza gli ordini degli agenti con eventuali modifiche fatte direttamente su JG
- **Processo:**
  - Controlla ordini recenti inviati (ultimi 2 giorni)
  - Verifica modifiche apportate in JG
  - Aggiorna i prodotti ordinati nel portale

#### 4. Aggiornamento Ordini ERP (`ordini_aggiorna_ERP_gg`)
- **Orario di esecuzione:** 04:25 ogni giorno
- **Funzione:** Aggiorna i campi TDOCOO e NROROO degli ordini che sono stati processati in JG
- **Processo:**
  - Cerca ordini degli ultimi 7 giorni senza codici ERP
  - Verifica se sono stati trasformati in ordini effettivi in JG
  - Aggiorna i riferimenti ERP nel portale

#### 5. Importazione Ordini JG (`importOrdiniJG_gg`)
- **Orario di esecuzione:** 04:30 ogni giorno
- **Funzione:** Importa nel portale gli ordini creati direttamente in JG che non sono presenti nel portale
- **Processo:**
  - Cerca ordini degli ultimi 7 giorni in JG non presenti nel portale
  - Crea nuovi ordini nel portale con tutti i dettagli
  - Associa ordini ai clienti e agenti corrispondenti

#### 6. Importazione Scadenzario JG (`importScadenzrioJG`)
- **Orario di esecuzione:** 05:10 ogni giorno
- **Funzione:** Importa lo scadenzario completo da JG
- **Processo:**
  - Elimina i record esistenti con fonte "JG"
  - Importa tutti i dati dello scadenzario da JG
  - Mantiene separati i dati caricati manualmente (fonte "LC")

### Funzionalità Work in Progress / Non Attive

#### 1. Sequenza Aggiornamento Completa (`sequenzaAggiornamento`)
- **Stato:** Disabilitata (commentata nel crontab)
- **Motivo:** Si fermava dopo la prima chiamata quando eseguita da crontab
- **Funzione:** Eseguiva in sequenza tutte le sincronizzazioni principali
- **Note:** Sostituita dalle chiamate individuali separate

#### 2. Sincronizzazione Evasione Ordini (`sincronizzaEvasioneOrdini`)
- **Stato:** Work in Progress
- **Funzione:** Aggiorna lo stato di evasione degli ordini basandosi sulle bolle/fatture
- **Limitazioni:**
  - Processa massimo 5 ordini per esecuzione
  - Logica di matching ancora in sviluppo
  - Alcune parti del codice sono commentate

#### 3. Sincronizzazione Bolle/Fatture (`syncBF`)
- **Stato:** Implementata ma non attiva nel crontab
- **Funzione:** Sincronizza bolle e fatture di oggi e ieri
- **Caratteristiche:**
  - Aggiorna ordini web esistenti con dati delle bolle/fatture
  - Inserisce nuovi ordini da bolle/fatture create direttamente in JG
  - Distingue tra ordini web e ordini tradizionali

### Note Tecniche

- Tutte le funzioni sono progettate per essere eseguite da riga di comando
- Il sistema previene l'esecuzione se un utente è loggato nel browser
- Le funzioni gestiscono grandi volumi di dati attraverso elaborazione a batch
- Ogni sincronizzazione include logging per monitoraggio e debug
- Il sistema mantiene la coerenza dei dati tra portale e AS400/JG

**In Sintesi:**

Il "Portale Agenti LuccaCarta" è uno strumento web che mira a semplificare e migliorare il lavoro degli agenti, fornendo loro un accesso centralizzato a tutte le informazioni e le funzionalità necessarie per gestire efficacemente le loro attività quotidiane. Il portale è progettato per essere intuitivo e facile da usare, con l'obiettivo di aumentare la produttività e l'efficienza degli agenti. Il sistema di sincronizzazione automatica garantisce che i dati siano sempre aggiornati e coerenti tra il portale web e il sistema gestionale aziendale.
