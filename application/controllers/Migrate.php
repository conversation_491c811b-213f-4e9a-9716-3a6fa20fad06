<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON>grate extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->library('migration');
    }

    public function index() {
        if ($this->migration->latest() === FALSE) {
            echo $this->migration->error_string();
        } else {
            echo "Migrazione eseguita con successo!";
        }
    }
}
