<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Notifiche extends CI_Controller {

    public function __construct() {
        parent::__construct();
        $this->load->model(['Notifichemodel', 'Agentimodel', 'Utentimodel']);

        // Verifica autenticazione
        if(empty($this->session->userdata('idutente'))) {
            redirect('login');
        }
    }

    public function index() {
        $data['notifiche'] = $this->Notifichemodel->get_notifiche();
        $this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Notifiche']);
        $this->load->view('notifiche/list', $data);
        $this->load->view('common/footer');
    }

    public function create() {
        $id_scadenzario = $this->input->get('id_scadenzario') ?: $this->input->post('id_scadenzario');

        if (!$id_scadenzario) {
            $this->session->set_flashdata(['msg'=>'ID scadenzario mancante', 'msgtype'=>'danger']);
            redirect('scadenzario');
        }

        // Recupera scadenzario e relative informazioni
        $this->load->model('Scadenzariomodel');
        $scadenzario = $this->Scadenzariomodel->getscadenzario($id_scadenzario);
        if (!$scadenzario) {
            $this->session->set_flashdata(['msg'=>'Scadenzario non trovato', 'msgtype'=>'danger']);
            redirect('scadenzario');
        }

        $agente = $this->Agentimodel->getagentebycodice($scadenzario->agente);

        // Recupera admin
        $admin_users = $this->Utentimodel->getutenti(['idruolo' => 1])[0]->result();

        if ($this->input->post()) {
            $notifica_data = array(
                'conto' => $scadenzario->conto,
                'partita' => $scadenzario->partita,
                'scadenza' => $scadenzario->scadenza,
                'titolo' => $this->input->post('titolo'),
                'testo' => $this->input->post('testo'),
                'destinatari' => $this->input->post('destinatari'),
                'data_programmata' => $this->input->post('data_programmata')
            );

            $this->Notifichemodel->insert($notifica_data);
            $this->session->set_flashdata(['msg'=>'Notifica programmata con successo', 'msgtype'=>'success']);
            redirect('scadenzario/modifica/' . $id_scadenzario);
        }

        // Carica dati aggiuntivi se necessario
        $this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Programma Notifica']);
        $this->load->view('notifiche/form', [
            'id_scadenzario' => $id_scadenzario,
            'agente' => $agente,
            'admin_users' => $admin_users
        ]);
        $this->load->view('common/footer');
    }

    // Aggiungi nuovo metodo per il cron
    public function modifica($id) {
        $data['notifica'] = $this->Notifichemodel->get_notifica($id);

        // Recupera dati per i checkbox come nella creazione
        $this->load->model('Scadenzariomodel');
        $scadenzario = $this->Scadenzariomodel->getscadenzarioByFields(
            $data['notifica']->conto,
            $data['notifica']->partita,
            $data['notifica']->scadenza
        );

        if (!$scadenzario) {
            $this->session->set_flashdata(['msg'=>'Scadenzario associato non trovato', 'msgtype'=>'warning']);
            redirect('scadenzario');
        }

        $agente = $this->Agentimodel->getagentebycodice($scadenzario->agente);
        $admin_users = $this->Utentimodel->getutenti(['idruolo' => 1])[0]->result();

        if ($this->input->post()) {
            $notifica_data = array(
                'titolo' => $this->input->post('titolo'),
                'testo' => $this->input->post('testo'),
                'destinatari' => $this->input->post('destinatari'),
                'data_programmata' => $this->input->post('data_programmata')
            );

            if ($this->Notifichemodel->update($id, $notifica_data)) {
                $this->session->set_flashdata(['msg'=>'Notifica modificata con successo', 'msgtype'=>'success']);
                redirect('scadenzario/modifica/' . $scadenzario->id);
            }
        }

        // Aggiungi questi dati alla view
        $data['id_scadenzario'] = $scadenzario->id;  // Modified this line
        $data['agente'] = $agente;
        $data['admin_users'] = $admin_users;

        $this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Modifica Notifica']);
        $this->load->view('notifiche/form', $data);
        $this->load->view('common/footer');
    }

    public function cron_invia_notifiche() {
        $this->load->library('email');
        $this->load->model('Scadenzariomodel');
        $notifiche = $this->Notifichemodel->get_notifiche_da_inviare();
        $errors = [];

        foreach ($notifiche as $notifica) {
            try {
                // Recupera lo scadenzario associato alla notifica
                $scadenzario = $this->Scadenzariomodel->getscadenzarioByFields(
                    $notifica->conto,
                    $notifica->partita,
                    $notifica->scadenza
                );

                if (!$scadenzario) {
                    throw new Exception("Scadenzario non trovato per la notifica ID: {$notifica->id}");
                }

                // Estrai gli ID degli utenti dai destinatari
                $destinatari = explode(',', $notifica->destinatari);
                if (empty($destinatari)) {
                    throw new Exception("Nessun destinatario trovato per la notifica ID: {$notifica->id}");
                }

                $sent_count = 0;
                foreach ($destinatari as $id_utente) {
                    $id_utente = (int)trim($id_utente);
                    $user = $this->Utentimodel->getutente($id_utente);
                    if (!$user) {
                        $errors[] = "Utente ID {$id_utente} non trovato per la notifica ID: {$notifica->id}";
                        continue;
                    }

                    $data = [
                        'destinatario'      => $user,
                        'titolo'            => $notifica->titolo,
                        'testo'             => $notifica->testo,
                        'link_scadenzario'  => base_url('scadenzario/modifica/' . $scadenzario->id)
                    ];

                    $message = $this->load->view('emails/notifica_email', $data, TRUE);

                    $this->email->clear();
                    $this->email->from($this->config->item('mailfrom'), $this->config->item('mailfromname'));
                    $this->email->to($user->email);
                    $this->email->subject('Notifica: ' . $notifica->titolo);
                    $this->email->message($message);
                    $this->email->set_mailtype('html');

                    if (!$this->email->send()) {
                        throw new Exception("Errore nell'invio email a {$user->email} per la notifica ID: {$notifica->id}");
                    }
                    $sent_count++;
                }

                if ($sent_count > 0) {
                    // Aggiorna lo stato solo se almeno una mail è stata inviata
                    $this->Notifichemodel->update_stato(
                        $notifica->id,
                        'inviata',
                        date('Y-m-d H:i:s')
                    );
                }

            } catch (Exception $e) {
                $errors[] = $e->getMessage();
            }
        }

        // Se ci sono errori, invia email di notifica
        if (!empty($errors)) {
            $this->email->clear();
            $this->email->from($this->config->item('mailfrom'), $this->config->item('mailfromname'));
            $this->email->to('<EMAIL>');
            $this->email->subject('Errori nell\'invio notifiche automatiche');
            $this->email->message(
                "<h2>Errori riscontrati durante l'invio delle notifiche</h2>" .
                "<ul><li>" . implode("</li><li>", $errors) . "</li></ul>" .
                "<p>Data: " . date('d/m/Y H:i:s') . "</p>"
            );
            $this->email->set_mailtype('html');
            $this->email->send();
        }

        echo "Notifiche elaborate: " . count($notifiche) . "\n";
        echo "Errori riscontrati: " . count($errors);
    }

    public function elimina($id) {
        // Recupera la notifica
        $notifica = $this->Notifichemodel->get_notifica($id);

        if ($notifica) {
            // Recupera lo scadenzario usando i campi della notifica
            $this->load->model('Scadenzariomodel');
            $scadenzario = $this->Scadenzariomodel->getscadenzarioByFields(
                $notifica->conto,
                $notifica->partita,
                $notifica->scadenza
            );

            if ($scadenzario) {
                $this->Notifichemodel->elimina($id);
                $this->session->set_flashdata(['msg'=>'Notifica eliminata con successo', 'msgtype'=>'success']);
                redirect('scadenzario/modifica/' . $scadenzario->id);
            } else {
                $this->session->set_flashdata(['msg'=>'Scadenzario associato non trovato', 'msgtype'=>'warning']);
                redirect('scadenzario');
            }
        } else {
            $this->session->set_flashdata(['msg'=>'Notifica non trovata', 'msgtype'=>'danger']);
            redirect('scadenzario');
        }
    }
}
