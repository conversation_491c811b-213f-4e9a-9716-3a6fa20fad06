<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Registrazione extends CI_CONTROLLER {
	public function __construct() {
		parent::__construct();
		$this->load->model('Utentimodel');
	}

	public function index() {
		$recaptcha = new Recaptcha();
		$box = $recaptcha->create_box();
		// $recaptcha = new \AlbertCht\InvisibleReCaptcha\InvisibleReCaptcha(
		//     $this->config->item('recaptcha.sitekey'),
		//     $this->config->item('recaptcha.secret'),
		//     $this->config->item('recaptcha.options')
		// ); // nella view $recaptcha->render();
		$this->load->view('common/head');
		$this->load->view('common/registrazione', ['recaptcha' => $box]);
		$this->load->view('common/footer');
	}

	public function checkusername($idutente='') {
		$user=$this->input->get('username');
		$where['username']=$user;
		if ($idutente!='') $where['U.idutente!=']=$idutente;
		$utente=$this->Utentimodel->getrsutente($where);
		if ($utente===false) {
			echo 'ok';
		}
		else echo 'Utente già registrato con questo nome';
	}

	public function checkemail($idutente='') {
		$user=$this->input->get('email');
		$where['email']=$user;
		if ($idutente!='') $where['U.idutente!=']=$idutente;
		$utente=$this->Utentimodel->getrsutente($where);
		if ($utente===false) {
			echo 'ok';
		}
		else echo 'Utente già registrato con questa email';
	}



	public function cambiapassword() {
		if (!$this->session->idutente) {
			redirect(base_url('login'));
		}
		
		if ($this->input->post('password')) {
			$this->Utentimodel->cambiapassword($this->input->post('password'));
			$this->session->set_flashdata([
				'msg' => 'Password cambiata con successo',
				'msgtype' => 'info'
			]);
			redirect(base_url('login/logout'));
		}
		
		$this->load->view('common/head');
		$this->load->view('common/cambiapassword');
		$this->load->view('common/footer');
	}

	public function passworddimenticata() {
		if ($this->input->post('username')) {
			$username = $this->input->post('username');
			$rsutente = $this->Utentimodel->getrsutente(['username'=>$username]);
			if ($rsutente && $rsutente->num_rows()==1) {
				$utente = $rsutente->row();
				$password = generatePassword();
				$data = array(
					'datacambiopass' => null,
					'password' => md5($password),
				);
				$this->Utentimodel->modificautente($utente->idutente, $data);
				$this->mail_password_dimenticata($utente, $password);
				$this->session->set_flashdata([
					'msg' => 'La nuova password temporanea è stata inviata alla tua e-mail.',
					'msgtype' => 'info'
				]);
				redirect(base_url('login'));
			}
		}
		$this->load->view('common/head');
		$this->load->view('common/passworddimenticata');
		$this->load->view('common/footer');
	}

	/**
	 * { function_description }
	 *
	 * @param      obj  $utente    The utente
	 * @param      string  $password  The password
	 */
	private function mail_password_dimenticata($utente, $password) {
		$this->load->library('email');
		$config['mailtype'] = 'html';
		$this->email->initialize($config);

		$this->email->from($this->config->item('mailfrom') , $this->config->item('mailfromname'));
		// $this->email->to($utente->email);
		$this->email->to('<EMAIL>');

		$this->email->subject('Portale agenti - Password dimenticata');
		$messaggio=$this->load->view('mail/passworddimenticata', ['utente'=>$utente, 'password'=>$password], true);
		$this->email->message($messaggio);

		$this->email->send();
	}

	public function registrazione() {
		$data=$this->input->post();
		unset($data['password_confirm']);
		if ($idutente=$this->Utentimodel->nuovo($data)) {
			$utente=$this->Utentimodel->getutente($idutente);
			// $this->mail_registrazione($utente);
			$this->session->set_flashdata(['msg'=>'Agente aggiunto', 'msgtype'=>'success']);
			redirect(base_url('admin/agente_lista'));
		}
		else {
			$this->session->set_flashdata(['msg'=>'Problema di registrazione, riprova più tardi', 'msgtype'=>'warning']);
			redirect(base_url('admin/agente_nuovo'));
		}
	}
 
	public function registrazione_ok() {
		$this->load->view('common/head');
		$this->load->view('common/registrazione_ok');
		$this->load->view('common/footer');
	}

}
