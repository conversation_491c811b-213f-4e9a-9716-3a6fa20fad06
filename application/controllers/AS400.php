<?php

defined('BASEPATH') or exit('No direct script access allowed');

class AS400 extends CI_Controller
{
    // protected $db;

    public function __construct()
    {
        parent::__construct();
        // $this->db = $this->load->database('AS400SP', true);
        // var_dump($this->db);exit;
        $this->load->model('AS400/ASClientiModel');
        $this->load->model('AS400/ASOrdiniModel');
    }

    public function queryCarrello($idordine)
    {
        $carrello = $this->ASOrdiniModel->getCarrello($idordine);
        var_dump($carrello);
        $this->load->view('common/footer');
    }

    public function getProdotti()
    {
        $this->load->model('AS400/ASProdottiModel');
        $a = $this->ASProdottiModel->getProdotti();
        var_dump($a);
    }

    /**
     * prodotto da JP
     *
     * @param [type] $codice
     * @return void
     */
    public function getProdotto($codice)
    {
        $this->load->model('AS400/ASProdottiModel');
        $a = $this->ASProdottiModel->getProdotto($codice);
        var_dump($a);
    }

    /**
     * Elenco prodotti JG
     *
     * @return void
     */
    public function getListini()
    {
        $this->load->model('AS400/ASProdottiModel');
        $a = $this->ASProdottiModel->getListini(0, 100);
        var_dump($a);
    }

    /**
     * Prende i codici clienti portale, cerca su jg, prende piva da jg e la mette nel portale
     * fa blocchi di 100 clienti a partire da start
     * @param int $start parte da
     * @return void
     */
    public function syncPivaCliente($start = 0)
    {
        $this->load->model('AS400/ASClientiModel');
        // $this->db->where('codice', '15700');
        $rs = $this->db->get('clienti');
        $clienti = $rs->result();
        // $clienti = $this->Clientimodel->getclienti();
        // var_dump($clienti);exit;
        $i = 0;
        foreach ($clienti as $c) {
            if (($c->codice != '') && ($i >= $start)) {
                $clientejg = $this->ASClientiModel->getCliente($c->codice);
                $pivajg = '';
                if (!is_null($clientejg)) {
                    $pivajg = trim($clientejg->FLCH07);
                    $piva = trim($c->piva);
                    if (($pivajg != '') && ($piva != $pivajg)) {
                        echo "codice: " . $c->codice . " piva: " . $piva . " pivajg: " . $pivajg;
                        echo "<br>";
                        $this->db->update('clienti', ['piva' => $pivajg], ['idcliente' => $c->idcliente]);
                    }
                }
            }
            $i++;
            // if ($i > ($start + 10)) break;
        }
        echo $i;
    }
    /**
     * Confronta per ogni codice cliente la ragione sociale e crea un report delle differenze
     * serve per capire se ci sono possibili clienti con codice errato
     *
     * @return void
     */
    public function reportCodiciCliente($start = 0)
    {
        $this->load->model('Clientimodel');
        $this->load->model('AS400/ASClientiModel');

        $clienti = $this->Clientimodel->getclienti();
        $i = 0;
        echo "<table border='1'>";
        echo "<tr><th>Codice portale</th><th>Portale</th><th>JGalileo</th></tr>";
        foreach ($clienti as $cliente) {
            if (($cliente->codice != '') && ($i >= $start)) {
                $clienteJG = $this->ASClientiModel->getCliente($cliente->codice);
                $codice = $cliente->codice;
                $ragsoc = strtoupper(trim($cliente->ragsoc));
                $ragsocjg = strtoupper(trim($clienteJG->DSCOKP));
                if ($clienteJG && (substr($ragsoc, 0, 10) != substr($ragsocjg, 0, 10))) {
                    echo "<tr><td>$codice</td><td>$ragsoc</td><td>$ragsocjg</td></tr>";
                }
            }
            $i++;
            if ($i > ($start + 500)) break;
        }
        echo "</table>";
        echo $i;
    }
    public function reportCodiciClienteArray()
    {
        $arr_codici = array(15129, 13999, 100, 13493, 15604, 124, 12870, 13744, 10683, 7432, 8411, 2449, 4720, 14927, 7689, 13135, 13205, 14353, 13512, 15577, 9620, 8015, 14294, 10837, 15046, 14251, 10855, 14913, 10734, 13735, 15762, 11586, 15065, 12854, 12469, 10751, 15927, 14900, 14970, 12434, 13430, 13732, 6521, 3136, 14065, 13167, 14375, 13523, 15144, 15347, 15122, 15181, 14557, 15161, 14362, 301012289, 15165, 15191, 15177, 15131, 15208, 15211, 15209, 10405, 15216, 15234, 15235, 15226, 15298, 15240, 15315, 15390, 15320, 15246, 15293, 15274, 15273, 15337, 15302, 15278, 15269, 15279, 15300, 15290, 15334, 13713, 15292, 15295, 15323, 15310, 15408, 15348, 15116, 14761, 15336, 15335, 15343, 301015097, 15360, 13111, 15351, 15341, 15172, 14588, 15399, 15405, 15465, 15402, 15400, 15366, 15378, 15374, 15397, 15392, 15398, 15377, 15395, 15418, 15430, 15438, 12461, 15433, 15436, 15434, 15435, 15445, 15456, 15439, 15474, 301015093, 15457, 15466, 12497, 15472, 15478, 15118, 15492, 15493, 15497, 15501, 15504, 15065, 301015085, 15535, 15510, 12839, 14847, 13441, 301015207, 15530, 15534, 15555, 15566, 15652, 15567, 15565, 13160, 15574, 15156, 15584, 15560, 11080, 15596, 12450, 15598, 15600, 15602, 15603, 15605, 15611, 11242, 15612, 15616, 15630, 12524, 15689, 15638, 15583, 15639, 14078, 15643, 15645, 10866, 15646, 13926, 15656, 15655, 15678, 15662, 15666, 15667, 15668, 15711, 15671, 15673, 15677, 15676, 15737, 15680, 15681, 15691, 15697, 15698, 15701, 15700, 535, 15704, 15707, 15714, 15718, 15722, 15720, 15721, 14465, 15790, 15727, 15726, 15725, 15729, 15667, 15739, 15741, 15747, 15754, 15769, 15759, 15761, 15764, 15803, 15766, 15770, 15771, 15774, 15779, 8906, 15781, 11107, 15793, 15795, 15797, 15799, 15811, 14343, 15820, 15816, 15817, 15821, 15822, 15828, 15841, 15850, 15856, 15077, 15858, 15862, 15867, 15868, 15871, 15872, 15877, 15875, 15880, 15888, 15885, 15887, 15899, 15894, 15904, 15886, 15911, 15903, 15906, 13471, 15908, 15897, 15912, 15916, 15914, 15917, 15922, 9706, 15785, 15926, 15928, 15931, 13421, 15934, 15935);
        $this->load->model('Clientimodel');
        $this->load->model('AS400/ASClientiModel');

        echo "<table border='1'>";
        echo "<tr><th>Codice portale</th><th>Portale</th><th>JGalileo</th></tr>";
        $replace = array(".", "-", "'");
        foreach ($arr_codici as $codice) {
            $cliente = $this->Clientimodel->getclientebycodice($codice);
            $clienteJG = $this->ASClientiModel->getCliente($codice);
            if ($clienteJG) {
                $ragsoc = preg_replace("/\s+/", " ", str_replace($replace, '', strtoupper(trim($cliente->ragsoc))));
                $ragsocjg = preg_replace("/\s+/", " ", str_replace($replace, '', strtoupper(trim($clienteJG->DSCOKP))));
                if (strpos($ragsocjg, substr($ragsoc, 0, 8)) === false) {
                    echo "<tr><td>$codice</td><td>$ragsoc</td><td>$ragsocjg</td></tr>";
                }
            }
        }
        echo "</table>";
    }

    public function getCliente($codice)
    {
        $this->load->model('AS400/ASClientiModel');
        $a = $this->ASClientiModel->getCliente($codice);
        var_dump($a);
    }
    public function getCliente_piva($piva)
    {
        $this->load->model('AS400/ASClientiModel');
        $a = $this->ASClientiModel->getCliente_piva($piva);
        var_dump($a);
    }

    public function getOrdineJG($doc_erp)
    {
        $ordine = $this->ASOrdiniModel->getOrdineJG($doc_erp);
        $testo = print_r($ordine, true);

        $this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Artcoli AS400']);
        $this->load->view('common/body', [
            'title' => '',
            'testo' => $testo,
        ]);
        $this->load->view('common/footer');
    }

    public function listaViews()
    {
        $sql = "SELECT DISTINCT TABLE_SCHEMA FROM QSYS2.VIEWS limit 50";
        $query = $this->db->query($sql);
        $a = $query->result();

        $testo = print_r($a, true);

        $this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Lista views AS400']);
        $this->load->view('common/body', [
            'title' => '',
            'testo' => $testo,
        ]);
        $this->load->view('common/footer');
    }

    public function listaSchemi()
    {
        // $where="WHERE TABLE_TYPE='T'";
        // $where.=" AND TABLE_SCHEMA LIKE '%LUC%'";

        $sql = "SELECT DISTINCT TABLE_SCHEMA FROM QSYS2.TABLES $where limit 50";
        $query = $this->db->query($sql);
        $a = $query->result();

        $testo = print_r($a, true);

        $this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Lista schemi AS400']);
        $this->load->view('common/body', [
            'title' => '',
            'testo' => $testo,
        ]);
        $this->load->view('common/footer');
    }

    public function listaTabelle($nome = '')
    {
        // $where=""; //TABLE_NAME LIKE '%SPA%'
        // if ($nome!='') $where = "TABLE_NAME LIKE '%".($nome)."%'";

        $where = " TABLE_TYPE='T'";
        $where .= " AND TABLE_SCHEMA LIKE '%TLU%'";
        $where .= " AND TABLE_TEXT LIKE '%clienti%'";

        $sql = "SELECT * FROM QSYS2.SYSTABLES WHERE $where limit 100";
        $query = $this->db->query($sql);
        $a = $query->result();

        $testo = print_r($a, true);

        $this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Lista tabelle AS400']);
        $this->load->view('common/body', [
            'title' => '',
            'testo' => $testo,
        ]);
        $this->load->view('common/footer');
    }

    public function rollback($idcart)
    {
        $this->ASOrdiniModel->rollback($idcart);
    }

    /**
     * Aggiorna le info del cliente prendendoli da JG
     *
     * @return void
     */
    public function syncAnagClienti()
    {
        $numbatch = 500;
        $datasync = date('Y-m-d');
        // $this->load->model('Clientimodel');
        $this->load->model('AS400/ASClientiModel');

        $this->db->select('C.*');
        $this->db->from('clienti C');
        $this->db->where('datasync <>', $datasync);
        $this->db->or_where('datasync is null');

        $this->db->order_by('idcliente', 'desc');

        $rs = $this->db->get();
        $clienti = $rs->result();

        $i = 0;
        foreach ($clienti as $cliente) {
            $i++;
            $update = array();
            if ($cliente->codice != '') {
                $clienteJG = $this->ASClientiModel->getcliente($cliente->codice);
                if ($clienteJG) {
                    // var_dump(($clienteJG)); exit;
                    // if ($clienteJG->CAP != '' && trim($clienteJG->CAP) != trim($cliente->cap) $update['cap'] = trim($clienteJG->CAP);
                    if ($clienteJG->DSCOKP != '' && trim(strtolower($clienteJG->DSCOKP)) != trim(strtolower($cliente->ragsoc))) $update['ragsoc'] = trim(ucwords(strtolower($clienteJG->DSCOKP)));
                    if ($clienteJG->INDRKP != '' && trim(strtolower($clienteJG->INDRKP)) != trim(strtolower($cliente->indirizzo))) $update['indirizzo'] = trim(ucwords(strtolower($clienteJG->INDRKP)));
                    if ($clienteJG->FLCH07 != '' && trim(strtolower($clienteJG->FLCH07)) != trim(strtolower($cliente->piva))) $update['piva'] = trim($clienteJG->FLCH07);
                    if ($clienteJG->LOCAKP != '' && trim(strtolower($clienteJG->LOCAKP)) != trim(strtolower($cliente->citta))) $update['citta'] = trim(ucwords(strtolower($clienteJG->LOCAKP)));
                    if ($clienteJG->PROVKP != '' && trim(strtolower($clienteJG->PROVKP)) != trim(strtolower($cliente->provincia))) $update['provincia'] = trim($clienteJG->PROVKP);
                }
                $update['datasync'] = $datasync;
                if (count($update) > 0) $this->db->update('clienti', $update, ['codice' => $cliente->codice]);
            } else {
                $update['datasync'] = $datasync;
                $this->db->update('clienti', $update, ['codice' => $cliente->codice]);
            }
            if ($i >= $numbatch) break;
        }
        if (count($clienti) == 0) {
            echo "finito";
        } else {
            echo '<a href="' . base_url('/AS400/syncAnagClienti') . '">Prossimo batch di ' . $numbatch . ', rimasti: ' . (count($clienti) - $numbatch);
        }
    }

    public function getScadenzario($start = 0, $limit = 10)
    {
        $this->load->model('AS400/ASScadenzarioModel');
        $s = $this->ASScadenzarioModel->getScadenzario($start, $limit);
        var_dump($s);
        exit;
        $this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'AS400']);
        $this->load->view('as400/queryscadenzario', ['scadenzario' => $s]);
        $this->load->view('common/footer');
    }

    public function getOrdini($data_inizio = '', $data_fine = '', $limite = 10)
    {
        $this->load->model('AS400/ASOrdiniModel');
        $s = $this->ASOrdiniModel->getOrdiniJG($data_inizio, $data_fine, $limite);
        var_dump($s);
        exit;
    }

    function apritabella($TDOCOC = '', $NROROC = '')
    {
        $this->db = $this->load->database('AS400SP', true);
        // fa una select per vedere i campi e dati della tabella LUC90DAT/OCCOM00F
        $sql = "SELECT * FROM  " . $this->db->database2 . ".OCCOM00F";
        $sql .= " WHERE NRRGOC!=0";
        if (!empty($TDOCOC) && !empty($NROROC)) $sql .= " AND TDOCOC='$TDOCOC' AND NROROC=$NROROC";
        $sql .= " ORDER BY DT01OC desc";
        $sql .= " LIMIT 10";
        echo "$sql<br>";
        $query = $this->db->query($sql);
        $res = $query->result();
        var_dump($res);
    }

    public function getBolleFatture($numerobolla = 0, $numerofattura = 0, $codicecliente = 0, $numerp=0)
    {
        $this->db = $this->load->database('AS400SP', true);
        $campi = array(
            'T.DT01FM as DataImmissioneRec',
            'T.CDDTFM as CodiceDitta',
            'T.CDAGFM as CodiceAgente',
            'T.TDOCFM as TipoDocumento',
            'T.NRDFFM as NumeroDocumentoFatturaz',
            'T.CDCFFM as CodiceCliente',
            'T.DTBOFM as DataBolla',
            'T.NRBOFM as NumeroBolla',
            'T.DTFTFM as DataFattura',
            'T.NRFTFM as NumFattura',
            'R.TPORFM',
            'R.NRORFM',
            'R.NRGOFM',
            'R.QTFTFM AS QTA',
            'R.PZNEFM1 AS PrezzoNettoUnitarioBase',
            'R.PZNEFM AS PrezzoNetto',
        );
        $sql = "SELECT " . implode(',', $campi) . " FROM  " . $this->db->database2 . ".FTMOV01U as T";
        // $sql = "SELECT * FROM  " . $this->db->database2 . ".FTMOV01U";
        $sql .= " JOIN " . $this->db->database2 . ".FTMOV02U AS R ON T.CDDTFM=R.CDDTFM AND T.TDOCFM=R.TDOCFM AND T.NRDFFM=R.NRDFFM";
        $sql .= " WHERE T.NRRGFM=0 AND T.TDOCFM!='C'";
        if($numerp>0) $sql.=" AND R.TPORFM='W' AND R.NRORFM=$numerp";
        if ($numerobolla > 0) $sql .= " AND T.NRBOFM=$numerobolla";
        if ($numerofattura > 0) $sql .= " AND T.NRFTFM=$numerofattura";
        if ($codicecliente > 0) {
            $codicecliente = aggiustacodicecliente($codicecliente);
            $sql .= " AND T.CDCFFM=$codicecliente";
        }
        $sql .= " ORDER BY T.DT01FM desc";
        $sql .= " LIMIT 10";
        echo "$sql<br>";
        $query = $this->db->query($sql);
        $res = $query->result();
        var_dump($res);
    }

    /**
     * Ritorna testata e dettaglio
     * numerobolla e codicecliente fanno da chiave
     * numerofattura e codicecliente fanno da chiave
     * in pratica codicecliente ci serve sempre nel db ci possono essere bolle con stesso numero ma codice cliente diverso
     *
     * @param integer $numerobolla
     * @param integer $numerofattura
     * @param integer $codicecliente
     * @return void
     */
    public function getTestateBolleFatture($numerobolla = 0, $numerofattura = 0, $codicecliente = 0, $docerp='')
    {
        $this->load->model('AS400/ASBolleFattureModel');
        $t = $this->ASBolleFattureModel->getTestate($numerobolla, $numerofattura, $codicecliente, $docerp);
        $bf=array();
        foreach ($t as $testata) {
            $r = $this->ASBolleFattureModel->getRighe($testata->CODICEDITTA, $testata->TIPODOCUMENTO, $testata->NUMERODOCUMENTOFATTURAZ);
            $bf[]=array('testata' => $testata, 'righe' => $r);
        }
        var_dump($bf);
        return $bf;
    }

    public function getBFSenzaOrdini() {
        $this->load->model('AS400/ASBolleFattureModel');
        $t = $this->ASBolleFattureModel->getBFSenzaOrdini();
        var_dump($t);
    }

    public function getRigheOrdineERP($numeroOrdineERP) {
    $this->load->model('AS400/ASBolleFattureModel');
    $righe = $this->ASBolleFattureModel->getRigheByOrdineERP($numeroOrdineERP);
    var_dump($righe);
}

}

/* End of file AS400.php */
