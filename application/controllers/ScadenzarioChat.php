<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ScadenzarioChat extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        if ($this->session->login !== true) redirect(base_url('login'));
        
        $this->load->model('ScadenzarioChatModel');
        $this->load->model('Agentimodel');
        $this->load->model('Utentimodel');
        $this->load->helper('html');
        $this->load->library('email');
    }

    public function index()
    {
        $this->mybreadcrumb->add('Scadenzario Chat', '#');
        $this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Scadenzario Chat', 'breadcrumb' => $this->mybreadcrumb->render()]);
        
        $chats = $this->ScadenzarioChatModel->getChats();
        $agenti = $this->Agentimodel->getagenti();
        
        $this->load->view('scadenzario_chat/lista', [
            'chats' => $chats,
            'agenti' => $agenti
        ]);
        $this->load->view('common/footer');
    }

    public function nuova($id_scadenzario)
    {
        // Verifica se l'utente ha i permessi per creare una chat per questo scadenzario
        $this->load->model('Scadenzariomodel');
        $scadenzario = $this->Scadenzariomodel->getscadenzario($id_scadenzario);
        
        if (!$scadenzario) {
            $this->session->set_flashdata(['msg' => 'Scadenzario non trovato', 'msgtype' => 'error']);
            redirect(base_url('scadenzario'));
        }

        // Controllo dei permessi usando il servizio ACL
        $this->load->library('Acl_service');
        if (!$this->acl_service->has_permission('scadenzario_chat', 'crea', $id_scadenzario)) {
            $this->session->set_flashdata(['msg' => 'Non hai i permessi per creare una chat per questo scadenzario', 'msgtype' => 'error']);
            redirect(base_url('scadenzario'));
        }

        // Carica la vista per creare una nuova chat
        $this->mybreadcrumb->add('Scadenzario Chat', base_url('ScadenzarioChat'));
        $this->mybreadcrumb->add('Nuova Chat', '#');
        $this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Nuova Chat', 'breadcrumb' => $this->mybreadcrumb->render()]);
        
        $admins = [];
        if ($this->session->ruolo !== 'Admin') {
            $admins = $this->db->get_where('utenti', ['idruolo' => 1])->result();
        }
        
        $this->load->view('scadenzario_chat/nuova', [
            'id_scadenzario' => $id_scadenzario,
            'scadenzario' => $scadenzario,
            'admins' => $admins
        ]);
        $this->load->view('common/footer');
    }

    public function crea()
    {
        $this->load->model('Scadenzariomodel');
        // Gestisce la creazione effettiva della chat
        if ($this->input->post()) {
            $id_scadenzario = $this->input->post('id_scadenzario');
            $titolo = $this->input->post('titolo');
            $responsabile_predefinito = $this->input->post('responsabile_predefinito');

            // Recupera lo scadenzario
            $scadenzario = $this->Scadenzariomodel->getscadenzario($id_scadenzario);
            if (!$scadenzario) {
                $this->session->set_flashdata([
                    'msg' => 'Scadenzario non trovato',
                    'msgtype' => 'error'
                ]);
                redirect(base_url('scadenzario'));
            }

            // Verifica se esiste già una chat aperta per questo scadenzario
            if ($this->ScadenzarioChatModel->chatEsistentePerScadenzario($scadenzario->conto, $scadenzario->partita, $scadenzario->scadenza)) {
                $this->session->set_flashdata([
                    'msg' => 'Esiste già una chat aperta per questo scadenzario', 
                    'msgtype' => 'error'
                ]);
                redirect(base_url('ScadenzarioChat/nuova/' . $id_scadenzario));
            }

            // Validazione
            $this->load->library('form_validation');
            $this->form_validation->set_rules('titolo', 'Titolo', 'required|max_length[255]');
            
            if ($this->form_validation->run() == FALSE) {
                // Torna indietro con gli errori
                $this->session->set_flashdata(['msg' => validation_errors(), 'msgtype' => 'error']);
                redirect(base_url('ScadenzarioChat/nuova/' . $id_scadenzario));
            }

            // Crea la chat
            $result = $this->ScadenzarioChatModel->creaChat($scadenzario, $titolo, $responsabile_predefinito);

            if ($result) {
                $this->session->set_flashdata(['msg' => 'Chat creata con successo', 'msgtype' => 'success']);
                redirect(base_url('ScadenzarioChat/dettaglio/' . $result));
            } else {
                $this->session->set_flashdata(['msg' => 'Errore nella creazione della chat', 'msgtype' => 'error']);
                redirect(base_url('ScadenzarioChat/nuova/' . $id_scadenzario));
            }
        }

        // Se si cerca di accedere direttamente senza POST
        redirect(base_url('scadenzario'));
    }

    public function dettaglio($id_chat)
    {
        // Carica i dettagli della chat
        $chat = $this->ScadenzarioChatModel->getChatDettaglio($id_chat);
        
        if (!$chat) {
            $this->session->set_flashdata(['msg' => 'Chat non trovata', 'msgtype' => 'error']);
            redirect(base_url('ScadenzarioChat'));
        }

        // Carica i dettagli dello scadenzario
        $this->load->model('Scadenzariomodel');
        $scadenzario = $this->Scadenzariomodel->getscadenzarioByFields($chat->conto, $chat->partita, $chat->scadenza);

        // Carica i messaggi della chat
        $messaggi = $this->ScadenzarioChatModel->getMessaggiChat($id_chat);

        $this->mybreadcrumb->add('Scadenzario', base_url('scadenzario/modifica/' . $scadenzario->id));
        $this->mybreadcrumb->add('Chat', '#');
        $this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Chat', 'breadcrumb' => $this->mybreadcrumb->render()]);
        
        $this->load->view('scadenzario_chat/dettaglio', [
            'chat' => $chat,
            'scadenzario' => $scadenzario,
            'messaggi' => $messaggi
        ]);
        $this->load->view('common/footer');
    }

    public function toggleChatStatus($id_chat)
    {
        // Check if user is admin
        if ($this->session->ruolo !== 'Admin') {
            $this->session->set_flashdata(['msg' => 'Accesso negato', 'msgtype' => 'error']);
            redirect(base_url('ScadenzarioChat'));
        }

        // Toggle chat status
        $this->ScadenzarioChatModel->toggleChatStatus($id_chat);
        
        // Redirect back to chat details
        redirect(base_url('ScadenzarioChat/dettaglio/' . $id_chat));
    }

    public function invia_messaggio()
    {
        if ($this->input->post()) {
            $id_chat = $this->input->post('id_chat');
            $messaggio = $this->input->post('messaggio');

            // Validazione
            $this->load->library('form_validation');
            $this->form_validation->set_rules('messaggio', 'Messaggio', 'required');
            
            if ($this->form_validation->run() == FALSE) {
                $this->session->set_flashdata(['msg' => validation_errors(), 'msgtype' => 'error']);
                redirect(base_url('ScadenzarioChat/dettaglio/' . $id_chat));
            }

            // Invia messaggio
            $result = $this->ScadenzarioChatModel->inviaMessaggio($id_chat, $messaggio);

            if ($result) {
                $this->session->set_flashdata(['msg' => 'Messaggio inviato con successo', 'msgtype' => 'success']);
            } else {
                $this->session->set_flashdata(['msg' => 'Errore nell\'invio del messaggio', 'msgtype' => 'error']);
            }

            redirect(base_url('ScadenzarioChat/dettaglio/' . $id_chat));
        }

        redirect(base_url('ScadenzarioChat'));
    }
}
