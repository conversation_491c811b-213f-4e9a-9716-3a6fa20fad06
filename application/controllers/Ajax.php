<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ajax extends CI_Controller {
    public function __construct() {
		parent::__construct();
		if($this->session->login !== true) redirect(base_url('login'));
		// $this->load->model('Preventivimodel');
		$this->load->model('Prodottimodel');
		// $this->load->model('Utentimodel');
		$this->load->model('Clientimodel');
		$this->load->helper('html');
	}

    public function fetchclienti($tipoout='html') {
		$output = '';
		if ($tipoout=='json') $output=array();
		$query = '';
		$this->load->model('ClientiModel');
		if($this->input->post('query')) {
			$query = $this->input->post('query');
		}
		elseif ($this->input->get_post('term')) {
			$query = $this->input->get_post('term');
		}
		$data = $this->ClientiModel->getClientibyRagSoc($query);
		if(count($data) > 0) {
			if ($tipoout=='html') $output .= $row->ragsoc."<br>";
			elseif ($tipoout=='option') $output .= '<option>Seleziona...</option>';
			elseif ($tipoout=='json') $output['results'][]=array('id'=>'', 'text'=>'---');
			foreach($data as $row) {
				if ($tipoout=='html') $output .= $row->ragsoc."<br>";
				elseif ($tipoout=='option') $output .= '<option>'.$row->ragsoc."</option>";
				elseif ($tipoout=='json') $output['results'][]=array('id'=>$row->idcliente, 'text'=>$row->ragsoc);
			}
		}
		if ($tipoout=='json') $output=json_encode($output);
		echo $output;
	 }

	 public function getlistino()
	 {
		$postData = $this->input->post();
        $listino=$this->Prodottimodel->listino_ajax($postData);
		echo json_encode($listino);
	 }

}

/* End of file Controllername.php */
