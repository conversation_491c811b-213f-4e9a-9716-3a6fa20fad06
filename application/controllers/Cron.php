<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Cron extends CI_Controller
{
	function __construct()
	{
		parent::__construct();
		if (!empty($this->session->idutente)) {
			echo "Utente loggato. Usare altro browser senza fare login.";
			exit();
		}
	}

	public function index()
	{
	}

	/**
	 * 22/5/2023: non si usa più perchè per qualche motivo chiamandolo da crontab,
	 * si ferma dopo la prima chiamata.
	 * Cron attuale:
	 * 10 4 * * *      /usr/bin/php7.4 /var/www/vhosts/agenti.luccacarta.it/index.php cron syncCodiciCliente
	 * 15 4 * * *      /usr/bin/php7.4 /var/www/vhosts/agenti.luccacarta.it/index.php cron syncListini
	 * 20 4 * * *      /usr/bin/php7.4 /var/www/vhosts/agenti.luccacarta.it/index.php cron syncOrdini
	 * 25 4 * * *      /usr/bin/php7.4 /var/www/vhosts/agenti.luccacarta.it/index.php cron ordini_aggiorna_ERP_gg
	 * 30 4 * * *      /usr/bin/php7.4 /var/www/vhosts/agenti.luccacarta.it/index.php cron importOrdiniJG_gg
	 * #20 1 * * *     /usr/bin/php7.4 /var/www/vhosts/agenti.luccacarta.it/index.php cron sequenzaAggiornamento | mail -s "Luccacarta sync" <EMAIL>
	 * 10 5 * * *      /usr/bin/php7.4 /var/www/vhosts/agenti.luccacarta.it/index.php cron importScadenzrioJG	 *
	 */
	public function sequenzaAggiornamento($gg = 7)
	{
		$date = new DateTime();
		$date->modify("-$gg days");
		$data_inizio = $date->format('Ymd');
		$data_fine = date('Ymd');

		echo "syncCodiciCliente\n";
		$this->syncCodiciCliente();
		echo "syncListini\n";
		$this->syncListini();
		echo "syncOrdini\n";
		$this->syncOrdini($gg);
		echo "ordini_aggiorna_ERP\n";
		$this->ordini_aggiorna_ERP($data_inizio, $data_fine, 0);
		echo "importOrdiniJG\n";
		$this->importOrdiniJG($data_inizio, $data_fine, 0);
	}

	/**
	 * Sincronizza gli ordini degli agenti per eventuali modifiche fatte su JG da Luccacarta
	 *
	 * @param int $gg giorni
	 * @return void
	 */
	public function syncOrdini($gg = 2)
	{
		$this->load->model('AS400/ASOrdiniModel');
		$this->load->model('Ordinimodel');

		$this->sincronizzaOrdini($gg);
	}

	/**
	 * Aaggiorna gli ordini inviati con le eventuali modifiche fatte su JG
	 *
	 *
	 * @param int $gg giorni
	 * @return array obj
	 */
	private function sincronizzaOrdini($gg)
	{
		$ordini = $this->Ordinimodel->getOrdiniRecentiInviati($gg);
		$out = "sincronizzaOrdini\n";
		foreach ($ordini as $o) {
			$ordineJG = $this->ASOrdiniModel->getOrdineJG($o->TDOCOO . $o->NROROO);
			if ($ordineJG->NROROO != '') {
				$out .= "idordine: " . $o->idordine . "\n";
				$this->Ordinimodel->aggiorna_prodottiordinati($o, $ordineJG->rigaordine);
			}
		}
		if (is_cli()) echo eliminatags($out);
		else echo $out;
	}

	public function sincronizzaEvasioneOrdini($gg=2) {
		$this->load->model('AS400/ASBolleFattureModel');
		$this->load->model('Ordinimodel');
		$ordini = $this->Ordinimodel->getOrdiniRecentiInviati($gg);
		$count = 0;
		foreach ($ordini as $o) {
			echo "idordine: " . $o->idordine . "<br>\n";
			if ($count >= 5) {
				break;
			}
			$bolla = $this->ASBolleFattureModel->getRigheByOrdineERP($o->NROROO);
			if ($bolla) {
				// $this->Ordinimodel->aggiorna_evasione($o, $bolla);
				// var_dump($bolla);
				// prendo le righe dell'ordine
				$prodotti = $this->Ordinimodel->getordiniprodotti($o->idordine);
				// per ogni riga, cerco se esiste una riga in bolla con lo stesso codice
				foreach ($prodotti as $keyp=>$p) {
					foreach ($bolla as $key=>$b) {
						if (trim($b->CDARFM)!='') {
							if ((trim($b->CDARFM) == $p->codice) && (trim($b->DSARFM==$p->descrizione))) {
								echo "codice: " . $b->CDARFM . " descrizione: " . $b->DSARFM . "<br>\n";
								// esiste, aggiorna campo evaso: evaso += qta
								$this->Ordinimodel->aggiorna_evasione($p->id, $b->QTFTFM);
								// rimuovo riga dalla bolla
								unset($bolla[$key]);
								unset($prodotti[$keyp]);
								echo "id riga: ".$p->id." Aggiornato prodotto: " . $p->codice . "<br>\n";
							}
						}
						else {
							unset($bolla[$key]);
						}
					}
				}
				if (count($bolla)>0) {
					echo "<p>Prodotti in bolla non corrispondenti in ordine: ";
					var_dump($bolla);
					var_dump($prodotti);
					exit;
				}

				// se esiste, aggiorna campo evaso: evaso += qta
				// insrisce riga in tabella evasioni con TDOCFM, NRDFFM
			}
			$count++;
		}
	}



	/**
	 * Svuota tabella listino e la ripopola da JG
	 *
	 * @return void
	 */
	public function syncListini()
	{
		$this->load->model('AS400/ASProdottiModel');
		$this->db->truncate('listino');
		$start = 0;
		$limit = 100;
		do {
			$a = $this->ASProdottiModel->getListini($start, $limit);
			$set = array();
			foreach ($a as $v) {
				$set[] = [
					'codice' => trim($v->CDARML),
					'descrizione' => trim($v->DESAAT),
					'prezzo' => trim($v->PRZLML),
					'um' => trim($v->UMBASE),
					'tags' => trim($v->DSC3AT),
				];
				// echo $v->CDARML.' '.$v->DESAAT.'<br>';
			}
			if (count($set) > 0) $this->db->insert_batch('listino', $set);
			// var_dump($a);

			$start += $limit;
		} while (count($a) > 0);
		touch('./upload/listino.xls');
		// 6/6/22: vengono sospesi i prodotti ordinati non a listino abbiamo deciso di renderli ordinabili lo stesso ma cambiando il codice a 00 001 durante la compilazione dell'ordine
		$this->prodottiSospesi();
	}

	/**
	 * Scorre tutti gli articoli a listino (escude 00 001) ordinati dai clienti e vede se c'è presenza nel listino
	 * se non c'è marca il prodotto come sospeso/annullato
	 * se c'è, toglie eventuale flag sospeso/annullato
	 * richiamata dopo la syncListini
	 *
	 * @return void
	 */
	public function prodottiSospesi()
	{
		$this->db->select('codice, datasospeso');
		$this->db->from('ordiniprodotti');
		$this->db->where('codice!=', '00 001');
		$this->db->distinct();
		$rs = $this->db->get();
		$rows = $rs->result();
		foreach ($rows as $prod) {
			if ($prod->codice != '') {
				$this->db->select('count(*) as num');
				$this->db->from('listino');
				$this->db->where('codice', $prod->codice);
				$rs = $this->db->get();
				$num = $rs->row()->num;
				if (($num == 0) && (is_null($prod->datasospeso))) {
					echo $prod->codice . " sospeso <br>";
					$this->db->update('ordiniprodotti', ['datasospeso' => date('Y-m-d')], ['codice' => $prod->codice]);
				} elseif ($num == 1 && !is_null($prod->datasospeso)) {
					echo $prod->codice . " riattivato <br>";
					$this->db->update('ordiniprodotti', ['datasospeso' => null], ['codice' => $prod->codice]);
				}
			}
		}
	}
	/**
	 * Cerca i clienti che non hanno codice poi con la piva o cf
	 * cerca su JG e se lo trova aggiorna codice cliente su portale
	 *
	 * @return void
	 */
	public function syncCodiciCliente()
	{
		$this->load->model('Clientimodel');
		$this->load->model('AS400/ASClientiModel');

		$clienti = $this->Clientimodel->getclientisenzacodice();
		$out = 'syncCodiciCliente\n';
		foreach ($clienti as $v) {
			$clientejg = $this->ASClientiModel->getCliente_piva($v->piva);
			if (!$clientejg) {
				$clientejg = $this->ASClientiModel->getCliente_cf($v->cf);
			}

			if (!$clientejg) {
				continue;
			}

			$codice = substr($clientejg->CDCFKP, -5);
			$out .= ($v->piva ? 'piva: ' . $v->piva : 'cf: ' . $v->cf) . ' -> codice: ' . $codice . "\n";
			$this->Clientimodel->aggiornacliente($v->idcliente, ['codice' => $codice]);
		}
		if (is_cli()) echo eliminatags($out);
		else echo $out;
	}

	/**
	 * Importa scadenzario da JG
	 * viene eseguito da crontab ogni notte
	 *
	 * @return void
	 *
	 * svuota scadenzario che hanno campo fonte JG, quelli con fonte LC sono caricati da LC a mano
	 *
	 */
	public function importScadenzrioJG()
	{
		$stop = 0; // quanti blocchi di $limit prendo da JG, solo per sviluppo. 0=tutti
		$this->db->delete('scadenzario', ['fonte' => 'JG']);
		$limit = 100;
		$start = 0;
		$i = 0;
		$this->load->model('AS400/ASScadenzarioModel');
		do {
			$s = $this->ASScadenzarioModel->getScadenzario($start, $limit);
			$insert = array();
			foreach ($s as $row) {
				$insert[] = array(
					'agente' => $row->CDAGSA,
					'conto' => substr($row->CDCFSA, 5),
					'ragsoc' => $row->DSCOSA,
					'anno' => $row->ANNO,
					'partita' => $row->NRFTSA,
					'scadenza' => $row->DTSCSA,
					'rata' => $row->DSRASA,
					'importo' => $row->TOFASA,
					'data' => $row->DTFTSA,
					'importoscaduto' => $row->IMSCADUTO,
					'importoascadere' => $row->IMSCSA,
					'fonte' => 'JG',
				);
			}
			if (count($insert) > 0) $this->db->insert_batch('scadenzario', $insert);

			$start += $limit;
			$i++;
		} while ((count($s) > 0) && (($i < $stop) || $stop == 0));
		echo "fatto";
	}

	/**
	 * Funzione per richiamare ordini_aggiorna_ERP con 1 solo parametro $gg
	 *
	 * @param integer $gg
	 * @return void
	 */
	public function ordini_aggiorna_ERP_gg($gg = 7)
	{
		$date = new DateTime();
		$date->modify("-$gg days");
		$data_inizio = $date->format('Ymd');
		$data_fine = date('Ymd');
		$this->ordini_aggiorna_ERP($data_inizio, $data_fine, 0);
	}

	/**
	 * Aggiorna i campi TDOCOO e NROROO degli ordini che li hanno vuoti
	 * Considera tutti gli ordini a prescindere che siano stati inviati o meno
	 * limite<=0 processa tutti gli ordini
	 *
	 */
	public function ordini_aggiorna_ERP($data_inizio = '', $data_fine = '', $limite = '')
	{
		$this->load->model('AS400/ASOrdiniModel');

		$data_inizio = (empty($data_inizio) && !empty($this->input->get('data_inizio'))) ? $this->input->get('data_inizio') : $data_inizio;
		$data_fine = (empty($data_fine) && !empty($this->input->get('data_fine'))) ? $this->input->get('data_fine') : $data_fine;
		$limite = (empty($limite) && !empty($this->input->get('limite'))) ? $this->input->get('limite') : $limite;

		$data_inizio = dataYmd_mysql($data_inizio);
		$data_fine = dataYmd_mysql($data_fine);

		if (!empty($data_inizio)) {
			if (empty($data_fine)) {
				$data_fine = date('Y-m-d 23:59');
			}
		}

		$this->db->select('O.*');
		$this->db->from('ordini O');

		$this->db->where('O.TDOCOO IS NULL');
		$this->db->where('O.NROROO IS NULL');

		if (!empty($data_inizio) && !empty($data_fine)) {
			$this->db->where("O.dataordine BETWEEN '$data_inizio' AND '$data_fine'");
		}


		if ($limite > 0) {
			$this->db->limit($limite);
		}


		//order
		$this->db->order_by('O.dataordine', 'DESC');

		$rs = $this->db->get();
		$ordini_portale = $rs->result();

		// print sql string
		// echo $this->db->last_query();

		foreach ($ordini_portale as $ordine) {
			$oct = $this->ASOrdiniModel->getCarrelloTestata($ordine->idordine);
			if ($oct->DOC_ERP != '') { // ordine inviato al carrello
				echo $ordine->idordine;
				$ordineJG = $this->ASOrdiniModel->getOrdineJG($oct->DOC_ERP);
				if (!empty($ordineJG->NROROO)) { // il carrello è passato come ordine effettivo
					$update = array();
					echo ":$oct->DOC_ERP";
					if (empty($ordine->datainviato)) {
						$update['datainviato'] = dataYmd_mysql($ordineJG->dataordine);
					}
					// update ordini
					$update['TDOCOO'] = $ordineJG->TDOCOO;
					$update['NROROO'] = $ordineJG->NROROO;
					$this->db->update('ordini', $update, ['idordine' => $ordine->idordine]);
				}
				echo  "\n";
			}
		}
	}

	/**
	 * Funzione per richiamare ordini_aggiorna_ERP con 1 solo parametro $gg
	 *
	 * @param integer $gg
	 * @return void
	 */
	public function importOrdiniJG_gg($gg = 7)
	{
		$date = new DateTime();
		$date->modify("-$gg days");
		$data_inizio = $date->format('Ymd');
		$data_fine = date('Ymd');
		$this->importOrdiniJG($data_inizio, $data_fine, 0);
	}

	/**
	 * Importa gli ordini da JG che non sono presenti nel portale
	 * @param string $data_inizio
	 * @param string $data_fine
	 * @param int $limite max numero record, 0 per tutti
	 */
	public function importOrdiniJG($data_inizio = '', $data_fine = '', $limite = '')
	{
		$this->load->model('AS400/ASOrdiniModel');
		$this->load->model('Ordinimodel');
		$this->load->model('Clientimodel');
		$this->load->model('Prodottimodel');
		$this->load->model('Agentimodel');
		$this->load->model('Utentimodel');

		$data_inizio = (empty($data_inizio) && !empty($this->input->get('data_inizio'))) ? $this->input->get('data_inizio') : $data_inizio;
		$data_fine = (empty($data_fine) && !empty($this->input->get('data_fine'))) ? $this->input->get('data_fine') : $data_fine;
		$limite = (empty($limite) && !empty($this->input->get('limite'))) ? $this->input->get('limite') : $limite;



		if ($data_inizio != '') {
			if ($data_fine == '') {
				$data_fine = date('Ymd');
			}
		}

		// prendiamo le testate degli ordini
		$testateOrdiniJG = $this->ASOrdiniModel->getTestateOrdiniJG($data_inizio, $data_fine, $limite);
		// var_dump($testateOrdiniJG);
		foreach ($testateOrdiniJG as $testataOrdineJG) {
			if ($testataOrdineJG->TDOCOO == null || $testataOrdineJG->NROROO == null) {
				var_dump($testataOrdineJG);
				exit;
			}
			$codice_erp = $testataOrdineJG->TDOCOO . $testataOrdineJG->NROROO;

			$ordine = $this->Ordinimodel->getordine_erp($codice_erp);
			if (!$ordine) {
				$ordineJG = $this->ASOrdiniModel->getOrdineJG($codice_erp);
				// var_dump($ordineJG);

				$codice_cliente = substr($testataOrdineJG->CDC1OO, -5);
				$cliente = $this->Clientimodel->getclientebycodice($codice_cliente);
				if ($cliente) {
					$agente = $this->Agentimodel->getagentebycodice($testataOrdineJG->CDAGOO);
					$clientejson = $this->Clientimodel->getclientejson($cliente->idcliente);

					$data['idcliente'] = $cliente->idcliente;
					$data['noteagente'] = $ordineJG->noteagente;
					$data['idagente'] = $agente->idutente;
					$data['cliente'] = $clientejson;
					$data['dataordine'] = dataYmd_mysql($testataOrdineJG->DT01OO);
					$data['datainviato'] = dataYmd_mysql($testataOrdineJG->DT01OO);
					$data['TDOCOO'] = $testataOrdineJG->TDOCOO;
					$data['NROROO'] = $testataOrdineJG->NROROO;
					$key = 0;
					foreach ($ordineJG->rigaordine as $rigaordine) {
						if ($rigaordine->TIMOOO != '06') { // escludo i commenti
							$data['codice'][$key] = trim($rigaordine->CDAROO);
							$data['descrizione'][$key] = $rigaordine->DSAROO;
							$data['prezzo'][$key] = floatval($rigaordine->PZNEOO);
							$data['prezzoordine'][$key] = floatval($rigaordine->PRZUOO);
							$data['sconto'][$key] = floatval($rigaordine->SCN1OO);
							$data['quantita'][$key] = $rigaordine->QTOROO;
							$data['um'][$key] = $rigaordine->CDUMOO;
							$data['note'][$key] = $this->ASOrdiniModel->getNoteArticolo($testataOrdineJG->TDOCOO, $testataOrdineJG->NROROO, $rigaordine->NRRGOO); //@todo dove sono le note prodotto in JG??
							$data['tiporiga'][$key] = $rigaordine->TIMOOO;

							$key++;
						}
					}
					$this->Ordinimodel->salvaordinenuovo($data);
					// $this->Ordinimodel->salvaprodottiordine($data, 1);
				} else { // cliente non trovato
					//@todo cercare cliente in JG e inserirlo nel portale, assegnato all'agente
					echo "Cliente non trovato $codice_cliente, ordine erp: $codice_erp <br>\n";
					// exit;
				}
			}
		}
	}

	/**
	 * Sincronizza Bolle/Fatture di oggi e ieri
	 * Aggiorna ordini web con dati delle bolle/fatture
	 * Inserisce nuovi ordini web da bolle/fatture create direttamente su JG
	 */
	public function syncBF()
	{
		// Caricamento dei modelli necessari
		$this->load->model('AS400/ASBolleFattureModel');
		$this->load->model('AS400/ASOrdiniModel');
		$this->load->model('Ordinimodel');
		$this->load->model('Clientimodel');
		$this->load->model('Agentimodel');
		$this->load->model('Utentimodel');
		$this->load->model('Prodottimodel');

		// Definizione dell'intervallo di date per la sincronizzazione
		$dataoggi = date('Ymd');
		$dataieri = date('Ymd', strtotime('-1 day'));

		// Recupero delle testate bolle/fatture da JG
		$t = $this->ASBolleFattureModel->getTestate(['dataInizio' => $dataieri, 'dataFine' => $dataoggi]);

		// Inizializzazione array per bolle/fatture e ordini web
		$bf = array(); // Conterrà tutte le bolle/fatture
		$ordini_webJG = []; // Conterrà solo le bolle/fatture relative a ordini web

		// Elaborazione delle testate e separazione bolle/fatture
		foreach ($t as $key => $testata) {
			// Recupero dettagli per ogni testata
			$r = $this->ASBolleFattureModel->getRighe($testata->CODICEDITTA, $testata->TIPODOCUMENTO, $testata->NUMERODOCUMENTOFATTURAZ);
			if (count($r) > 0) {
				$bf[$key] = array('testata' => $testata, 'righe' => $r);
				foreach ($r as $riga) {
					if ($riga->TPORFM == 'W') {
						// Identificazione ordini web
						$doc_erp = 'W' . $riga->NRORFM;
						$ordini_webJG[$doc_erp] = $bf[$key];
						unset($bf[$key]); // Rimozione da $bf per mantenere solo bolle/fatture non web
						break;
					}
				}
			}
		}

		// Aggiornamento ordini web esistenti
		echo "Aggiorna ordini portale da JG bolle/fatture\n";
		foreach ($ordini_webJG as $doc_erp => $ordineJG) {
			$ordine = $this->Ordinimodel->getordine_erp($doc_erp);
			if ($ordine) {
				echo "$doc_erp " . $ordine->idordine . "\n";
				// Aggiornamento prodotti ordinati
				$this->Ordinimodel->aggiorna_prodottiordinati($ordine, $ordineJG['righe']);
				// Aggiornamento tipo documento per evitare future sincronizzazioni
				$this->Ordinimodel->updateOrdine($ordine->idordine, ['tipodocumento' => $ordineJG['testata']->TIPODOCUMENTO]);
			} else {
				echo "Ordine web mancante per $doc_erp\n";
			}
		}

		// Inserimento nuovi ordini web da bolle/fatture JG
		echo "Aggiunge bf come ordini\n";
		foreach ($bf as $bollafattura) {
			// Verifica esistenza ordine
			$ordine = $this->Ordinimodel->getordine_erp($bollafattura['testata']->TIPODOCUMENTO . $bollafattura['testata']->NUMERODOCUMENTOFATTURAZ);
			if ($ordine) continue; // Skip se l'ordine esiste già

			echo "Aggiungo " . $bollafattura['testata']->TIPODOCUMENTO . $bollafattura['testata']->NUMERODOCUMENTOFATTURAZ . "\n";

			// Recupero dati cliente e agente
			$cliente = $this->Clientimodel->getclientebycodice(substr($bollafattura['testata']->CODICECLIENTE, 5));
			$agente = $this->Agentimodel->getagentebycodice(trim($bollafattura['testata']->CODICEAGENTE));

			// Controlli di sicurezza
			if (!$cliente || !$agente) {
				echo "Cliente o Agente non trovato\n";
				continue;
			}

			// Preparazione dati per inserimento nuovo ordine
			$insert = $this->prepareInsertData($bollafattura, $cliente, $agente);

			// Inserimento nuovo ordine
			$this->Ordinimodel->salvaordinenuovo($insert);
		}
	}

	/**
	 * Prepara i dati per l'inserimento di un nuovo ordine
	 * @param array $bollafattura Dati della bolla/fattura
	 * @param object $cliente Dati del cliente
	 * @param object $agente Dati dell'agente
	 * @return array Dati formattati per l'inserimento
	 */
	private function prepareInsertData($bollafattura, $cliente, $agente)
	{
		$insert = [];
		$insert['dataordine'] = $dataordine;
		$insert['datainviato'] = $dataordine;
		$insert['idcliente'] = $cliente->idcliente;
		$insert['noteagente'] = '';
		$insert['idagente'] = $agente->idutente;
		$insert['tipodocumento'] = $bollafattura['testata']->TIPODOCUMENTO;
		$insert['TDOCOO'] = $bollafattura['testata']->TIPODOCUMENTO;
		$insert['NROROO'] = $bollafattura['testata']->NUMERODOCUMENTOFATTURAZ;

		$insert['tiporiga'] = array();
		$insert['codice'] = array();
		$insert['descrizione'] = array();
		$insert['prezzoordine'] = array();
		$insert['prezzolistino'] = array();
		$insert['sconto'] = array();
		$insert['prezzo'] = array();
		$insert['quantita'] = array();
		$insert['note'] = array();
		$insert['um'] = array();
		return $insert;
	}
}

/* End of file Cron.php */
