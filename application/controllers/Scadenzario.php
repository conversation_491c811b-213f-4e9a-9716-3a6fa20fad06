<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Scadenzario extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		if ($this->session->login !== true) redirect(base_url('login'));
		// if ($this->session->userdata('ruolo')!='Admin') redirect(base_url($this->session->userdata('ruolo')));

		$this->load->model('Scadenzariomodel');
		$this->load->model('Agentimodel');
		$this->load->model('Utentimodel');
		$this->load->helper('html');
	}
	public function index($idcliente = '')
	{
		$this->mybreadcrumb->add('Scadenzario', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => '', 'breadcrumb' => '']);
		$sc = $this->Scadenzariomodel->getscadenzari($idcliente);

		$this->load->view('scadenzario/lista', ['sc' => $sc]);
		$this->load->view('common/footer');
	}

	public function caricaexcel()
	{
		if ($this->session->userdata('ruolo') != 'Admin') redirect(base_url($this->session->userdata('ruolo')));
		$this->mybreadcrumb->add('Scadenzario', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Scadenzario', 'breadcrumb' => $this->mybreadcrumb->render()]);
		if ($this->input->post('cmd') == 'Carica') {
			$config['overwrite']          = true;
			$config['upload_path']          = './upload/';
			$config['allowed_types']        = 'xlsx';
			$config['max_size']             = 350000;
			$config['file_name']			= 'scadenzario';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('fileutente')) {
				echo $this->upload->display_errors();
				var_dump($this->upload->data());
			} else {
				$filename = $this->upload->data()['file_name'];
				$out = heading("$filename caricato", 3);
				foreach ($this->upload->data() as $key => $value) {
					$out .= "$key: $value <br>";
				}
				if ($this->upload->data()['file_size'] > 500) $out .= "<p><strong>Controlla il file potrebbe essere non ottimizzato</strong>";
				$this->load->view('common/body', array('testo' => $out));
				// var_dump($this->upload->data());
			}
		} else {
			$this->load->view('scadenzario/caricaexcelform');
		}
		$this->load->view('common/footer');
	}
	public function scadenzario2db()
	{
		if ($this->session->userdata('ruolo') != 'Admin') redirect(base_url($this->session->userdata('ruolo')));
		$this->mybreadcrumb->add('Scadenzario', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Scadenzario', 'breadcrumb' => $this->mybreadcrumb->render()]);
		// $arr=$this->Scadenzariomodel->checkxls();
		// var_dump($arr);
		$out = '';
		if (true || count($arr['campinodb']) == 0) {
			$totale = $this->Scadenzariomodel->aggiornascadenzario();
			$out = 'Totale aggiunti: ' . $totale . '<br>';
		} else {
			$out = 'I campi del file non corrispondono<br>Campi mancanti nel file:<br>';
			$out .= implode('<br>', $arr['campinodb']);
		}
		$this->load->view('common/body', array('testo' => $out));
		$this->load->view('common/footer');
	}

	public function upload_fattura_form($id)
	{
		// if ($this->session->userdata('ruolo') != 'Admin') {
		// 	$this->session->set_flashdata(['msg' => 'Non hai i permessi per accedere a questa funzione', 'msgtype' => 'error']);
		// 	redirect(base_url('scadenzario/modifica/' . $id));
		// }
		$this->mybreadcrumb->add('Scadenzario', base_url('scadenzario'));
		$this->mybreadcrumb->add('Modifica', base_url('scadenzario/modifica/' . $id));
		$this->mybreadcrumb->add('Carica Fattura', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Carica Fattura', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$sc = $this->Scadenzariomodel->getscadenzario($id);
		$this->load->view('scadenzario/upload_fattura_form', ['sc' => $sc]);
		$this->load->view('common/footer');
	}

	public function delete_pdf($id)
	{
		// if ($this->session->userdata('ruolo') != 'Admin') {
		// 	$this->session->set_flashdata(['msg' => 'Non hai i permessi per accedere a questa funzione', 'msgtype' => 'error']);
		// 	redirect(base_url('scadenzario/modifica/' . $id));
		// }

		$scadenzario = $this->Scadenzariomodel->getscadenzario($id);
		if (!$scadenzario) {
			$this->session->set_flashdata(['msg' => 'Scadenzario non trovato', 'msgtype' => 'error']);
			redirect(base_url('scadenzario'));
		}

		$upload_path = FCPATH . 'upload/scadenzario/fatture/' . $scadenzario->anno . '/' . $scadenzario->conto . '/' . $scadenzario->id . '/';
		$file_path = $upload_path . 'fattura.pdf';

		if (file_exists($file_path)) {
			unlink($file_path);
			rmdir($upload_path);
			$this->session->set_flashdata(['msg' => 'PDF e cartella eliminati con successo', 'msgtype' => 'success']);
		} else {
			$this->session->set_flashdata(['msg' => 'PDF non trovato', 'msgtype' => 'error']);
		}

		redirect(base_url('scadenzario/modifica/' . $id));
	}

	public function upload_fattura($id)
	{
		// if ($this->session->userdata('ruolo') != 'Admin') {
		// 	$this->session->set_flashdata(['msg' => 'Non hai i permessi per accedere a questa funzione', 'msgtype' => 'error']);
		// 	redirect(base_url('scadenzario/modifica/' . $id));
		// }

		$scadenzario = $this->Scadenzariomodel->getscadenzario($id);
		if (!$scadenzario) {
			$this->session->set_flashdata(['msg' => 'Scadenzario non trovato', 'msgtype' => 'error']);
			redirect(base_url('scadenzario'));
		}

		$anno = $scadenzario->anno;
		$codice_cliente = $scadenzario->conto;
		$id_scadenzario = $scadenzario->id;

		$upload_path = FCPATH . 'upload/scadenzario/fatture/' . $anno . '/' . $codice_cliente . '/' . $id_scadenzario . '/';
		$file_path = $upload_path . 'fattura.pdf';

		// Check if file exists and overwrite is not selected
		if (file_exists($file_path) && !$this->input->post('overwrite')) {
			$this->session->set_flashdata(['msg' => 'Un file PDF esiste già. Seleziona "Sovrascrivi il file esistente" per sostituirlo.', 'msgtype' => 'error']);
			redirect(base_url('scadenzario/modifica/' . $id));
		}

		$created = true;
		if (!is_dir($upload_path)) {
			$created = mkdir($upload_path, 0775, true);
		}
		if (is_dir($upload_path)) {
			$config['upload_path'] = $upload_path;
			$config['allowed_types'] = 'pdf';
			$config['max_size'] = 2048; // 2MB
			$config['overwrite'] = TRUE; // Always overwrite if checkbox is checked
			$config['file_name'] = 'fattura.pdf';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('fattura')) {
				$error = array('error' => $this->upload->display_errors());
				$this->session->set_flashdata(['msg' => 'Errore durante l\'upload del file: ' . $error['error'], 'msgtype' => 'error']);
			} else {
				$data = array('upload_data' => $this->upload->data());
				$this->session->set_flashdata(['msg' => 'Fattura caricata con successo!', 'msgtype' => 'success']);
			}
		}
		else {
			$this->session->set_flashdata(['msg' => 'Errore durante la creazione della cartella', 'msgtype' => 'error']);
		}

		redirect(base_url('scadenzario/modifica/' . $id));
	}

	public function modifica($id)
	{
		$this->load->library('Acl_service');
		
		$acl = $this->acl_service->check_scadenzario_permission($id);
		
		if ($acl->modifica) {
			// Check if a chat already exists for this scadenzario
			$this->load->model('ScadenzarioChatModel');
			$scadenzario = $this->Scadenzariomodel->getscadenzario($id);
			$existing_chat = $this->ScadenzarioChatModel->getChatByScadenzario($scadenzario->conto, $scadenzario->partita, $scadenzario->scadenza);

			// Load notifications for this scadenzario
			$this->load->model('Notifichemodel');
			$all_notifiche = $this->Notifichemodel->get_notifiche();
			$scadenzario = $this->Scadenzariomodel->getscadenzario($id);
			$notifiche = array_filter($all_notifiche, function($n) use ($scadenzario) {
				return $n->conto == $scadenzario->conto && 
				       $n->partita == $scadenzario->partita && 
				       $n->scadenza == $scadenzario->scadenza;
			});

			$this->mybreadcrumb->add('Scadenzario', '#');
			$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Scadenzario', 'breadcrumb' => $this->mybreadcrumb->render()]);
			$sc = $this->Scadenzariomodel->getscadenzario($id);

			$this->load->view('scadenzario/modifica', [
				'sc' => $sc, 
				'nobtns' => false, 
				'existing_chat' => $existing_chat,
				'notifiche' => $notifiche
			]);
			$this->load->view('common/footer');
		} else {
			$this->session->set_flashdata(['msg' => 'Non è possibile modificare questo elemento', 'msgtype' => 'error']);
			redirect(base_url('scadenzario'));
		}
	}

	public function pagato($id)
	{
		$this->Scadenzariomodel->setpagato($id);
		
		$this->load->model('ScadenzarioChatModel');
		$chat = $this->ScadenzarioChatModel->getChatByScadenzario($id);
		if ($chat && $chat->chiusa == 0) {
			$this->ScadenzarioChatModel->toggleChatStatus($chat->id);
		}
		
		$this->session->set_flashdata(['msg' => 'Registrata riscossione', 'msgtype' => 'info']);
		$this->notificapagato($id);
		redirect(base_url('scadenzario'));
	}

	public function notificapagato($id)
	{
		$sc = $this->Scadenzariomodel->getscadenzario($id);
		$agente = $this->Agentimodel->getagente($this->session->idutente);

		$schedahtml = $this->load->view('scadenzario/notificapagato_mail', [
			'sc' => $sc,
			'agente' => $agente,
		], true);

		// print_r($schedahtml); exit;

		$this->load->library('email');
		$config['mailtype'] = 'html';
		$this->email->initialize($config);

		if (ENVIRONMENT === 'development') {
		    $this->email->from('<EMAIL>', 'Notifiche Scadenzario');
		    $this->email->to('<EMAIL>');
		} else {
		    $this->email->from($this->config->item('mailfrom'), 'Agente ' . $agente->nome . ' ' . $agente->cognome);
		    $this->email->reply_to($agente->email);
		    $this->email->to('<EMAIL>');
		}

		$this->email->subject('Partita #' . $sc->partita . ' ' . $sc->ragsoc);
		$this->email->message($schedahtml);

		if ($this->email->send()) {
			// $this->db->update('ordini', ['datainviato'=>date('Y-m-d H:i:s')], ['idordine'=>$idordine]);
			$this->session->set_flashdata(['msg' => 'Messaggio riscossione inviato a Luccacarta', 'msgtype' => 'info']);
		} else {
			$this->session->set_flashdata(['msg' => 'Errore nell\'invio del messaggio riscossione a Luccacarta. Riprova o contatta l\'amministratore.', 'msgtype' => 'danger']);
		}

		redirect(base_url('scadenzario'));
	}

	public function allClientsToPdf($idagente = '')
	{
		$this->load->model('Clientimodel');
		$this->load->model('Scadenzariomodel');

		// Recupera i clienti dell'agente
		$clienti = $this->Clientimodel->getclienti($idagente);
		$agente = $this->Agentimodel->getagente($this->session->idutente);

		$this->load->library('mypdf');
		$pdf = new MYPDF('P');

		foreach ($clienti as $cliente) {
			// Recupera lo scadenzario del cliente
			$scadenzario = $this->Scadenzariomodel->getScadenzarioByCliente($cliente->codice);

			// Genera il PDF per lo scadenzario del cliente
			$html_header = $this->load->view('scadenzario/scadenzario_pdf_header', ['cliente' => $cliente, 'sc' => $scadenzario], true);
			$html = $this->load->view('scadenzario/scadenzario_pdf', ['sc' => $scadenzario, 'agente' => $agente, 'cliente' => $cliente], true);

			$pdf->SetMargins(5, 50, 5);
			$pdf->setHeaderData($ln = '', $lw = 0, $ht = '', $hs = $html_header, $tc = array(0, 0, 0), $lc = array(0, 0, 0));
			$pdf->setFooterData('footer', 0, '', '', array(0, 0, 0), array(0, 0, 0));
			$pdf->AddPage();
			$pdf->writeHTML($html, true, false, true, false, '');
		}

		$pdf->Output('Scadenzario_Clienti.pdf', 'I');
	}



	public function cliente2pdf($codice, $out = "pdf")
	{
		$this->load->model('Clientimodel');

		$sc = $this->Scadenzariomodel->getscadenzari($codice);
		$agente = $this->Agentimodel->getagentebycodice(trim($sc[0]->agente));
		$cliente = $this->Clientimodel->getclientebycodice($codice);
		if (!$cliente) {
			$cliente = new stdClass;
			$cliente->ragsoc = $sc[0]->ragsoc;
			$cliente->indirizzo = '';
			$cliente->cap = '';
			$cliente->citta = '';
			$cliente->piva = '';
			$cliente->email = '';
			$cliente->telefono = '';
			$cliente->codice = $codice;
		}

		if ($out == 'scheda') {
			$this->mybreadcrumb->add('Scadenzario', '#');
			$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => '', 'breadcrumb' => '']);

			$this->load->view('scadenzario/scadenzario_pdf_header', [
				'cliente' => $cliente,
				'sc' => $sc,
			]);
			$this->load->view('scadenzario/scadenzario_pdf', [
				'sc' => $sc,
				'agente' => $agente,
				'cliente' => $cliente,
			]);
			$this->load->view('common/footer');
		} else {
			$html_header = $this->load->view('scadenzario/scadenzario_pdf_header', [
				'cliente' => $cliente,
				'sc' => $sc,
			], true);
			$html = $this->load->view('scadenzario/scadenzario_pdf', [
				'sc' => $sc,
				'agente' => $agente,
				'cliente' => $cliente,
			], true);
			$this->load->library('mypdf');
			$pdf = new MYPDF('P');
			$pdf->SetMargins(5, 50, 5);
			$pdf->setHeaderData($ln = '', $lw = 0, $ht = '', $hs = $html_header, $tc = array(0, 0, 0), $lc = array(0, 0, 0));
			$pdf->setFooterData('footer', 0, '', '', array(0, 0, 0), array(0, 0, 0));
			$pdf->AddPage();
			$pdf->writeHTML($html, true, false, true, false, '');
			// Sanitizza il nome del file rimuovendo caratteri non validi per il filesystem
			$filename = 'Scadenzario_'.preg_replace('/[\/\\\:*?"<>|]/', '_', $cliente->ragsoc);
			$pdf->Output($filename . '.pdf', 'I');
		}
	}

	public function acl_scadenziario($id)
	{
		$this->load->library('Acl_service');
		return $this->acl_service->check_scadenzario_permission($id);
	}

	/**
	 * Importa scadenzario da JG
	 *
	 * @return void
	 *
	 * svuota scadenzario che hanno campo fonte JG, quelli con fonte LC sono caricati da LC a mano
	 *
	 */
	public function importJG()
	{
		redirect(base_url('cron/importJG'));
	}
}

/* End of file Scadenzario.php */
/* Location: ./application/controllers/Scadenzario.php */
