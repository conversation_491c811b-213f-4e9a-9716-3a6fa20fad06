<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Login extends CI_Controller {
	public function __construct() {
		parent::__construct();
		$this->load->model('Utentimodel');
		$this->load->view('common/head');
	}

	public function index() {
		if($this->session->userdata('login') == true) {
			// redirect(base_url('dashboard'));
			// echo $this->session->userdata('ruolo');exit;
			if ($this->session->userdata('ruolo')!='') redirect(base_url($this->session->userdata('ruolo')));
		}
		$this->load->view('common/login');
	}

	public function dologin() {
		$username = $this->input->post('username');
		$password = $this->input->post('password');

		$data = [
			'username' => $username,
			'password' => $password,
		];
		$utente = $this->Utentimodel->login($data);
		
		if($utente) {
			$data_session = [
				'username' => $utente->username,
				'idutente' => $utente->idutente,
				'ruolo' => $utente->ruolo,
				'datacreato'=>$utente->datacreato,
				'datalogin'=>$utente->datalogin,
				'login' => true,
				'foto'=>$utente->foto,
				'codice'=>$utente->codice,
			];
			$this->session->set_userdata($data_session);
			
			if($this->input->post('remember_me')) {
				$this->load->helper('cookie');
				$cookie = $this->input->cookie('ci_session');
				$this->input->set_cookie('ci_session', $cookie, '31557600');
			}
			
			$this->Utentimodel->aggiorna_ultimologin($utente->idutente);
			
			// Verifica se deve cambiare password
			if ($utente->datacambiopass === null) {
				redirect(base_url('registrazione/cambiapassword'));
			}
			
			echo json_encode(['success' => true]);
		} else {
			echo json_encode(['success' => false]);
			$this->session->set_flashdata(['msg'=>'Nome utente o password errati', 'msgtype'=>'error']);
		}
		redirect(base_url('login'));
	}

	public function logout() {
		$this->load->helper('cookie');
		delete_cookie('edu_msg_home');
		$this->session->sess_destroy();
		redirect(base_url('login'));
	}


}
