<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Admin extends CI_CONTROLLER
{
	private $database;

	public function __construct()
	{
		parent::__construct();
		if ($this->session->login !== true) redirect(base_url('login'));
		if ($this->session->userdata('ruolo') != 'Admin') redirect(base_url($this->session->userdata('ruolo')));

		// $this->db3=$this->load->database('my_mssql', true);
		$this->load->model('Utentimodel');
		$this->load->model('Agentimodel');
		$this->load->model('Clientimodel');
		$this->load->model('Ruolimodel');
		$this->load->model('Ordinimodel');
		$this->load->model('Cataloghimodel');
		$this->load->helper('html');
	}

	public function index()
	{
		$clienti = $this->Clientimodel->getclienti();
		list($query, $nagenti) = $this->Utentimodel->getutenti();

		$this->Ordinimodel->setstatordini();

		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => '']);
		$this->load->view('common/dashboard', [
			'nclienti' => count($clienti),
			'nagenti' => $nagenti,
			'totaleordini' => $this->Ordinimodel->totaleordini,
			'totaledainviare' => $this->Ordinimodel->totaledainviare,
			'totaleinviati' => $this->Ordinimodel->totaleinviati,
		]);
		$this->load->view('common/footer');
	}

	public function agente_nuovo()
	{
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Admin']);
		$this->load->view('admin/agente_nuovo');
		$this->load->view('common/footer');
	}
	public function agente_lista()
	{
		list($query, $totale) = $this->Utentimodel->getutenti();
		$utenti = $query->result();

		$this->mybreadcrumb->add('Gestione agenti', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione agenti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->load->view('admin/agente_lista', ['data' => $utenti]);
		$this->load->view('common/footer');
	}

	public function agente_modifica($idagente)
	{
		$agente = $this->Utentimodel->getutente($idagente);
		// var_dump($agente);

		$this->mybreadcrumb->add('Gestione utenti', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione utenti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->load->view('admin/agente_modifica', ['agente' => $agente]);
		$this->load->view('common/footer');
	}

	public function agente_aggiorna()
	{
		$data = $this->input->post();
		//var_dump($data); exit;
		$idutente = $data['idutente'];
		unset($data['idutente']);
		$this->Utentimodel->modificautente($idutente, $data);
		$this->session->set_flashdata(['msg' => 'Utente aggiornato', 'msgtype' => 'info']);
		redirect(base_url('admin/agente_lista'));
	}

	public function ordini_lista($idagente)
	{
		$agente = $this->Agentimodel->getagente($idagente);
		$nome = $agente->nome . ' ' . $agente->cognome;
		$this->mybreadcrumb->add('Agenti', base_url('admin/agente_lista'));
		$this->mybreadcrumb->add('Ordini agente', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Lista ordini agente: ' . $nome, 'breadcrumb' => $this->mybreadcrumb->render()]);
		$ordini = $this->Agentimodel->getordini($idagente);
		$this->load->view('agente/ordini', ['data' => $ordini, 'idcliente' => '']);
		$this->load->view('common/footer');
	}

	public function agente_elimina($idagente)
	{
		$this->Agentimodel->agente_elimina($idagente);
		redirect(base_url('admin/agente_lista'));
	}

	public function cliente_elimina($idcliente)
	{
		if ($idcliente != '') {
			$this->db->delete('clienti', ['idcliente' => $idcliente]); // ordini e ordiniprodotti vengono eliminati da mysql con le foreign keys
			$this->session->set_flashdata(['msg' => 'Cliente eliminato', 'msgtype' => 'info']);
		}
		redirect(base_url('Agente/cliente_lista'));
	}
	/**
	 * Sposta gli ordini da un cliente ad un altro
	 * usato perchè un agente ha creato lo stesso cliente 2 volte
	 * poi li ha usati entrambi a caso
	 *
	 * @param      <type>  $idclientefrom  The idclientefrom
	 * @param      <type>  $idclienteto    The idclienteto
	 */
	public function ordini_sposta($idclientefrom, $idclienteto)
	{
		$this->db->update('ordini', ['idcliente' => $idclienteto], ['idcliente' => $idclientefrom]);
	}

	public function caricaexcel()
	{
		$this->mybreadcrumb->add('Gestione prodotti', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione prodotti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		if ($this->input->post('cmd') == 'Carica') {
			$config['overwrite']          = true;
			$config['upload_path']          = './upload/';
			$config['allowed_types']        = 'xls|xlsx';
			$config['max_size']             = 350000;
			$config['file_name']			= 'listino';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('fileutente')) {
				echo $this->upload->display_errors();
				var_dump($this->upload->data());
			} else {
				$filename = $this->upload->data()['file_name'];
				$out = heading("$filename caricato", 3);
				foreach ($this->upload->data() as $key => $value) {
					$out .= "$key: $value <br>";
				}
				$out .= "<p><strong>Ricordare di aggiornare il listino per rendere effettivo il file caricato..</strong></p>";
				if ($this->upload->data()['file_size'] > 500) $out .= "<p><strong>Controlla il file potrebbe essere non ottimizzato</strong>";
				$this->load->view('common/body', array('testo' => $out));
				// var_dump($this->upload->data());
			}
		} else {
			$this->load->view('admin/caricaexcelform');
		}
		$this->load->view('common/footer');
	}

	public function caricaexcel_clienti()
	{
		$this->mybreadcrumb->add('Gestione clienti', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione clienti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		if ($this->input->post('cmd') == 'Carica') {
			$config['overwrite']          = true;
			$config['upload_path']          = './upload/';
			$config['allowed_types']        = 'xls|xlsx';
			$config['max_size']             = 350000;
			$config['file_name']			= 'clienti';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('fileutente')) {
				echo $this->upload->display_errors();
				var_dump($this->upload->data());
			} else {
				$filename = $this->upload->data()['file_name'];
				$out = heading("$filename caricato", 3);
				foreach ($this->upload->data() as $key => $value) {
					$out .= "$key: $value <br>";
				}
				$out .= "<p>Ricordare di aggiornare i clienti per rendere effettivo il file caricato..</p>";
				if ($this->upload->data()['file_size'] > 500) $out .= "<p><strong>Controlla il file potrebbe essere non ottimizzato</strong>";
				$this->load->view('common/body', array('testo' => $out));
				// var_dump($this->upload->data());
			}
		} else {
			$this->load->view('admin/caricaexcelform_clienti');
		}
		$this->load->view('common/footer');
	}

	public function listino2db()
	{
		$this->load->model('Prodottimodel');
		$this->mybreadcrumb->add('Gestione prodotti', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione prodotti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$arr = $this->Prodottimodel->checkxlslistino();
		if (count($arr['campinodb']) == 0) {
			$totaleprodotti = $this->Prodottimodel->aggiornaprodotti();
			$out = 'Totale prodotti: ' . $totaleprodotti;
		} else {
			$out = 'I campi del file listino non corrispondono<br>Campi mancanti nel file:<br>';
			$out .= implode('<br>', $arr['campinodb']);
		}
		$this->load->view('common/body', array('testo' => $out));
		$this->load->view('common/footer');
	}

	public function clienti2db()
	{
		$this->mybreadcrumb->add('Gestione clienti', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione clienti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$arr = $this->Clientimodel->checkxlsclienti();
		if (count($arr['campinodb']) == 0) {
			list($msg, $totaleclienti) = $this->Clientimodel->aggiornaclienti();
			$out = 'Totale clienti aggiunti: ' . $totaleclienti . '<br>' . $msg;
		} else {
			$out = 'I campi del file listino non corrispondono<br>Campi mancanti nel file:<br>';
			$out .= implode('<br>', $arr['campinodb']);
		}
		$this->load->view('common/body', array('testo' => $out));
		$this->load->view('common/footer');
	}

	public function listino()
	{
		$this->load->model('Prodottimodel');
		$this->mybreadcrumb->add('Gestione prodotti', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione prodotti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$prodotti = $this->Prodottimodel->lista();
		$this->load->view('admin/prodotti_lista', ['data' => $prodotti]);
		$this->load->view('common/footer');
	}

	public function cataloghicarica()
	{
		$this->mybreadcrumb->add('Gestione cataloghi', '#');
		$this->load->view('common/header', ['tipoutente' => 'admin', 'title' => 'Gestione cataloghi', 'breadcrumb' => $this->mybreadcrumb->render()]);
		if ($this->input->post('cmd') == 'Carica') {
			$config['overwrite']          = false;
			$config['upload_path']          = CATALOGHIPATH;
			$config['allowed_types']        = 'pdf';
			$config['max_size']             = 10000000;
			// $config['file_name']			= 'cata';

			$this->load->library('upload', $config);

			if (!$this->upload->do_upload('fileutente')) {
				echo $this->upload->display_errors();
				// var_dump($this->upload->data());
			} else {
				$filename = $this->upload->data()['file_name'];
				$titolo = $this->input->post('titolo');
				if ($this->input->post('titolo') == '') $titolo = $filename;
				$data = array(
					'titolo' => $titolo,
					'descrizione' => $this->input->post('descrizione'),
					'file_name' => $filename,
					'dimensione' => round($this->upload->data()['file_size']),
				);
				$idcatalogo = $this->Cataloghimodel->addcatalogo($data);
				$out = heading("$filename caricato", 3);
				foreach ($this->upload->data() as $key => $value) {
					$out .= "$key: $value <br>";
				}
				$this->load->view('common/body', array('testo' => $out));
				var_dump($this->upload->data());
				redirect(base_url('agente/cataloghi'));
			}
		} else {
			//@todo       prima della view calcola spazio a disposizione e blocca se supera
			$spazio = round($this->Cataloghimodel->getspazioutilizzato() / 1024);
			$this->load->view('admin/cataloghicaricaform', ['spazio' => $spazio]);
		}
		$this->load->view('common/footer');
	}

	public function cataloghi_elimina($id)
	{
		$this->Cataloghimodel->delcatalogo($id);
		redirect(base_url('agente/cataloghi'));
	}

	public function cataloghi_pubblicato($id, $stato)
	{
		$this->Cataloghimodel->setpubblicato($id, $stato);
		redirect(base_url('agente/cataloghi'));
	}
}
