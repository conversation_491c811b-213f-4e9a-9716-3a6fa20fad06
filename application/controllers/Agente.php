<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Agente extends CI_CONTROLLER
{
	private $database;
	public	$tiporiga = array(
		'01' => 'Vendita',
		'04' => 'Omaggio',
		'07' => 'Comodato d\'uso',
		// '08'=>'Sconto incondizionato',
	);

	public function __construct()
	{
		parent::__construct();
		if ($this->session->login !== true) redirect(base_url('login'));
		// if ($this->session->userdata('ruolo')!='Agente') redirect(base_url($this->session->userdata('ruolo')));

		// $this->db3=$this->load->database('my_mssql', true);
		$this->load->model('Utentimodel');
		$this->load->model('Clientimodel');
		$this->load->model('Ordinimodel');
		$this->load->model('Prodottimodel');
		$this->load->model('Ruolimodel');
		$this->load->model('Cataloghimodel');
		$this->load->model('Preventivimodel');
		$this->load->helper('html');
	}

	public function index()
	{
		$clienti = $this->Clientimodel->getclienti();
		list($query, $nagenti) = $this->Utentimodel->getutenti();
		$this->Ordinimodel->setstatordini();

		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Agente']);
		$this->load->view('common/dashboard', [
			'nclienti' => count($clienti),
			'nagenti' => $nagenti,
			'totaleordini' => $this->Ordinimodel->totaleordini,
			'totaledainviare' => $this->Ordinimodel->totaledainviare,
			'totaleinviati' => $this->Ordinimodel->totaleinviati,
		]);
		$this->load->view('common/footer');
	}

	// public function agente_modifica($idagente) {
	// 	$agente=$this->Utentimodel->getutente($idagente);
	// 	// var_dump($agente);

	// 	$this->mybreadcrumb->add('Gestione agenti', '#');
	// 	$this->load->view('common/header', ['tipoutente'=>'agente', 'title'=>'Gestione agenti', 'breadcrumb'=>$this->mybreadcrumb->render()]);
	// 	$this->load->view('admin/agente_modifica', ['agente'=>$agente]);
	// 	$this->load->view('common/footer');
	// }

	// public function agente_aggiorna() {
	// 	$data=$this->input->post();
	// 	//var_dump($data); exit;
	// 	$idutente=$data['idutente'];
	// 	unset($data['idutente']);
	// 	$this->Utentimodel->modificautente($idutente, $data);
	// 	$this->session->set_flashdata(['msg'=>'Agente aggiornato', 'msgtype'=>'info']);
	// 	redirect(base_url('Agente/agente_lista'));
	// }

	public function cliente_nuovo($codice = '', $ragsoc = '')
	{
		if ($ragsoc != '') $ragsoc = base64_decode($ragsoc);
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Nuovo cliente']);
		if ($this->input->post()) {
			$esito = $this->Clientimodel->nuovocliente($this->input->post());
			if ($esito === true) {
				$this->session->set_flashdata(['msg' => 'Cliente aggiunto', 'msgtype' => 'info']);
				redirect(base_url('Agente/cliente_lista'));
			} else {
				$this->session->set_flashdata(['msg' => $esito, 'msgtype' => 'warning']);
				redirect(base_url('Agente/cliente_nuovo/' . $cliente->idcliente));
			}
		} else {
			$agenti = array('' => 'Non assegnato');
			if ($this->session->ruolo == 'Admin') {
				list($agentirs, $totale) = $this->Utentimodel->getutenti(["idruolo" => 2]);
				$agentiob = $agentirs->result();
				// var_dump($agentiob);exit;
				foreach ($agentiob as $value) {
					$agenti[$value->idutente] = $value->nome . ' ' . $value->cognome;
				}
				uasort($agenti, function ($a, $b) {
					if ($a == $b) {
						return 0;
					}
					return ($a < $b) ? -1 : 1;
				});
			}
			$this->load->view('admin/cliente_nuovo', ['agenti' => $agenti, 'codice' => $codice, 'ragsoc' => $ragsoc]);
		}
		$this->load->view('common/footer');
	}

	public function cliente_lista($idutente = '')
	{
		// $clienti = $this->Clientimodel->getclienti($idutente);

		$this->mybreadcrumb->add('Gestione clienti', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Gestione clienti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->load->view('admin/cliente_lista_ajax');
		$this->load->view('common/footer');
	}

	public function get_clienti_ajax($idutente = '')
	{
		$postData = $this->input->post();
		$data = $this->Clientimodel->getclienti_ajax($idutente, $postData);

		echo json_encode($data);
	}

	public function cliente_elencoglobale($stato = 'attivi')
	{
		$clienti = $this->Clientimodel->getelencoclienti($stato);

		switch ($stato) {
			case 'attivi':
				$titolo = 'Clienti attivi';
				break;
			default:
				$titolo = 'Clienti non attivi';
				break;
		}

		// $this->mybreadcrumb->add($titolo, '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => $titolo, 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->load->view('admin/cliente_elencoglobale', ['data' => $clienti]);
		$this->load->view('common/footer');
	}

	// public function cliente_scheda($idcliente) {
	// 	$cliente=$this->Clientimodel->getcliente($idcliente);

	// 	$this->mybreadcrumb->add('Gestione clienti', '#');
	// 	$this->load->view('common/header', ['tipoutente'=>'agente', 'title'=>'Gestione clienti', 'breadcrumb'=>$this->mybreadcrumb->render()]);
	// 	$this->load->view('admin/cliente_scheda', ['cliente'=>$cliente]);
	// 	$this->load->view('common/footer');
	// }

	public function cliente_modifica($idcliente)
	{
		$this->mybreadcrumb->add('Clienti', base_url('agente/cliente_lista'));
		$this->mybreadcrumb->add('Modifica', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Gestione clienti', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$cliente = $this->Clientimodel->getcliente($idcliente);
		if (is_object($cliente)) {
			$agenti = array('' => 'Non assegnato');
			if ($this->session->ruolo == 'Admin') {
				list($agentirs, $totale) = $this->Utentimodel->getutenti(["idruolo" => 2]);
				$agentiob = $agentirs->result();
				// var_dump($agentiob);exit;
				foreach ($agentiob as $value) {
					$agenti[$value->idutente] = $value->nome . ' ' . $value->cognome;
				}
				uasort($agenti, function ($a, $b) {
					if ($a == $b) {
						return 0;
					}
					return ($a < $b) ? -1 : 1;
				});
			}
			$this->load->view('admin/cliente_modifica', ['cliente' => $cliente, 'agenti' => $agenti]);
		}

		$this->load->view('common/footer');
	}

	/**
	 * Aggiorna cliente con i dati dalla form
	 * e invia una mail a Luccacarta
	 * @return void
	 */
	public function cliente_aggiorna()
	{
		$data = $this->input->post();
		$idcliente = $data['idcliente'];
		$clienteold = (array) $this->Clientimodel->getCliente($idcliente);
		$agente = $this->Utentimodel->getutente($this->session->idutente);
		unset($data['idcliente']);
		// var_dump($clienteold);
		// var_dump($data);
		$testomail = $this->load->view('mail/clientemodificato', ['dati' => $data, 'clienteold' => $clienteold, 'agente' => $agente], true);
		// echo $testomail;
		// exit;
		$this->load->library('email');
		$config['mailtype'] = 'html';
		$this->email->initialize($config);

		$this->email->from($this->config->item('mailfrom'), 'Agente ' . $agente->nome . ' ' . $agente->cognome);
		$this->email->reply_to($agente->email);
		if (ENVIRONMENT === 'development') {
		    $this->email->to('<EMAIL>');
		} else {
		    $this->email->to($this->config->item('mail.invio.ordini'));
		}

		$this->email->subject('Cliente modificato: ' . $clienteold['ragsoc']);
		$this->email->message($testomail);
		$this->email->send();
		// exit;

		$this->Clientimodel->aggiornacliente($idcliente, $data);
		$this->session->set_flashdata(['msg' => 'Cliente aggiornato', 'msgtype' => 'info']);
		redirect(base_url('Agente/cliente_lista'));
	}

	public function cliente_ordini($idcliente)
	{
		$ordini = $this->Ordinimodel->getordini($idcliente);
		$cliente = $this->Clientimodel->getcliente($idcliente);
		// $prodotti=$this->Ordinimodel->getordiniprodotti()
		$this->mybreadcrumb->add('Clienti', base_url('agente/cliente_lista'));
		$this->mybreadcrumb->add('Ordini cliente', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => $cliente->ragsoc . ' <small>ordini</small>', 'breadcrumb' => $this->mybreadcrumb->render()]);

		$this->load->view('agente/ordini_ajax', ['idcliente' => $idcliente]);
		$this->load->view('common/footer');
	}

	public function listino()
	{
		$this->mybreadcrumb->add('Listino', '');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Listino', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$prodotti = $this->Prodottimodel->lista();
		$this->load->view('admin/prodotti_lista', ['data' => $prodotti]);
		$this->load->view('common/footer');
	}

	public function ordini_nuovo($idcliente = '')
	{
		if ($idcliente == '') redirect(base_url('agente/cliente_lista'));
		// $prodottiordinati=$this->Clientimodel->getprodottiordinati($idcliente);
		// $ordini=$this->Ordinimodel->getordini($idcliente);
		// $listino = $this->Prodottimodel->listaconordinati($idcliente);
		$cliente = $this->Clientimodel->getcliente($idcliente);

		$this->mybreadcrumb->add('Gestione ordini', '');
		$this->mybreadcrumb->add('Clienti', base_url('agente/cliente_lista'));
		$this->mybreadcrumb->add('Nuovo ordine', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Nuovo ordine', 'breadcrumb' => $this->mybreadcrumb->render()]);

		$this->load->view('agente/ordini_nuovo', [
			// 'ordini'=>$ordini,
			'cliente' => $cliente,
			// 'listino' => $listino,
		]);
		$this->load->view('common/footer');
	}
	public function ordini_modifica($idordine)
	{
		$ordine = $this->Ordinimodel->getordine($idordine);
		$prodotti = $this->Ordinimodel->getordiniprodotti($ordine->idordine);
		// $listino = $this->Prodottimodel->listaconordinati($ordine->idcliente);
		$cliente = $this->Clientimodel->getcliente($ordine->idcliente);
		$agente = $this->Utentimodel->getutente($ordine->idagente);
		$this->mybreadcrumb->add('Gestione ordini', '');
		$this->mybreadcrumb->add('Ordine', base_url('agente/ordini_scheda/' . $idordine));
		$this->mybreadcrumb->add('Modifica', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Modifica ordine #' . $idordine, 'breadcrumb' => $this->mybreadcrumb->render()]);

		$this->load->view('agente/ordini_modifica', [
			'ordine' => $ordine,
			'cliente' => $cliente,
			'prodotti' => $prodotti,
			// 'listino' => $listino,
		]);
		$this->load->view('common/footer');
	}

	public function ordini_salva()
	{
		// var_dump($this->input->post()); exit;
		$idordine = $this->Ordinimodel->salvaordinenuovo($this->input->post());
		$idcliente = $this->input->post('idcliente');
		// $this->session->set_flashdata(['msg' => 'Ordine salvato', 'msgtype' => 'info']);
		$myflashdata = new MyFlashdata;
		$myflashdata->addFlashdataMsg('Ordine salvato', 'success');
		redirect(base_url('agente/ordini_scheda/' . $idordine));
	}

	public function ordini_salvamodifica()
	{
		$this->Ordinimodel->salvaordinemodifica($this->input->post());
		// $this->session->set_flashdata(['msg' => 'Ordine salvato', 'msgtype' => 'info']);
		$myflashdata = new MyFlashdata;
		$myflashdata->addFlashdataMsg('Ordine modificato', 'success');
		redirect(base_url('agente/ordini_scheda/' . $this->input->post('idordine')));
	}

	public function ordini_elimina($idordine, $idcliente)
	{
		$this->mybreadcrumb->add('Gestione ordini', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Gestione ordini', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->Ordinimodel->eliminaordine($idordine);
		$this->session->set_flashdata(['msg' => 'Ordine eliminato', 'msgtype' => 'info']);
		redirect(base_url('agente/cliente_ordini/' . $idcliente));
	}

	public function ordini_lista()
	{
		$this->mybreadcrumb->add('Gestione ordini', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Lista ordini', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$ordini = $this->Ordinimodel->getordini();
		// $this->load->view('agente/ordini', ['data' => $ordini, 'idcliente' => '']);
		$this->load->view('agente/ordini_ajax', ['idcliente' => '']);
		$this->load->view('common/footer');
	}

	public function get_ordini_ajax()
	{
		$postData = $this->input->post();
		$data = $this->Ordinimodel->getordini_ajax($postData);

		echo json_encode($data);
	}

	public function ordini_scheda($idordine = '')
	{
		if (empty($idordine)) {
			redirect(base_url('agente/ordini_lista'));
		}

		$ordine = $this->Ordinimodel->getordine($idordine);
		$prodotti = $this->Ordinimodel->getordiniprodotti($ordine->idordine);
		$cliente = $this->Clientimodel->getcliente($ordine->idcliente);
		$agente = $this->Utentimodel->getutente($ordine->idagente);

		$oct = new stdClass();
		$oct->DOC_ERP = '';
		$oct->ID_CART = '';

		// questo lo faccio a prescidere che sia stato inviato o no perchè capita raramente che l'ordine sia stato inviato
		// ma per qualche motivo sul portale non risulta inviato e magari il carrello esiste.
		if ($ordine->NROROO == '') {
			$this->load->model('AS400/ASOrdiniModel');
			$oct = $this->ASOrdiniModel->getCarrelloTestata($idordine);
			if (!empty($oct->TDOCOO) && (!empty($oct->NROROO))) {
				$data = array(
					'TDOCOO' => $oct->TDOCOO,
					'NROROO' => $oct->NROROO,
				);
				$this->Ordinimodel->updateOrdine($idordine, $data);
			}
		} else {
			$oct->DOC_ERP = $ordine->TDOCOO . $ordine->NROROO;
		}

		$this->mybreadcrumb->add('Gestione ordini', '');
		$this->mybreadcrumb->add('Lista ordini', base_url('agente/ordini_lista'));
		$this->mybreadcrumb->add('Ordini cliente', base_url('agente/cliente_ordini/' . $ordine->idcliente));
		$this->mybreadcrumb->add('Ordine', '#');

		$data = [
			'ordine' => $ordine,
			'DOC_ERP' => $oct->DOC_ERP,
			'cliente' => $cliente,
			'prodotti' => $prodotti,
			'agente' => $agente,
			'nobtns' => false,
		];

		//$this->load->model('AS400/ASBolleFattureModel');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Dettaglio ordine #' . $idordine, 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->load->view('agente/ordini_scheda', $data);
		$this->load->view('common/footer');
	}

	public function ordini_duplica($idordine)
	{
		$newid = $this->Ordinimodel->duplicaordine($idordine);
		$ordine = $this->Ordinimodel->getordine($newid);
		$this->session->set_flashdata(['msg' => 'Ordine duplicato', 'msgtype' => 'info']);

		redirect(base_url('agente/cliente_ordini/' . $ordine->idcliente));
	}

	/**
	 * Invia ordine a Luccacarta
	 */
	public function ordini_invia($idordine)
	{
		log_message('info', '=== INIZIO ordini_invia() - idordine: ' . $idordine . ' ===');
		$ordine = $this->Ordinimodel->getordine($idordine);
		$prodotti = $this->Ordinimodel->getordiniprodotti($ordine->idordine);
		$cliente = $this->Clientimodel->getcliente($ordine->idcliente);
		$agente = $this->Utentimodel->getutente($ordine->idagente);

		log_message('info', 'Dati base caricati - Cliente: ' . $cliente->ragsoc . ' - Agente: ' . $agente->nome);

		$myflashdata = new MyFlashdata;
		$flag_cliente_bloccato = $this->Clientimodel->checkClienteBloccato($cliente); // questo cancella il codice cliente se risulta bloccato su JG
		log_message('info', 'Check cliente bloccato: ' . ($flag_cliente_bloccato ? 'SI' : 'NO'));

		$json = json_encode(['ordine' => $ordine, 'prodotti' => $prodotti, 'cliente' => $cliente, 'agente' => $agente]);
		// inviamo ordine ad as400...
		$this->load->model('AS400/ASClientiModel');
		$this->load->model('AS400/ASOrdiniModel');
		// $as_cliente=$this->ASClientiModel->getCliente($cliente->codice);
		// var_dump($as_cliente);
		// controllo se l'ordine è già stato creato in as400
		list($anac, $ocr, $oct) = $this->ASOrdiniModel->getCarrello($idordine);
		log_message('info', 'Stato carrello - ID_CART: ' . $oct->ID_CART);

		if ($oct->ID_CART == '') {
			log_message('info', 'Creazione nuovo ordine AS400');
			$esito = $this->ASOrdiniModel->addOrdine($cliente, $ordine, $prodotti, $agente);
			if ($esito == '') {
				log_message('info', 'Ordine AS400 creato con successo');
				$this->ASOrdiniModel->inviaCarrello($idordine); // con invio ordine si genera numoero ordine JG: campo DOC_ERP
				list($anac, $ocr, $oct) = $this->ASOrdiniModel->getCarrello($idordine);

				// verifico se è stato spedito il carrello che genera ordine vero e proprio per luccacarta
				$ordineJG = $this->ASOrdiniModel->getOrdineJG($oct->DOC_ERP);
				log_message('info', 'Stato ordine JG - DOC_ERP: ' . $oct->DOC_ERP);
				if (!empty($ordineJG->NROROO)) {
					log_message('info', 'Aggiornamento riferimenti ERP');
					// aggiorno campi ERP nell'ordine dell'agente
					$data = array(
						'TDOCOO' => $oct->TDOCOO,
						'NROROO' => $oct->NROROO,
					);
					$this->Ordinimodel->updateOrdine($idordine, $data);
				} else {
					$ordineJG->TDOCOO = '';
					$ordineJG->NROROO = '';
				}
			} else {
				log_message('error', 'Errore creazione ordine AS400: ' . $esito);
				$testo = "idordine: " . $idordine . "<br>";
				$testo .= "cliente:" . $cliente->ragsoc . "<br>";
				$testo .= "agente:" . $agente->nome . ' ' . $agente->cognome . "<br>";
				$testo .= $esito;
				inviamail('<EMAIL>', 'Errore controller Agente/inviaordine', $testo);

				if (preg_match('/Il valore per la richiesta o per la variabile INDIRI è troppo lungo/', $esito)) {
					$this->session->set_flashdata(['msg' => 'ERRORE: Questo ordine non può essere inviato, il campo INDIRIZZO è troppo lungo.<br>Correggerlo e provare a inviarlo di nuovo', 'msgtype' => 'danger']);
					redirect(base_url('agente/cliente_modifica/' . $ordine->idcliente));
				} else {
					$this->session->set_flashdata(['msg' => 'ERRORE: Questo ordine non può essere inviato, è stata inviata una segnalazione tecnica per risovere il problema.<br>' . $esito, 'msgtype' => 'danger']);
					redirect(base_url('agente/ordini_modifica/' . $ordine->idordine));
				}
			}
		} else {
			log_message('info', 'Ordine AS400 già esistente');
			// se ordine c'è già verifico se è stato spedito il carrello che genera ordine vero e proprio per luccacarta
			$ordineJG = $this->ASOrdiniModel->getOrdineJG($oct->DOC_ERP);
			// $this->session->set_flashdata(['msg' => 'Ordine inviato', 'msgtype' => 'info']);
			$myflashdata->addFlashdataMsg('Ordine già inviato in precedenza', 'notice');
		}

		$schedahtml = $this->load->view('agente/ordini_mail', [
			'ordine' => $ordine,
			'DOC_ERP' => $ordineJG->TDOCOO . $ordineJG->NROROO,
			'cliente' => $cliente,
			'prodotti' => $prodotti,
			'agente' => $agente,
			'flag_cliente_bloccato' => $flag_cliente_bloccato,
		], true);

		// print_r($schedahtml); exit;
		log_message('info', 'Preparazione invio email - Environment: ' . ENVIRONMENT . ' - Codice cliente: ' . $cliente->codice);
		if (ENVIRONMENT == 'production') {
			// invio mail solo se il cliente è nuovo/senza codice
			if ($cliente->codice == '') {
				$this->load->library('email');
				$config['mailtype'] = 'html';
				$this->email->initialize($config);

				$this->email->from($this->config->item('mailfrom'), 'Agente ' . $agente->nome . ' ' . $agente->cognome);
				$this->email->reply_to($agente->email);
				$this->email->to($this->config->item('mail.invio.ordini'));

				$this->email->subject('Ordine #' . $ordine->idordine . ' ' . $cliente->ragsoc);
				$this->email->message($schedahtml);

				// Log del tentativo di invio
				log_message('info', 'Tentativo invio email ordine #' . $ordine->idordine .
								  ' da: ' . $agente->email .
								  ' a: ' . $this->config->item('mail.invio.ordini') .
								  ' cliente: ' . $cliente->ragsoc);

				$sent = $this->email->send();

				// Log del risultato
				if($sent) {
					log_message('info', 'Email inviata con successo per ordine #' . $ordine->idordine);
				} else {
					log_message('error', 'Errore invio email ordine #' . $ordine->idordine . ': ' . $this->email->print_debugger());
				}
			}
		}
		log_message('info', 'Aggiornamento data invio ordine');
		$this->db->update('ordini', ['datainviato' => date('Y-m-d H:i:s')], ['idordine' => $idordine]);
		// $this->session->set_flashdata(['msg' => 'Ordine inviato', 'msgtype' => 'info']);
		$myflashdata->addFlashdataMsg('Ordine inviato', 'success');

		$this->session->set_flashdata('messages', $myflashdata->getFlashdataMsgs());
		log_message('info', '=== FINE ordini_invia() - Redirect a scheda ordine ===');
		redirect(base_url('agente/ordini_scheda/' . $idordine));
	}

	public function cataloghi()
	{
		$cataloghi = $this->Cataloghimodel->getcataloghi();
		$spazio = round($this->Cataloghimodel->getspazioutilizzato() / 1024) . ' Mb';
		$this->mybreadcrumb->add('Cataloghi', '#');
		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Lista cataloghi', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->load->view('agente/cataloghi', ['data' => $cataloghi, 'spazio' => $spazio]);
		$this->load->view('common/footer');
	}

	/**
	 * Modifica profilo agente
	 */
	public function agente_modifica()
	{
		$agente = $this->Utentimodel->getutente($this->session->idutente);
		// var_dump($agente);

		$this->load->view('common/header', ['tipoutente' => 'agente', 'title' => 'Modifica profilo', 'breadcrumb' => $this->mybreadcrumb->render()]);
		$this->load->view('agente/agente_modifica', ['agente' => $agente]);
		$this->load->view('common/footer');
	}

	public function agente_aggiorna()
	{
		$data = $this->input->post();
		//var_dump($data); exit;
		$idutente = $this->session->idutente;
		$this->Utentimodel->modificautente($idutente, $data);
		$this->session->set_flashdata(['msg' => 'Dati aggiornati', 'msgtype' => 'info']);
		redirect(base_url('agente/agente_modifica/'));
	}
}
