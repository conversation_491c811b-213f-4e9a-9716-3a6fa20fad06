<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Preventivi extends CI_Controller {

	public function __construct() {
		parent::__construct();
		if($this->session->login !== true) redirect(base_url('login'));
		$this->load->model('Preventivimodel');
		$this->load->model('Prodottimodel');
		$this->load->model('Utentimodel');
		$this->load->model('Clientimodel');
		$this->load->model('Ordinimodel');
		$this->load->helper('html');
	}

	public function preventivi_lista() {
		$this->mybreadcrumb->add('Gestione preventivi', '#');
		$this->load->view('common/header', ['tipoutente'=>'agente', 'title'=>'Lista preventivi', 'breadcrumb'=>$this->mybreadcrumb->render()]);
		$preventivi=$this->Preventivimodel->getpreventivi();
		$this->load->view('preventivi/preventivi_lista', ['data'=>$preventivi, 'idcliente'=>'']);
		$this->load->view('common/footer');
	}

	public function preventivi_nuovo($idcliente='') {
 		$listino=$this->Prodottimodel->lista($idcliente);
 		// $cliente=$this->Clientimodel->getcliente($idcliente);
		$this->load->view('common/header', ['tipoutente'=>'agente', 'title'=>'Nuovo preventivo', 'breadcrumb'=>$this->mybreadcrumb->render()]);

		$this->load->view('preventivi/preventivi_nuovo', [
			// 'ordini'=>$ordini,
			// 'cliente'=>$cliente,
			'listino'=>$listino,
		]);
		$this->load->view('common/footer');
	}

	public function preventivi_modifica($idpreventivo) {
 		$preventivo=$this->Preventivimodel->getpreventivo($idpreventivo);
 		$prodotti=$this->Preventivimodel->getpreventiviprodotti($preventivo->idpreventivo);
 		$listino=$this->Prodottimodel->lista();
 		$agente=$this->Utentimodel->getutente($preventivo->idagente);
		$this->mybreadcrumb->add('Gestione preventivi', '');
		$this->mybreadcrumb->add('preventivo', base_url('agente/ordini_scheda/'.$idpreventivo));
		$this->mybreadcrumb->add('Modifica', '#');
		$this->load->view('common/header', ['tipoutente'=>'agente', 'title'=>'Modifica preventivo #'.$idpreventivo, 'breadcrumb'=>$this->mybreadcrumb->render()]);

		$this->load->view('preventivi/preventivi_modifica', [
			'preventivo'=>$preventivo,
			'prodotti'=>$prodotti,
			'listino'=>$listino,
		]);
		$this->load->view('common/footer');
	}

	public function preventivi_modifica_ex() {
		$this->Preventivimodel->preventivi_modifica_ex($this->input->post());
		$this->session->set_flashdata(['msg'=>'Preventivo modificato', 'msgtype'=>'info']);
		redirect(base_url('preventivi/preventivi_scheda/'.$this->input->post('idpreventivo')));
	}

	public function preventivi_salva() {
		$idpreventivo=$this->Preventivimodel->salvapreventivonuovo($this->input->post());
		$this->session->set_flashdata(['msg'=>'preventivo salvato', 'msgtype'=>'info']);
		redirect(base_url('preventivi/preventivi_scheda/'.$idpreventivo));
	}

	public function preventivi_elimina_ex($idpreventivo) {
		$this->Preventivimodel->eliminapreventivo($idpreventivo);
		$this->session->set_flashdata(['msg'=>'Preventivo eliminato', 'msgtype'=>'info']);
		redirect(base_url('preventivi/preventivi_lista'));
	}


	/*------------------------------------------------------------------------*//**
	 * Scheda preventivo
	 *
	 * @param      integer  $idpreventivo  The idpreventivo
	 * @param      string   $out           scheda|pdf
	 */
	public function preventivi_scheda($idpreventivo, $out="scheda") {
		$preventivo=$this->Preventivimodel->getpreventivo($idpreventivo);
		$colonnesconto=$this->Preventivimodel->getColonneSconto($idpreventivo);
		$colonnaqta=$this->Preventivimodel->visQuantita($idpreventivo);
		$prodotti=$this->Preventivimodel->getpreventiviprodotti($preventivo->idpreventivo);
		$agente=$this->Utentimodel->getutente($preventivo->idagente);

		if ($out=='scheda') {
			$this->mybreadcrumb->add('Gestione preventivi', '');
			$this->mybreadcrumb->add('Lista preventivi', base_url('preventivi/preventivi_lista'));
			$this->mybreadcrumb->add('preventivo', '#');
			$this->load->view('common/header', ['tipoutente'=>'agente', 'title'=>'Dettaglio preventivo #'.$idpreventivo, 'breadcrumb'=>$this->mybreadcrumb->render()]);
			$this->load->view('preventivi/preventivi_scheda', [
				'preventivo'=>$preventivo,
				'colonnesconto'=>$colonnesconto,
				'prodotti'=>$prodotti,
				'agente'=>$agente,
				'colonnaqta'=>$colonnaqta,
				'nobtns'=>false,
			]);
			$this->load->view('common/footer');
		} else {
			$html_header=$this->load->view('preventivi/preventivi_pdf_header', [
				'preventivo'=>$preventivo,
			], true);
			$html=$this->load->view('preventivi/preventivi_pdf', [
				'preventivo'=>$preventivo,
				'colonnesconto'=>$colonnesconto,
				'prodotti'=>$prodotti,
				'agente'=>$agente,
				'colonnaqta'=>$colonnaqta,
				'nobtns'=>true,
			], true);
			$this->load->library('mypdf');
			if ($colonnesconto[3]==1) $pdf = new MYPDF('L');
			else $pdf = new MYPDF('P');
			$pdf->SetMargins(5, 50, 5);
			$pdf->setHeaderData($ln='', $lw=0, $ht='', $hs=$html_header, $tc=array(0,0,0), $lc=array(0,0,0));
			$pdf->setFooterData('footer',0,'','',array(0,0,0), array(0,0,0) );
			$pdf->AddPage();
			$pdf->writeHTML($html, true, false, true, false, '');

			// Sanitize client name for filename
			$client_name = strtolower($preventivo->cliente);
			$client_name = preg_replace('/[^a-z0-9]+/', '_', $client_name);
			$client_name = trim($client_name, '_');
			$filename = 'preventivo_' . $client_name . '.pdf';

			$pdf->Output($filename, 'I');
		}
	}

	public function preventivo_ordine_ex()
	{
		$data=$this->input->post();
		// var_dump($data);exit;
		foreach ($data['codice'] as $key => $value) {
			if ($data['prezzopreventivo'][$key]>$data['prezzo'][$key]) {
				$data['sconto'][$key]=100-number_format($data['prezzo'][$key]/$data['prezzopreventivo'][$key]*100, 2);
			}
			$data['prezzoordine'][$key]=$data['prezzopreventivo'][$key];

		}
		// var_dump($data);exit;

		$idordine=$this->Ordinimodel->salvaordinenuovo($data);
		$idcliente=$this->input->post('idcliente');
		$this->session->set_flashdata(['msg'=>'Ordine salvato', 'msgtype'=>'info']);
		redirect(base_url('agente/ordini_modifica/'.$idordine));
	}

}

/* End of file Preventivi.php */
/* Location: ./application/controllers/Preventivi.php */