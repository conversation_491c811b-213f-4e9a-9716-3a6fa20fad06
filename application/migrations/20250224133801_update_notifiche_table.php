<?php defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Update_notifiche_table extends CI_Migration {

    public function up() {
        // Rimuovo prima il vincolo di chiave esterna
        $this->db->query('ALTER TABLE `notifiche` DROP FOREIGN KEY `fk_scadenzario`');
        $this->db->query('ALTER TABLE `notifiche` DROP INDEX `fk_scadenzario`');
        
        // Rimuovo la colonna id_scadenzario
        $this->db->query('ALTER TABLE `notifiche` DROP COLUMN `id_scadenzario`');
        
        // Aggiungo le nuove colonne
        $this->dbforge->add_column('notifiche', array(
            'conto' => array(
                'type' => 'VARCHAR',
                'constraint' => 10,
                'null' => TRUE,
                'after' => 'id'
            )
        ));
        
        $this->dbforge->add_column('notifiche', array(
            'partita' => array(
                'type' => 'INT',
                'constraint' => 10,
                'unsigned' => TRUE,
                'null' => TRUE,
                'after' => 'conto'
            )
        ));
        
        $this->dbforge->add_column('notifiche', array(
            'scadenza' => array(
                'type' => 'DATE',
                'null' => TRUE,
                'after' => 'partita'
            )
        ));
    }

    public function down() {
        // Rimuovo le nuove colonne
        $this->dbforge->drop_column('notifiche', 'scadenza');
        $this->dbforge->drop_column('notifiche', 'partita');
        $this->dbforge->drop_column('notifiche', 'conto');
        
        // Ripristino la colonna id_scadenzario e il vincolo
        $this->dbforge->add_column('notifiche', array(
            'id_scadenzario' => array(
                'type' => 'INT',
                'constraint' => 10,
                'unsigned' => TRUE,
                'null' => FALSE,
                'after' => 'id'
            )
        ));
        
        $this->db->query('ALTER TABLE `notifiche` ADD INDEX `fk_scadenzario` (`id_scadenzario`) USING BTREE');
        $this->db->query('ALTER TABLE `notifiche` ADD CONSTRAINT `fk_scadenzario` FOREIGN KEY (`id_scadenzario`) REFERENCES `scadenzario` (`id`) ON UPDATE RESTRICT ON DELETE RESTRICT');
    }
}