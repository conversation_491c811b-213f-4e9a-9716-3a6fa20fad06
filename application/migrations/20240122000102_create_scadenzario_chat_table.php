<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_scadenzario_chat_table extends CI_Migration {

    public function up() {
        // Definizione della tabella scadenzario_chat
        $this->dbforge->add_field(array(
            'id' => array(
                'type'           => 'INT',
                'constraint'     => 10,
                'unsigned'       => TRUE,
                'auto_increment' => TRUE
            ),
            'id_scadenzario' => array(
                'type'       => 'INT',
                'constraint' => 10,
                'unsigned'   => TRUE,
            ),
            'titolo' => array(
                'type'       => 'VARCHAR',
                'constraint' => 255,
                'null'       => FALSE,
                'collation'  => 'latin1_swedish_ci',
            ),
            'chiusa' => array(
                'type'       => 'TINYINT',
                'constraint' => 1,
                'null'       => TRUE,
                'default'    => 0
            ),
            'data_creazione TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP',
        ));

        // Definizione chiave primaria
        $this->dbforge->add_key('id', TRUE);

        // Aggiunta di un indice alla colonna `id_scadenzario`
        $this->db->query('ALTER TABLE `scadenzario_chat` ADD INDEX `id_scadenzario` (`id_scadenzario`) USING BTREE');

        // Creazione della tabella
        $this->dbforge->create_table('scadenzario_chat', TRUE);

        // Aggiunta della chiave esterna manualmente per supporto MySQL avanzato
        $this->db->query('
            ALTER TABLE `scadenzario_chat`
            ADD CONSTRAINT `scadenzario_chat_ibfk_1`
            FOREIGN KEY (`id_scadenzario`)
            REFERENCES `scadenzario` (`id`)
            ON UPDATE RESTRICT
            ON DELETE RESTRICT
        ');
    }

    public function down() {
        // Rimozione della tabella in caso di rollback
        //$this->dbforge->drop_table('scadenzario_chat', TRUE);
    }
}
