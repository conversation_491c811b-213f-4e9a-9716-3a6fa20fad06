<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_utenti_table extends CI_Migration {

    public function up()
    {
        $this->dbforge->add_field(array(
            'idutente' => array(
                'type' => 'SMALLINT',
                'constraint' => 5,
                'unsigned' => TRUE,
                'auto_increment' => TRUE
            ),
            'idruolo' => array(
                'type' => 'INT',
                'constraint' => 11,
                'null' => TRUE,
            ),
            'username' => array(
                'type' => 'VARCHAR',
                'constraint' => '15',
                'null' => TRUE,
                'collation' => 'latin1_swedish_ci'
            ),
            'password' => array(
                'type' => 'VARCHAR',
                'constraint' => '32',
                'null' => TRUE,
                'collation' => 'latin1_swedish_ci'
            ),
            'email' => array(
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => TRUE,
                'collation' => 'latin1_swedish_ci'
            ),
            'nome' => array(
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => TRUE,
                'collation' => 'latin1_swedish_ci'
            ),
            'cognome' => array(
                'type' => 'VARCHAR',
                'constraint' => '50',
                'null' => TRUE,
                'collation' => 'latin1_swedish_ci'
            ),
            'datalogin' => array(
                'type' => 'DATETIME',
                'null' => TRUE,
            ),
            'datacambiopass' => array(
                'type' => 'DATETIME',
                'null' => TRUE,
            ),
            'datacreato' => array(
                'type' => 'DATETIME',
                'null' => FALSE,
                'default' => 'CURRENT_TIMESTAMP'
            ),
            'codice' => array(
                'type' => 'VARCHAR',
                'constraint' => '20',
                'null' => TRUE,
                'collation' => 'latin1_swedish_ci'
            ),
            'telefono' => array(
                'type' => 'VARCHAR',
                'constraint' => '20',
                'null' => TRUE,
                'collation' => 'latin1_swedish_ci'
            )
        ));
        
        $this->dbforge->add_key('idutente', TRUE);
        $this->dbforge->add_key('email', TRUE);
        $this->dbforge->create_table('utenti');
    }

    public function down()
    {
        $this->dbforge->drop_table('utenti');
    }
}
