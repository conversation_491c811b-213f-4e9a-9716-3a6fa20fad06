<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_scadenzario_chat_messaggi_table extends CI_Migration {

    public function up() {
        // Definizione della tabella scadenzario_chat_messaggi
        $this->dbforge->add_field(array(
            'id' => array(
                'type'           => 'INT',
                'constraint'     => 10,
                'unsigned'       => TRUE,
                'auto_increment' => TRUE
            ),
            'id_chat' => array(
                'type'       => 'INT',
                'constraint' => 10,
                'unsigned'   => TRUE,
            ),
            'id_utente' => array(
                'type'       => 'SMALLINT',
                'constraint' => 5,
                'unsigned'   => TRUE,
            ),
            'messaggio' => array(
                'type'       => 'TEXT',
                'null'       => FALSE,
                'collation'  => 'latin1_swedish_ci',
            ),
            'data_ora TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP',
        ));

        // Definizione chiave primaria
        $this->dbforge->add_key('id', TRUE);

        // Creazione della tabella
        $this->dbforge->create_table('scadenzario_chat_messaggi', TRUE);

        // Aggiunta degli indici manualmente (poiché dbforge ha limitazioni con gli indici avanzati)
        $this->db->query('ALTER TABLE `scadenzario_chat_messaggi` ADD INDEX `id_chat` (`id_chat`) USING BTREE');
        $this->db->query('ALTER TABLE `scadenzario_chat_messaggi` ADD INDEX `id_utente` (`id_utente`) USING BTREE');

        // Aggiunta delle chiavi esterne manualmente
        $this->db->query('
            ALTER TABLE `scadenzario_chat_messaggi`
            ADD CONSTRAINT `scadenzario_chat_messaggi_ibfk_1`
            FOREIGN KEY (`id_chat`)
            REFERENCES `scadenzario_chat` (`id`)
            ON UPDATE RESTRICT
            ON DELETE RESTRICT
        ');

        $this->db->query('
            ALTER TABLE `scadenzario_chat_messaggi`
            ADD CONSTRAINT `scadenzario_chat_messaggi_ibfk_2`
            FOREIGN KEY (`id_utente`)
            REFERENCES `utenti` (`idutente`)
            ON UPDATE RESTRICT
            ON DELETE RESTRICT
        ');
    }

    public function down() {
        // Rimozione della tabella in caso di rollback
        $this->dbforge->drop_table('scadenzario_chat_messaggi', TRUE);
    }
}
