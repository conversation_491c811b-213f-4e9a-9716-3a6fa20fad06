<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_notifiche extends CI_Migration {

    public function up() {
        // Define fields for 'notifiche' table
        $this->dbforge->add_field(array(
            'id' => array(
                'type' => 'INT',
                'constraint' => 11,
                'auto_increment' => true,
            ),
            'id_scadenzario' => array(
                'type' => 'INT',
                'constraint' => 10,
                'unsigned' => TRUE, // aggiunto unsigned per corrispondere alla chiave di scadenzario
            ),
            'testo' => array(
                'type' => 'TEXT',
            ),
            'destinatario' => array(
                'type' => "ENUM('admin','agente','entrambi')",
                'default' => 'admin',
            ),
            'data_invio' => array(
                'type' => 'DATETIME',
            ),
            'stato' => array(
                'type' => "ENUM('pending','inviata','fallita')",
                'default' => 'pending',
            ),
            'created_at' => array(
                'type' => 'TIMESTAMP',
                'null' => true
            ),
            'updated_at' => array(
                'type' => 'TIMESTAMP',
                'null' => true
            ),
        ));
        $this->dbforge->add_key('id', true);
        $this->dbforge->create_table('notifiche', true);

        $this->db->query("ALTER TABLE notifiche MODIFY created_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP");
        $this->db->query("ALTER TABLE notifiche MODIFY updated_at TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP");

        // Add foreign key constraint manually
        $this->db->query('ALTER TABLE notifiche ADD CONSTRAINT fk_scadenzario FOREIGN KEY (id_scadenzario) REFERENCES scadenzario(id)');
    }

    public function down() {
        $this->dbforge->drop_table('notifiche', true);
    }
}
