<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Alter_notifiche_table extends CI_Migration {

    public function up() {
        // Rinominiamo data_invio a data_programmata
        $this->dbforge->modify_column('notifiche', array(
            'data_invio' => array(
                'name' => 'data_programmata',
                'type' => 'DATETIME'
            )
        ));
        
        // Aggiungiamo nuove colonne
        $this->dbforge->add_column('notifiche', array(
            'creata_il' => array('type' => 'DATETIME'),
            'data_invio' => array('type' => 'DATETIME', 'null' => true),
            'titolo' => array('type' => 'VARCHAR', 'constraint' => 255)
        ));
    }

    public function down() {
        // Rollback
        $this->dbforge->modify_column('notifiche', array(
            'data_programmata' => array(
                'name' => 'data_invio',
                'type' => 'DATETIME'
            )
        ));
        
        $this->dbforge->drop_column('notifiche', 'creata_il');
        $this->dbforge->drop_column('notifiche', 'data_invio');
        $this->dbforge->drop_column('notifiche', 'titolo');
    }
}
