<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Migration_Create_scadenzario_table extends CI_Migration {

    public function up() {
        // Definizione della tabella scadenzario
        $this->dbforge->add_field(array(
            'id' => array(
                'type'           => 'INT',
                'constraint'     => 10,
                'unsigned'       => TRUE,
                'auto_increment' => TRUE
            ),
            'agente' => array(
                'type'       => 'VARCHAR',
                'constraint' => 5,
                'null'       => TRUE,
                'default'    => NULL,
                'collation'  => 'latin1_swedish_ci',
            ),
            'conto' => array(
                'type'       => 'VARCHAR',
                'constraint' => 10,
                'null'       => TRUE,
                'default'    => NULL,
                'collation'  => 'latin1_swedish_ci',
            ),
            'ragsoc' => array(
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'null'       => TRUE,
                'default'    => NULL,
                'collation'  => 'latin1_swedish_ci',
            ),
            'anno' => array(
                'type'    => 'YEAR',
                'null'    => TRUE,
                'default' => NULL,
            ),
            'partita' => array(
                'type'       => 'INT',
                'constraint' => 10,
                'unsigned'   => TRUE,
                'null'       => TRUE,
                'default'    => NULL,
            ),
            'scadenza' => array(
                'type'    => 'DATE',
                'null'    => TRUE,
                'default' => NULL,
            ),
            'rata' => array(
                'type'       => 'VARCHAR',
                'constraint' => 50,
                'null'       => TRUE,
                'default'    => NULL,
                'collation'  => 'latin1_swedish_ci',
            ),
            'importo' => array(
                'type'       => 'DECIMAL',
                'constraint' => '10,2',
                'null'       => TRUE,
                'default'    => NULL,
            ),
            'data' => array(
                'type'    => 'DATE',
                'null'    => TRUE,
                'default' => NULL,
            ),
            'importoscaduto' => array(
                'type'       => 'DECIMAL',
                'constraint' => '10,2',
                'null'       => TRUE,
                'default'    => NULL,
            ),
            'importoascadere' => array(
                'type'       => 'DECIMAL',
                'constraint' => '10,2',
                'null'       => TRUE,
                'default'    => NULL,
            ),
            'fonte' => array(
                'type'       => "ENUM('JG','LC')",
                'null'       => TRUE,
                'default'    => NULL,
                'collation'  => 'latin1_swedish_ci',
            ),
        ));

        // Definizione chiave primaria
        $this->dbforge->add_key('id', TRUE);

        // Creazione della tabella
        $this->dbforge->create_table('scadenzario', TRUE);

        // Impostazione dell'AUTO_INCREMENT manualmente
        $this->db->query('ALTER TABLE `scadenzario` AUTO_INCREMENT=869754');
    }

    public function down() {
        // Rimozione della tabella in caso di rollback
        $this->dbforge->drop_table('scadenzario', TRUE);
    }
}
