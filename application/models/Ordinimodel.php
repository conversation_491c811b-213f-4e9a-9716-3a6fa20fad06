<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Ordinimodel extends CI_Model
{

	public $totaleordini, $totaleinviati, $totaledainviare;

	public function getordini($idcliente = '', $limite = '')
	{
		$this->db->select('O.*, C.ragsoc, (select count(*) from ordiniprodotti OP where OP.idordine=O.idordine) as numprodotti,
			(select sum(prezzo*quantita) from ordiniprodotti OP where OP.idordine=O.idordine) as totaleordine');
		$this->db->from('ordini O');
		$this->db->join('clienti C', 'C.idcliente = O.idcliente');
		// $this->db->order_by('O.dataordine', 'desc');
		// $this->db->limit(5000);

		if ($this->session->ruolo == 'Agente') $this->db->where('O.idagente', $this->session->idutente);
		if ($idcliente)	$this->db->where('O.idcliente', $idcliente);

		if ($limite != '') {
			$this->db->limit($limite);
		}

		$rs = $this->db->get();
		$rows = $rs->result();
		return $rows;
	}

	public function getordini_ajax($postData = null)
	{

		if ($postData) {
			$draw = $postData['draw'];
			if (key_exists('idcliente', $postData)) $idcliente = $postData['idcliente'];
			else $idcliente = '';
			$start = $postData['start'];
			$rowperpage = $postData['length']; // Rows display per page
			$columnIndex = $postData['order'][0]['column']; // Column index
			$columnName = $postData['columns'][$columnIndex]['data']; // Column name
			$columnSortOrder = $postData['order'][0]['dir']; // asc or desc
			$searchValue = $postData['search']['value']; // Search value
		} else {
			$response = array();
			$draw = '';
			$idcliente = '';
			$start = 0;
			$rowperpage = 10;
			$columnIndex = '';
			$columnName = '';
			$columnSortOrder = '';
			$searchValue = '';
		}

		## Search
		$searchQuery = "";
		if ($searchValue != '') {
			$searchQuery = " (C.ragsoc like '%" . $searchValue . "%' or O.idordine like '%" . $searchValue . "%') ";
		}

		## Total number of records without filtering
		$this->db->select('count(*) as allcount');
		$this->db->from('ordini O');
		if ($this->session->ruolo == 'Agente') $this->db->where('O.idagente', $this->session->idutente);
		if ($idcliente != '')	$this->db->where('O.idcliente', $idcliente);

		$records = $this->db->get()->result();
		$totalRecords = $records[0]->allcount;

		## Total number of record with filtering
		$this->db->select('count(*) as allcount');
		$this->db->from('ordini O');
		$this->db->join('clienti C', 'C.idcliente = O.idcliente');
		if ($this->session->ruolo == 'Agente') $this->db->where('O.idagente', $this->session->idutente);
		if ($idcliente != '')	$this->db->where('O.idcliente', $idcliente);

		if ($searchQuery != '') $this->db->where($searchQuery);
		$records = $this->db->get()->result();
		$totalRecordwithFilter = $records[0]->allcount;

		## Fetch records
		$this->db->select('O.*, C.ragsoc, (select count(*) from ordiniprodotti OP where OP.idordine=O.idordine) as numprodotti,
			(select sum(prezzo*quantita) from ordiniprodotti OP where OP.idordine=O.idordine) as totaleordine');
		$this->db->from('ordini O');
		$this->db->join('clienti C', 'C.idcliente = O.idcliente');
		if ($this->session->ruolo == 'Agente') $this->db->where('O.idagente', $this->session->idutente);
		if ($idcliente != '')	$this->db->where('O.idcliente', $idcliente);

		if ($searchQuery != '')
			$this->db->where($searchQuery);
		$this->db->order_by($columnName, $columnSortOrder);
		$this->db->limit($rowperpage, $start);
		$records = $this->db->get()->result();

		$data = array();

		foreach ($records as $record) {

			$data[] = array(
				"idordine" => $record->idordine,
				"ragsoc" => $record->ragsoc,
				"dataordine" => $record->dataordine,
				"totaleordine" => $record->totaleordine,
				"datainviato" => $record->datainviato,
				"datamodifica" => $record->tipodocumento,
			);
		}

		## Response
		$response = array(
			"draw" => intval($draw),
			"iTotalRecords" => $totalRecords,
			"iTotalDisplayRecords" => $totalRecordwithFilter,
			"aaData" => $data
		);

		return $response;
	}

	/**
	 * Lista ordini inviati da oggi a $gg giorni indietro
	 * 20/7/2023 aggiuta where
	 *
	 * @param integer $gg
	 * @return array obj
	 */
	public function getOrdiniRecentiInviati($gg = 30)
	{
		$this->db->select('*');
		$this->db->from('ordini O');
		$this->db->where('(O.datainviato >= DATE(NOW() - INTERVAL ' . $gg . ' DAY))');
		$this->db->where('O.tipodocumento is null');
		$rs = $this->db->get();
		$rows = $rs->result();
		return $rows;
	}

	public function setstatordini($idcliente = '')
	{
		$ordini = $this->getordini($idcliente);
		$this->totaleordini = count($ordini);
		$totaleinviati = 0;
		foreach ($ordini as $o) {
			if ($o->datainviato != '') $totaleinviati++;
		}
		$this->totaleinviati = $totaleinviati;
		$this->totaledainviare = $this->totaleordini - $totaleinviati;
	}

	public function getordiniprodotti($idordine)
	{
		$this->db->select('OP.*, L.prezzo as prezzolistino');
		$this->db->from('ordiniprodotti OP');
		$this->db->join('listino L', 'L.codice = OP.codice', 'left');
		$this->db->where('idordine', $idordine);
		$rs = $this->db->get();
		$rows = $rs->result();
		return $rows;
	}

	public function salvaordinenuovo($data)
	{
		// var_dump($data);exit;
		$clientejson = $this->Clientimodel->getclientejson($data['idcliente']);
		// crea ordine
		$insert = array();
		$insert['idcliente'] = $data['idcliente'];
		$insert['noteagente'] = $data['noteagente'];
		$insert['idagente'] = (isset($data['idagente'])) ? $data['idagente'] : $this->session->idutente;
		$insert['cliente'] = $clientejson;

		if (isset($data['dataordine'])) $insert['dataordine'] = $data['dataordine'];
		if (isset($data['datainviato'])) $insert['datainviato'] = $data['datainviato'];
		if (isset($data['TDOCOO'])) $insert['TDOCOO'] = $data['TDOCOO'];
		if (isset($data['NROROO'])) $insert['NROROO'] = $data['NROROO'];
		if (isset($data['tipodocumento'])) $insert['tipodocumento'] = $data['tipodocumento'];

		$this->db->insert('ordini', $insert);
		$idordine = $this->db->insert_id();

		// verifico che su jg non ci sia un idcart con questo idordine
		$idordine = $this->checkidordineJG($idordine);

		// aggiungi prodotti ordinati
		$this->salvaprodottiordine($data, $idordine);

		return $idordine;
	}

	public function salvaprodottiordine($data, $idordine)
	{
		$insert = array();
		$i = 0;
		foreach ($data['codice'] as $key => $codice) {
			$prodotto = $this->Prodottimodel->getprodottojson($codice);
			$insert[$i]['idordine'] = $idordine;
			$insert[$i]['codice'] = $codice;
			$insert[$i]['descrizione'] = trim($data['descrizione'][$key]);
			$insert[$i]['prezzo'] = $data['prezzo'][$key];
			$insert[$i]['prezzoordine'] = $data['prezzoordine'][$key];
			$insert[$i]['sconto'] = ($data['sconto'][$key] > 0) ? $data['sconto'][$key] : null;
			$insert[$i]['quantita'] = $data['quantita'][$key];
			$insert[$i]['um'] = $data['um'][$key];
			$insert[$i]['note'] = $data['note'][$key];
			$insert[$i]['tiporiga'] = $data['tiporiga'][$key];
			$insert[$i]['prodotto'] = $prodotto;
			$i++;
		}
		// var_dump($insert);
		$this->db->insert_batch('ordiniprodotti', $insert);
	}

	public function salvaordinemodifica($data)
	{
		if (empty($data['idordine'])) return false;

		$idordine = $data['idordine'];
		unset($data['idordine']);

		$ordine = $this->getordine($idordine);
		if ($ordine->idagente != $this->session->idutente && $this->session->ruolo == 'Agente') return false;

		$update = array();
		$where = array();
		if (!empty($data['noteagente'])) $update['noteagente'] = $data['noteagente'];
		if ($ordine->datainviato != null) $update['datamodifica'] = date('Y-m-d H:i:s');
		if (!empty($data['TDOCOO']) && !empty($data['NROROO'])) {
			$update['TDOCOO'] = $data['TDOCOO'];
			$update['NROROO'] = $data['NROROO'];
		}

		if (count($update) > 0) $this->updateOrdine($idordine, $update);

		// echo $idordine;
		// var_dump($data);
		// $idordine=1;
		// aggiungi prodotti ordinati
		if (key_exists('codice', $data) && count($data['codice']) > 0) {
			$this->db->delete('ordiniprodotti', ['idordine' => $idordine]);
			$insert = array();
			$i = 0;
			foreach ($data['codice'] as $key => $codice) {
				$prodotto = $this->Prodottimodel->getprodottojson($codice);
				$insert[$i]['idordine'] = $idordine;
				$insert[$i]['codice'] = $codice;
				$insert[$i]['descrizione'] = trim($data['descrizione'][$key]);
				$insert[$i]['prezzo'] = $data['prezzo'][$key];
				$insert[$i]['prezzoordine'] = $data['prezzoordine'][$key];
				$insert[$i]['sconto'] = ($data['sconto'][$key] > 0) ? $data['sconto'][$key] : null;
				$insert[$i]['quantita'] = $data['quantita'][$key];
				$insert[$i]['um'] = $data['um'][$key];
				$insert[$i]['note'] = $data['note'][$key];
				$insert[$i]['tiporiga'] = $data['tiporiga'][$key];
				$insert[$i]['prodotto'] = $prodotto;
				$i++;
			}
			// var_dump($insert);
			// exit;
			$this->db->insert_batch('ordiniprodotti', $insert);
		}
	}

	public function updateOrdine($idordine, $data)
	{
		$this->db->update('ordini', $data, ['idordine' => $idordine]);
	}

	/**
	 * Elimina i prodotti ordinati e li ripopola con dati da JG
	 *
	 * @param [type] $o
	 * @param [type] $ordineJG
	 * @return void
	 */
	public function aggiorna_prodottiordinati($o, $ordineJG)
	{
		$this->db->delete('ordiniprodotti', ['idordine' => $o->idordine]); // elimino tutti i prodotti di quell'ordine
		// inserisco tutti i prodotti ordinati da JG a portale
		$arr_data = array();
		foreach ($ordineJG as $rigaordine) {
			$data = array();
			if ($rigaordine->TIMOOO != '06') { // escludo i commenti ordine
				$data['idordine'] = $o->idordine;
				$data['codice'] = trim($rigaordine->CDAROO);
				$data['descrizione'] = $rigaordine->DSAROO;
				$data['prezzo'] = floatval($rigaordine->PZNEOO); // prezzo listino
				$data['prezzoordine'] = floatval($rigaordine->PRZUOO);
				$data['sconto'] = floatval($rigaordine->SCN1OO);
				$data['quantita'] = $rigaordine->QTOROO;
				$data['um'] = $rigaordine->CDUMOO;
				$data['note'] = $this->ASOrdiniModel->getNoteArticolo($rigaordine->TDOCOO, $rigaordine->NROROO, $rigaordine->NRRGOO);
				$data['tiporiga'] = $rigaordine->TIMOOO;
				$arr_data[] = $data;
				// $this->db->insert('ordiniprodotti', $data);
			}
		}
		$this->db->insert_batch('ordiniprodotti', $arr_data);
	}

	public function eliminaordine($idordine)
	{
		$ordine = $this->getordine($idordine);
		if ($ordine->idagente == $this->session->idutente) {
			$this->db->delete('ordini', ['idordine' => $idordine]);
			$this->db->delete('ordiniprodotti', ['idordine' => $idordine]);
		}
	}

	public function getordine($idordine)
	{
		$this->db->select('*');
		$this->db->from('ordini');
		if ($this->session->ruolo == 'Agente') $this->db->where('idagente', $this->session->idutente);
		$this->db->where('idordine', $idordine);
		$rs = $this->db->get();
		$row = $rs->row();
		return $row;
	}

	public function getordine_erp($doc_erp)
	{
		list($tipo, $numero) = split_codice_erp($doc_erp);
		if ($tipo == '' or $numero == '') return false;

		$this->db->select('*');
		$this->db->from('ordini');
		if ($this->session->ruolo == 'Agente') $this->db->where('idagente', $this->session->idutente);
		$this->db->where('TDOCOO', $tipo);
		$this->db->where('NROROO', $numero);
		$rs = $this->db->get();
		$row = $rs->row();
		return $row;
	}

	public function duplicaordine($idordine)
	{
		$ordine = $this->getordine($idordine);
		$prodotti = $this->getordiniprodotti($idordine);
		$insert = array();
		$insert['idcliente'] = $ordine->idcliente;
		$insert['idagente'] = $ordine->idagente;
		$insert['cliente'] = $ordine->cliente;
		// $insert['noteagente']=$ordine->noteagente;
		$this->db->insert('ordini', $insert);
		$idordine = $this->db->insert_id();

		$idordine = $this->checkidordineJG($idordine);

		$insert = array();
		$i = 0;
		foreach ($prodotti as $prodotto) {
			$insert[$i]['idordine'] = $idordine;
			$insert[$i]['codice'] = $prodotto->codice;
			$insert[$i]['descrizione'] = $prodotto->descrizione;
			$insert[$i]['prezzo'] = $prodotto->prezzo;
			$insert[$i]['quantita'] = $prodotto->quantita;
			$insert[$i]['note'] = $prodotto->note;
			$insert[$i]['prodotto'] = $prodotto->prodotto;
			// aggiunti 8/3/22
			$insert[$i]['prezzoordine'] = $prodotto->prezzoordine;
			$insert[$i]['sconto'] = $prodotto->sconto;
			$insert[$i]['note'] = $prodotto->note;
			$insert[$i]['tiporiga'] = $prodotto->tiporiga;
			$i++;
		}
		$this->db->insert_batch('ordiniprodotti', $insert);
		return $idordine;
	}
	/**
	 * Verifica se c'è un idcart su jg con quel idordine e che non ci sia già un idordine presente sul portale
	 * se trova uno dei due, incrementa di 1 finchè trova vuoto su jg e nessun ordine con quel idordine
	 * ritorna il nuovo idordine da utilizzare
	 *
	 * @param int $idordine
	 * @return int $nuovo_idordine
	 */
	private function checkidordineJG($idordine)
	{
		$this->load->model('AS400/ASOrdiniModel');
		$oct = $this->ASOrdiniModel->getCarrelloTestata($idordine);
		if ($oct->ID_CART != '') {
			$nuovo_idordine = $idordine;
			$i = 1;
			$j = 1;
			do {
				$nuovo_idordine += $i;
				$i += $j;
				$j++;
				$oct = $this->ASOrdiniModel->getCarrelloTestata($nuovo_idordine);
				$ordine = $this->getordine($nuovo_idordine);
			} while (($oct->ID_CART != '') || ($ordine));
			$this->db->update('ordini', ['idordine' => $nuovo_idordine], ['idordine' => $idordine]);
			$idordine = $nuovo_idordine;
		}
		return $idordine;
	}

	public function aggiorna_evasione($idordiniprodotti, $qta) {
		$this->db->set('qtaevasa', 'COALESCE(qtaevasa, 0) + ' . $qta, FALSE);
		$this->db->where('id', $idordiniprodotti);
		$this->db->update('ordiniprodotti');
	}

}

/* End of file Ordinimodel.php */
/* Location: ./application/models/Ordinimodel.php */