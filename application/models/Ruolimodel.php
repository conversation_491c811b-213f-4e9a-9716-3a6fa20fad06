<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Ruolimodel extends CI_Model {
	public function getIdRuoloByNome($nome) {
		$query=$this->db->get_where('ruoli', ['ruolo'=>$nome]);
		$row=$query->row();
		if ($row) return $row->idruolo;
		else return '2'; // default educatore

	}
	public function getlistaruoli($tipo='') {
		$ruoli_rs=$this->db->get('ruoli');
		if ($tipo=='arr') $ruoli=$ruoli_rs->result_array();
		else $ruoli=$ruoli_rs->result();
		return $ruoli;
	}

	public function dropdown_ruoli($escludi=array()) {
		$ruoli_rs=$this->db->get('ruoli');
		$ruoli=$ruoli_rs->result();
		$dropdown=array();
		$dropdown[]='Tutti';
		//$dropdown['-1']='Senza ruolo';
		foreach ($ruoli as $ruolo) {
			$dropdown[$ruolo->idruolo]=$ruolo->ruolo;
		}
		foreach ($dropdown as $key=>$value) {
			if (in_array($value, $escludi)) unset($dropdown[$key]);
		}
		return $dropdown;
	}
}