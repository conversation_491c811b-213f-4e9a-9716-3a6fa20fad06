<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Prodottimodel extends CI_Model
{


    public function checkxlslistino()
    {
        $out = array();
        $campi_ok = array('codice articolo', 'descrizione #', 'prezzo listino', 'um');
        $campi_xls = getallcampi_xls('./upload/listino.xls');

        foreach ($campi_xls as $key => $value) {
            $out['campixls'][] = $value;
        }

        $campixlsok = (array_intersect($campi_xls, $campi_ok));
        $out['campinodb'] = array_diff($campi_ok, $campixlsok);

        // var_dump(array_diff($campi_ok, $campi_xls));
        // var_dump($campi_ok);
        // var_dump($campi_xls);

        return $out;
    }

    public function aggiornaprodotti()
    {
        $runfilename = 'aggiornalistino';
        $filexls = './upload/listino.xls';
        $campilistino = array('Codice Listino', 'Codice Articolo', 'Descrizione #', 'Prezzo Listino', 'UM', 'tags');
        $out = '';
        $numprodotti = 0;

        runfile_crea($runfilename);

        $this->db->truncate('listino');

        $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
        $reader->setReadDataOnly(true);
        $spreadsheet = $reader->load($filexls);
        $worksheet = $spreadsheet->getActiveSheet();
        $highestColumn = $worksheet->getHighestColumn();
        $highestRow = $worksheet->getHighestRow();
        $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

        $rigacampi = $worksheet->rangeToArray('A1:L1');
        foreach ($campilistino as $campo) {
            $posizionecampi[$campo] = array_search($campo, $rigacampi[0]) + 1;
        }
        // var_dump($rigacampi);
        // var_dump($posizionecampi);
        $set = array();
        for ($row = 2; $row <= $highestRow; ++$row) {
            if (!runfile_esiste($runfilename)) exit;
            $value = (string)$worksheet->getCellByColumnAndRow($posizionecampi['Codice Listino'], $row)->getValue();
            if ($value == 'BASE') {
                $set[] = array(
                    'codice' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Codice Articolo'], $row)->getValue(),
                    'descrizione' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Descrizione #'], $row)->getValue(),
                    'prezzo' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Prezzo Listino'], $row)->getValue(),
                    'um' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['UM'], $row)->getValue(),
                    'tags' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['tags'], $row)->getValue(),
                );
                // $this->db->replace('listino', $set);
                $numprodotti++;
            }
        }
        $this->db->insert_batch('listino', $set);

        // var_dump($set);

        runfile_elimina($runfilename);
        return $numprodotti;
    }

    public function getprodotto($codice)
    {
        $rs = $this->db->get_where('listino', ['codice' => $codice]);
        $row = $rs->row();
        return $row;
    }

    public function getprodottojson($codice)
    {
        $prodotto = $this->getprodotto($codice);
        $json = '';
        if ($prodotto) $json = json_encode($prodotto);
        return $json;
    }

    public function lista()
    {
        $rs = $this->db->get('listino');
        $rows = $rs->result();

        return $rows;
    }

    public function listaconordinati($idcliente)
    {
        $lista = $this->lista();
        $prodottiordinati = $this->Clientimodel->getprodottiordinati($idcliente);
        $codici = array();
        foreach ($prodottiordinati as $key => $p) {
            $codici[$p->codice] = $key; // codici dei prodotti ordinati
        }
        foreach ($lista as $key => $v) {
            if (array_key_exists($v->codice, $codici)) {
                $k = $codici[$v->codice];
                $dataordine = $prodottiordinati[$k]->dataordine;
                $ultimoprezzo = $prodottiordinati[$k]->prezzo;
                $ultimaqta = $prodottiordinati[$k]->quantita;
                $ultimosconto = $prodottiordinati[$k]->sconto;
                $tags = $prodottiordinati[$k]->tags;
                unset($prodottiordinati[$k]); // tolgo quelli con riscontro nel listino, alla fine rimarranno i prodotti personalizzati
            } else {
                $dataordine = '';
                $ultimoprezzo = '';
                $ultimaqta = '';
                $ultimosconto = '';
                $tags = '';
            }
            $lista[$key]->dataordine = $dataordine;
            $lista[$key]->ultimoprezzo = $ultimoprezzo;
            $lista[$key]->ultimaqta = $ultimaqta;
            $lista[$key]->ultimosconto = $ultimosconto;
            $lista[$key]->tags = $tags;
        }
        // aggiungo i prodotti personalizzati
        foreach ($prodottiordinati as $value) {
            $key++;
            $lista[$key] = new stdclass();
            $lista[$key]->codice = $value->codice;
            $lista[$key]->descrizione = $value->descrizione;
            $lista[$key]->prezzo = $value->prezzo;
            $lista[$key]->um = $value->um;
            $lista[$key]->dataordine = $value->dataordine;
            $lista[$key]->ultimoprezzo = $value->prezzo;
            $lista[$key]->ultimaqta = $value->quantita;
            $lista[$key]->ultimosconto = $value->sconto;
            $lista[$key]->tags = $value->tags;
        }

        // 20/4/2022: aggiungo i prodotti personalizzati con codice 00 001
        $personalizzati = $this->Clientimodel->getprodottipersonalizzatiordinati($idcliente);
        foreach ($personalizzati as $value) {
            $key++;
            $lista[$key] = new stdclass();
            $lista[$key]->codice = $value->codice;
            $lista[$key]->descrizione = $value->descrizione;
            $lista[$key]->prezzo = $value->prezzo;
            $lista[$key]->um = $value->um;
            $lista[$key]->dataordine = $value->dataordine;
            $lista[$key]->ultimoprezzo = $value->prezzo;
            $lista[$key]->ultimaqta = $value->quantita;
            $lista[$key]->ultimosconto = $value->sconto;
            $lista[$key]->tags = $value->tags;
        }
        // print_r($lista);exit;
        return $lista;
    }

    /**
     * Listino prodotti
     * se è presente idcliente ritorna listino con info ultimi acquisti e prodotti personalizzati
     * altrimenti il normale listino
     *
     * @param [type] $postData
     * @return void
     */
    public function listino_ajax($postData = null)
    {
        if ($postData) {
            $draw = $postData['draw'];
            $idcliente = key_exists('idcliente', $postData) ? $postData['idcliente'] : '';
            $start = $postData['start'];
            $rowperpage = $postData['length'];
            $columnIndex = $postData['order'][0]['column'];
            $columnName = $postData['columns'][$columnIndex]['data'];
            $columnSortOrder = $postData['order'][0]['dir'];
            $searchValue = $postData['search']['value'];
            $ordinatocliente = $postData['ordinatocliente'];
        } else {
            $draw = '';
            $idcliente = '';
            $start = 0;
            $rowperpage = 10;
            $columnIndex = '';
            $columnName = '';
            $columnSortOrder = '';
            $searchValue = '';
            $ordinatocliente = 0;
        }

        $searchQuery = $searchValue ? " (codice LIKE '%$searchValue%' OR descrizione LIKE '%$searchValue%' OR tags LIKE '%$searchValue%') " : '';
        $searchQueryOrdinati = $searchValue ? " (OP.codice LIKE '%$searchValue%' OR OP.descrizione LIKE '%$searchValue%') " : '';

        $totalRecords = 0;
        if ($ordinatocliente == 0) {
            $this->db->select('COUNT(*) as allcount');
            $this->db->from('listino P');
            $totalRecords = $this->db->get()->row()->allcount;
        }

        if ($idcliente != '') {
            $this->db->select('COUNT(*) as allcount');
            $this->db->from('ordiniprodotti OP');
            $this->db->join('ordini O', 'O.idordine=OP.idordine');
            $this->db->where('O.idcliente', $idcliente);
            $this->db->where('OP.codice', '00 001');
            $totalRecords += $this->db->get()->row()->allcount;
        }

        $totalRecordwithFilter = 0;
        if ($ordinatocliente == 0) {
            $this->db->select('COUNT(*) as allcount');
            $this->db->from('listino P');
            if ($searchQuery) {
                $this->db->where($searchQuery);
            }
            $totalRecordwithFilter = $this->db->get()->row()->allcount;
        }

        if ($idcliente != '') {
            $this->db->select('COUNT(*) as allcount');
            $this->db->from('ordiniprodotti OP');
            $this->db->join('ordini O', 'O.idordine=OP.idordine');
            $this->db->where('O.idcliente', $idcliente);
            $this->db->where('OP.codice', '00 001');
            if ($searchQueryOrdinati) {
                $this->db->where($searchQueryOrdinati);
            }
            $totalRecordwithFilter += $this->db->get()->row()->allcount;
        }

        $codiciordinati = [];
        if ($idcliente != '') {
            $this->db->select('codice');
            $this->db->from('ordiniprodotti OP');
            $this->db->join('ordini O', 'O.idordine=OP.idordine');
            $this->db->where('O.idcliente', $idcliente);
            $this->db->distinct();
            $rows = $this->db->get()->result();
            if ($ordinatocliente == 1) {
                $totalRecords += count($rows);
            }

            $this->db->select('codice');
            $this->db->from('ordiniprodotti OP');
            $this->db->join('ordini O', 'O.idordine=OP.idordine');
            $this->db->where('O.idcliente', $idcliente);
            if ($searchQueryOrdinati) {
                $this->db->where($searchQueryOrdinati);
            }
            $this->db->distinct();
            $rows = $this->db->get()->result();
            foreach ($rows as $codice) {
                $codiciordinati[] = $codice->codice;
            }
            if ($ordinatocliente == 1) {
                $totalRecordwithFilter += count($rows);
            }
        }

        if ($ordinatocliente == 0) {
            $this->db->select('P.codice, P.descrizione, P.prezzo, P.um, P.tags, "" as dataordine, "" as prezzoordine, "" as sconto, "" as quantita, "" as datasospeso, "" as note');
            $this->db->from('listino P');
            if (count($codiciordinati) > 0) {
                $this->db->where_not_in('codice', $codiciordinati);
            }
            $query1 = $this->db->get_compiled_select();
        }

        if ($idcliente != '') {
            $this->db->select('tags');
            $this->db->from('listino');
            $this->db->where('codice', 'OP.codice');
            $this->db->limit(1);
            $subq_tags = '(' . $this->db->get_compiled_select() . ') as tags';

            $this->db->select('descrizione, MAX(dataordine) AS ultimadata');
            $this->db->from('ordiniprodotti');
            $this->db->join('ordini', 'ordini.idordine=ordiniprodotti.idordine');
            $this->db->where('idcliente', $idcliente);
            $this->db->where('ordiniprodotti.codice', '00 001');
            $this->db->group_by('ordiniprodotti.descrizione');
            $innerjoin = '(' . $this->db->get_compiled_select() . ') as EachItem';

            $this->db->select("OP.codice, OP.descrizione, OP.prezzo, OP.um, $subq_tags, dataordine, prezzoordine, sconto, quantita, datasospeso, note");
            $this->db->from('ordiniprodotti OP');
            $this->db->join('ordini O', 'O.idordine=OP.idordine');
            $this->db->join($innerjoin, 'EachItem.ultimadata=O.dataordine AND EachItem.descrizione=OP.descrizione');
            $this->db->where('OP.codice', '00 001');
            $this->db->where('O.idcliente', $idcliente);
            $query2 = $this->db->get_compiled_select();

            $this->db->select('codice, MAX(dataordine) AS ultimadata');
            $this->db->from('ordiniprodotti');
            $this->db->join('ordini', 'ordini.idordine=ordiniprodotti.idordine');
            $this->db->where('idcliente', $idcliente);
            $this->db->where('codice!=', '00 001');
            $this->db->group_by('codice');
            $innerjoin = '(' . $this->db->get_compiled_select() . ') as EachItem';

            $this->db->select("OP.codice, IF(P.descrizione!='', P.descrizione, OP.descrizione) as descrizione, P.prezzo, P.um, $subq_tags, dataordine, prezzoordine, sconto, quantita, datasospeso, note");
            $this->db->from('ordiniprodotti OP');
            $this->db->join('ordini O', 'O.idordine=OP.idordine');
            $this->db->join($innerjoin, 'EachItem.ultimadata=O.dataordine AND EachItem.codice=OP.codice');
            $this->db->join('listino P', 'P.codice = OP.codice', 'left');
            $this->db->where('idcliente', $idcliente);
            $query3 = $this->db->get_compiled_select();

            if ($ordinatocliente == 0) {
                $this->db->from("(($query1) UNION ($query2) UNION ($query3)) as unionTable");
            } else {
                $this->db->from("(($query2) UNION ($query3)) as unionTable");
            }
        } else {
            $this->db->from("($query1) as query1");
        }

        $this->db->select('*');
        if ($searchQuery) {
            $this->db->where($searchQuery);
        }
        $campi = ['codice', 'descrizione', 'prezzo', 'um', 'dataordine', 'prezzoordine', 'sconto', 'quantita', 'datasospeso', 'note'];
        foreach ($postData["order"] as $o) {
            if (in_array($campi[$o['column']], $campi)) {
                $this->db->order_by($campi[$o['column']], $o['dir']);
            }
        }
        $this->db->limit($rowperpage, $start);

        $records = $this->db->get()->result();

        $data = [];
        foreach ($records as $record) {
            $data[] = [
                "codice" => $record->codice,
                "descrizione" => $record->descrizione,
                "prezzo" => $record->prezzo,
                "um" => $record->um,
                "dataordine" => $record->dataordine,
                "ultimoprezzo" => $record->prezzoordine,
                "ultimaqta" => $record->quantita,
                "ultimosconto" => $record->sconto,
                "datasospeso" => $record->datasospeso,
                "note" => $record->note,
            ];
        }

        $response = [
            "draw" => intval($draw),
            "iTotalRecords" => $totalRecords,
            "iTotalDisplayRecords" => $totalRecordwithFilter,
            "aaData" => $data
        ];

        return $response;
    }
}

/* End of file Prodottimodel.php */
/* Location: ./application/models/Prodottimodel.php */