<?php

defined('BASEPATH') or exit('No direct script access allowed');

class ASClientiModel extends CI_Model
{
    public function __construct()
    {
        $this->db = $this->load->database('AS400SP', true);
    }

    public function getCliente($codice_cliente)
    {
        if ($codice_cliente) {
            $codice_cliente = aggiustacodicecliente($codice_cliente);
            // echo $codice_cliente; exit;
            $rs = $this->db->query("SELECT * FROM  " . $this->db->database . ".S3KPI00F where CDCFKP ='" . $codice_cliente . "' ");
            if ($rs) $row = $rs->row();
            else $row = null;
            return $row;
        } else return false;
    }

    public function getCliente_piva($piva)
    {
        if ($piva) {
            $rs = $this->db->query("SELECT * FROM  " . $this->db->database . ".S3KPI00F where FLCH07 ='" . $piva . "' ");
            $row = $rs->row();
            return $row;
        } else return false;
    }

    public function getCliente_cf($cf)
    {
        if ($cf) {
            $rs = $this->db->query("SELECT * FROM  " . $this->db->database . ".S3KPI00F where FLCH09 ='" . $cf . "' ");
            $row = $rs->row();
            return $row;
        } else return false;
    }
}

/* End of file OrdiniModel.php */
