<?php
defined('BASEPATH') or exit('No direct script access allowed');
/**
Tracciato File: LUC90OBJ/S3SPA00F - S3SA : Visualizzazione PARTITE e RISCHI clienti su S3APP
=============================================================================================
Nome       Tipo Da    A   Lun. Cifre Dec. Descrizione
---------------------------------------------------------------------------------------------
CDCFSA     A     1    10    10            CODICE CLIENTE
DSCOSA     A    11    50    40            RAGIONE SOCIALE
LOCASA     A    51    75    25            LOCALITÀ
PROVSA     A    76    80     5            PROVINCIA
DTFTSA     P    81    85     5  8    0    DATA FATTURA
DF10SA     A    86    95    10            DATA FATTURA
NRFTSA     P    96    99     4  7    0    NUMERO FATTURA
DTSCSA     P   100   104     5  8    0    DATA SCADENZA
DS10SA     A   105   114    10            DATA SCADENZA
IMSCSA     P   115   124    10 19    6    IMPORTO SCADENZA
DSRASA     A   125   164    40            DESCRIZIONE TIPO RATA
CDPASA     A   165   167     3            PAGAMENTO FATTURA
DSPASA     A   168   207    40            DESCRIZIONE PAGAMENTO
CDAGSA     A   208   210     3            CODICE AGENTE
DSAGSA     A   211   250    40            DESCRIZIONE AGENTE
TOFASA     P   251   260    10 19    6    TOTALE FATTURA
CDCSSA     A   261   272    12            CODICE DESTINAZIONE
INTESA     A   273   312    40            INTESTAZIONE
PRVDSA     A   313   317     5            PROVINCIA
LOCDSA     A   318   342    25            Località
STATSA     A   343   362    20            STATO
FACCSA     A   363   382    20            FACCINA
TPRESA     A   383   383     1            TIPO RECORD
KPIST      A   384   393    10            STATO
 */
class ASScadenzarioModel extends CI_Model
{
    public function __construct()
    {
        $this->db = $this->load->database('AS400SP', true);
    }
    public function getScadenzario($start = 0, $limit = 10)
    {
        $rs = $this->db->query("SELECT CDAGSA, CDCFSA, YEAR(DTFTSA) as ANNO, DSCOSA, DTFTSA, NRFTSA, DTSCSA, DSRASA,
            IMSCSA, TOFASA, STATSA,
            CASE(STATSA)
                WHEN 'SCADUTO' THEN IMSCSA
                ELSE 0
            END AS IMSCADUTO
            FROM  " . $this->db->database . ".S3SPA00F
        WHERE NRFTSA>0 AND CDCFSA LIKE '0301%' order by CDCFSA, NRFTSA limit $start, $limit");
        // WHERE DTFTSA=20221222");
        // WHERE CDCFSA LIKE '%13977' order by CDCFSA, NRFTSA limit $start, $limit");
        // CDCFSA LIKE '%001158'
        $rows = $rs->result();
        return $rows;
    }
}
/* End of file ScadenzarioModel.php */
/**
 *             CASE(STATSA)
                WHEN 'SCADUTO' THEN TOFASA
                ELSE 0
            END AS IMSCADUTO,
            CASE(STATSA)
                WHEN 'SCADUTO' THEN 0
                ELSE TOFASA
            END AS IMSCSA,
 */
