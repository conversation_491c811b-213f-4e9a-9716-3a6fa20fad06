<?php

defined('BASEPATH') or exit('No direct script access allowed');

class ASProdottiModel extends CI_Model
{

    public function __construct()
    {
        $this->db = $this->load->database('AS400SP', true);
    }

    public function getProdotti()
    {
        $rs = $this->db->query("SELECT * FROM  " . $this->db->database . ".S3ART00F A WHERE SUBSTR(A.CDARAT, 1, 2)='01' LIMIT 10");
        $rows = $rs->result();
        return $rows;
    }


    public function getProdotto($codice)
    {
        $codice = urldecode($codice);
        $rs = $this->db->query("SELECT * FROM  " . $this->db->database . ".WB_PRZAPP P
            JOIN " . $this->db->database . ".S3ART00F A ON P.CDARML=A.CDARAT
            WHERE P.CDAGML=30 AND P.CLISML='BASE' AND P.CDARML='" . $codice . "'
            LIMIT 10");
        $rows = $rs->result();
        return $rows;
    }

    public function getListini($start, $limit)
    {
        // $rs = $this->db->query("SELECT P.CDARML, P.PRZLML, A.DESAAT, A.UMBASE FROM  ".$this->db->database.".WB_PRZAPP P
        // $rs = $this->db->query("SELECT * FROM  ".$this->db->database.".WB_PRZAPP P
        //     JOIN ".$this->db->database.".S3ART00F A ON P.CDARML=A.CDARAT
        //     WHERE P.CDAGML=30 AND P.CLISML='BASE'
        //     AND SUBSTR(P.CDARML, 1, 2) IN ('FF')
        //     LIMIT $start, $limit");
        $sql = "SELECT * FROM  " . $this->db->database . ".WB_PRZAPP P
        JOIN " . $this->db->database . ".S3ART00F A ON P.CDARML=A.CDARAT
        WHERE P.CDAGML=30 AND P.CLISML='BASE'
        AND SUBSTR(P.CDARML, 1, 1) IN ('0', '1', '9')
        AND SUBSTR(P.CDARML, 2, 1) IN ('0', '1', '2', '3', '4', '5', '6', '7', '8', '9') ";
        $sql .= "LIMIT $start, $limit";
        $rs = $this->db->query($sql); //WHERE CDARML='14 121'
        $rows = $rs->result();
        return $rows;
    }
}

/* End of file OrdiniModel.php */
