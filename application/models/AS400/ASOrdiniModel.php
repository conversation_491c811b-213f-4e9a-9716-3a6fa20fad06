<?php

defined('BASEPATH') or exit('No direct script access allowed');

class ASOrdiniModel extends CI_Model
{
    protected $db;

    public function __construct()
    {
        $this->db = $this->load->database('AS400SP', true);
    }
    public function addOrdine($cliente, $ordine, $prodotti, $agente)
    {

        $idcart = $ordine->idordine;
        $anac = $this->_makeANAC($cliente, $idcart);
        // var_dump($anac).'<br>\r\n';

        $ocr = $this->_makeOCR($prodotti, $idcart);
        // var_dump($ocr).'<br>\r\n';

        $oct = $this->_makeOCT($ordine, $idcart, $cliente, $agente);
        // var_dump($oct).'<br>\r\n';



        // exit;
        set_error_handler(function ($errno, $errstr, $errfile, $errline) {
            throw new ErrorException($errstr, $errno, 0, $errfile, $errline);
        });
        foreach ($ocr as $v) {
            try {
                $res = $this->_insert('S3OCR', $v);
            } catch (Exception $e) {
                echo "Errore";
                return $e->getMessage();
            }
        }
        try {
            $res = $this->_insert('S3ANAC', $anac);
        } catch (Exception $e) {
            echo "Errore";
            return $e->getMessage();
        }

        $res = $this->_insert('S3OCT', $oct);
        return '';
    }

    /**
     * Recupera i dati del carrello inviato a JG
     *
     * @param [type] $idcart
     * @param bool $solotestata query solo tabella oct
     * @return array($anac, $ocr, $oct)
     */
    public function getCarrello($idcart, $solotestata = false)
    {
        //where ID_CART='".$this->idcart."'
        if (!$solotestata) {
            $query = $this->db->query("SELECT * FROM  " . $this->db->schema . ".S3ANAC where ID_CART='" . $idcart . "' ");
            $anac = $query->result();

            $ocr = $this->getCarrelloRighe($idcart);
        } else {
            $anac = array();
            $ocr = array();
        }

        $oct = $this->getCarrelloTestata($idcart);
        return array($anac, $ocr, $oct);
    }

    /**
     * Recupera la testata dell'ordine dal carrello inviato a JG
     *
     * @param [type] $idcart
     * @return $oct
     */
    public function getCarrelloTestata($idcart)
    {
        $query = $this->db->query("SELECT ID_CART, DOC_ERP FROM  " . $this->db->schema . ".S3OCT where ID_CART='" . $idcart . "' ");
        $oct = $query->row();
        if (is_null($oct)) {
            $oct = new stdClass();
            $oct->DOC_ERP = '';
            $oct->ID_CART = '';
        } else {
            list($oct->TDOCOO, $oct->NROROO) = split_codice_erp($oct->DOC_ERP);
            // $oct->TDOCOO = substr($oct->DOC_ERP, 0, 1);
            // $oct->NROROO = trim(substr($oct->DOC_ERP, 1, -1));
        }
        return $oct;
    }

    /**
     * Recupera le righe dell'ordine dal carrello inviato a JG
     *
     * @param [type] $idcart
     * @return $ocr
     */
    public function getCarrelloRighe($idcart)
    {
        // $query = $this->db->query("SELECT OCR.*, OCT.DOC_ERP FROM  " . $this->db->schema . ".S3OCR AS OCR
        //     JOIN " . $this->db->schema . ".S3OCT AS OCT ON OCT.ID_CART=OCR.ID_CART where OCR.ID_CART='" . $idcart . "' ");
        $query = $this->db->query("SELECT * FROM  " . $this->db->schema . ".S3OCR where ID_CART='" . $idcart . "' ");
        $ocr = $query->result();
        return $ocr;
    }

    /**
     * Ordine su jGalileo visto da Luccacarta
     * Ogni record è un prodotto
     *
     * @param [type] $doc_erp
     * @return array of obj
     */
    public function getOrdineJG($doc_erp)
    {
        list($tipo_ordine, $numordine) = split_codice_erp($doc_erp);

        $tabellaOrdiniJG = $this->db->database2 . ".OCMOV01J";
        $where = "TDOCOO='$tipo_ordine' AND NROROO=$numordine";
        $orderby = "order by TIMOOO, NRRGOO asc";

        $select = "DT01OO, TDOCOO, NROROO, NRRGOO, NSRGOO, CDAROO, DSAROO, TIMOOO, CDUMOO, QTOROO, PRZUOO, PZNEOO, SCN1OO, SCN2OO, SCN3OO, SCN4OO";

        // $select = "*";
        $query = $this->db->query("SELECT $select FROM $tabellaOrdiniJG WHERE $where $orderby");
        $ordinejg = $query->result();

        $out = new stdClass();
        $out->rigaordine = $ordinejg;
        $out->noteagente = $this->_estraiNoteAgente($ordinejg);
        $out->dataordine = $ordinejg[0]->DT01OO;
        $out->TDOCOO = $ordinejg[0]->TDOCOO;
        $out->NROROO = $ordinejg[0]->NROROO;

        // var_dump($out);
        // exit;

        return $out;
    }

    public function getNoteArticolo($TDOCOC, $NROROC, $NRRGOC)
    {
        $sql = "SELECT TCOMOC FROM " . $this->db->database2 . ".OCCOM00F WHERE TDOCOC='$TDOCOC' AND NROROC='$NROROC' AND NRRGOC='$NRRGOC'";
        $query = $this->db->query($sql);
        $rows = $query->result();
        $note = '';
        foreach ($rows as $row) {
            $note .= $row->TCOMOC . ' ';
        }
        $note = utf8_decode($note);
        return $note;
    }

    private function _estraiNoteAgente($righeOrdine)
    {
        $note = '';
        foreach ($righeOrdine as $key => $value) {
            if ($value->TIMOOO == '06')
                $note .= $value->DSAROO;
        }
        return utf8_encode(trim($note));
    }

    /**
     * Prende le testate degli ordini da JG
     * in pratica fa una selecte disticnt sui campi TDOCOO.NROROO, CDC1OO, CDAGOO
     * tipodoc.numerodoc, codice cliente, codice agente
     *
     * @param string $data_inizio
     * @param string $data_fine
     * @param integer $limite
     * @return void
     */
    public function getTestateOrdiniJG($data_inizio, $data_fine, $limite)
    {
        $select = "DT01OO, TDOCOO, NROROO, CDC1OO, CDAGOO";
        $sql = "SELECT DISTINCT $select FROM  " . $this->db->database2 . ".OCMOV01J WHERE CDAGOO!='30 ' ";
        if ($data_inizio != '') {
            $sql .= " AND DT01OO>='$data_inizio' ";
        }
        if ($data_fine != '') {
            $sql .= " AND DT01OO<='$data_fine' ";
        }

        $sql .= "ORDER BY DT01OO DESC";
        if ($limite > 0) $sql .= " LIMIT $limite";

        $query = $this->db->query($sql);
        $ordinijg = $query->result();
        return $ordinijg;
    }

    /**
     * Undocumented function
     *
     * @param string $data_inizio
     * @param string $data_fine
     * @param integer $limite
     * @return void
     */
    public function getOrdiniJG($data_inizio = '', $data_fine = '', $limite = 10)
    {
        $select = "DT01OO, TDOCOO, NROROO, NRRGOO, NSRGOO, CDAROO, DSAROO, CDUMOO, QTOROO, PRZUOO, PZNLOO, PZNEOO, CDC1OO, CDAGOO";
        $sql = "SELECT $select FROM  " . $this->db->database2 . ".OCMOV01J WHERE CDAGOO!='30 ' ";
        if ($data_inizio != '') {
            $sql .= " AND DT01OO>='$data_inizio' ";
        }
        if ($data_fine != '') {
            $sql .= " AND DT01OO<='$data_fine' ";
        }

        $sql .= "ORDER BY DT01OO DESC LIMIT $limite";

        $query = $this->db->query($sql);
        $ordinijg = $query->result();
        return $ordinijg;
    }

    private function _insert($tabella, $dati)
    {
        $campi = array_keys($dati);
        $valori = array_map(function ($a) {
            // $a = addslashes($a);
            $a = str_replace("'", "''", $a);
            return "'$a'";
        }, array_values($dati));
        $sql = "INSERT INTO " . $this->db->schema . ".$tabella (" . implode(',', $campi) . ") VALUES (" . implode(',', $valori) . ")";
        $res = $this->db->query($sql);

        return $res;
    }

    private function _makeANAC($cliente, $idcart)
    {
        $this->load->model('AS400/ASClientiModel');
        $as_cliente = $this->ASClientiModel->getCliente($cliente->codice);
        $codice = aggiustacodicecliente($cliente->codice);
        $object = array(
            'ID_CART' => $idcart,
            'COD_WEB' => $codice,
            'COD_SOG' => $codice,
            'TP_SOG' => 'C',
            'PIVA' => $cliente->piva,
            'CODFIS' => $cliente->cf,
        );
        if (is_null($as_cliente)) {
            $object['RAGSOC'] = $cliente->ragsoc;
            $object['INDIRI'] = $cliente->indirizzo;
            $object['CAP'] = $cliente->cap;
            $object['LOCALI'] = $cliente->citta;
            $object['PROV'] = $cliente->provincia;
            $object['NZN'] = 'Italia';
            $object['TEL'] = $cliente->telefono;
            $object['EMAIL'] = $cliente->email;
            $object['CODAGE'] = $cliente->idagente;
            $object['NOTE'] = 'Altra destinazione: ' . $cliente->altradestinazione .
                "\r\nPagamento: " . $cliente->pagamento .
                "\r\nTipoDocumento: " . $cliente->tipodocumento .
                "\r\nSDI: " . $cliente->sdi .
                "\r\nPEC: " . $cliente->pec .
                "\r\nEsenteSDI-PEC: " . $cliente->esentesdipec;
        } else {
        }
        return $object;
    }

    private function _makeOCR($prodotti, $idcart)
    {
        $o = array();
        $i = 0;
        foreach ($prodotti as $v) {
            $o[$i]['ID_CART'] = $idcart;
            $o[$i]['NUM_RIG'] = $i + 1;
            $o[$i]['COD_ART'] = $v->codice;
            $o[$i]['DES_ART'] = $v->descrizione;
            $o[$i]['PRZ'] = $v->prezzoordine;
            $o[$i]['PRZN'] = $v->prezzo;
            if ($v->tiporiga == '04') $o[$i]['QTA_OM'] = $v->quantita;
            else $o[$i]['QTA'] = $v->quantita;
            $o[$i]['UM'] = $v->um;
            $o[$i]['TP_RIGA'] = $v->tiporiga;
            if ($v->sconto > 0) {
                $o[$i]['SCN_1'] = $v->sconto;
                $o[$i]['FLG_SCN'] = '%';
            }
            $o[$i]['PROM'] = '0';
            $o[$i]['NT_RIG'] = $v->note;

            $i++;
        }
        return $o;
    }

    private function _makeOCT($ordine, $idcart, $cliente, $agente)
    {
        $dataordine = substr(str_replace(['-', ' ', ':'], '', $ordine->dataordine), 0, 8);
        $o = array(
            'ID_CART' => $idcart,
            'DOC_WEB' => $idcart,
            // 'DOC_ERP' => 'W' . $idcart,
            'DT_INS' => $dataordine,
            'DT_RIF' => $dataordine,
            'UT_INS' => $agente->codice,
            'TP_DOC' => 'W',
            'RIF_CLI' => aggiustacodicecliente($cliente->codice),
            'NOTE' => $ordine->noteagente,
        );
        return $o;
    }

    public function rollback($idcart)
    {
        $tabelle = array('S3ANAC', 'S3OCR', 'S3OCT');
        foreach ($tabelle as $tabella) {
            $sql = "delete from " . $this->db->schema . ".$tabella where ID_CART='" . $idcart . "'";
            $this->db->query($sql);
            echo "deleted from $tabella<br>";
        }
    }

    /**
     * Invia l'ordine a luccacarta/jgalileo
     * @param string $idcart
     * @return void
     */
    public function inviaCarrello($idcart)
    {
        // $db=$this->load->database('AS400SP', true);

        $codice_cliente = '';
        $codice_destinatario = '';
        $codice_destinazione = '';
        $tipo_ordine = '';
        $doc_ordine = '';
        $data_conferma = '';
        $messaggio = '';
        $status = '';

        // da manualistica test: CALL TLU90OBJ.CARRELLO (?, ? , ? , ? , ? , ? , ?, ?, ?)
        $sql = "CALL " . $this->db->schema . ".CARRELLO (? , ? , ? , ? , ? , ? , ?, ?, ?)";
        $query = $this->db->query($sql, array($idcart, $codice_cliente, $codice_destinatario, $codice_destinazione, $tipo_ordine, $doc_ordine, $data_conferma, $messaggio, $status));
    }

    // /**
    //  * Usata solo per testing
    //  *
    //  * @return void
    //  */
    // public function queryCarrello()
    // {
    //     // "10 160            "
    //     $sql = "SELECT * FROM TLU90DAT.OCMOV01J WHERE TDOCOO='W' ORDER BY DT01OO DESC limit 10"; //WHERE CDAROO='15 019'   WHERE TDOCOO='W'
    //     $query = $this->db->query($sql);
    //     $a = $query->result();
    //     return $a;
    // }
}

/* End of file OrdiniModel.php */
