<?php

defined('BASEPATH') or exit('No direct script access allowed');

class ASBolleFattureModel extends CI_Model
{
    protected $db;

    public function __construct()
    {
        $this->db = $this->load->database('AS400SP', true);
    }

    // public function getTestate($numerobolla = 0, $numerofattura = 0, $codicecliente = 0)
    public function getTestate($parametri=array())
    {
        // estrapola le variabili dall'array parametri
        extract($parametri);

        $this->db = $this->load->database('AS400SP', true);
        $campi = array(
            'T.DT01FM as DataImmissioneRec',
            'T.CDDTFM as CodiceDitta',
            'T.CDAGFM as CodiceAgente',
            'T.TDOCFM as TipoDocumento',
            'T.NRDFFM as NumeroDocumentoFatturaz',
            'T.CDCFFM as CodiceCliente',
            'T.DTBOFM as DataBolla',
            'T.NRBOFM as NumeroBolla',
            'T.DTFTFM as DataFattura',
            'T.NRFTFM as NumFattura',
            'T.NRDFFM'
        );
        $sql = "SELECT " . implode(',', $campi) . " FROM  " . $this->db->database2 . ".FTMOV01U as T";
        // $sql = "SELECT * FROM  " . $this->db->database2 . ".FTMOV01U as T";
        $sql .= " WHERE T.NRRGFM=0 AND T.TDOCFM!='C'";

        $limit=20;

        if(!empty($dataInizio) and !empty($dataFine)) {
            $sql .= " AND T.DT01FM BETWEEN '$dataInizio' AND '$dataFine'";
            $limit=0;
        }
        if (!empty($CDDTFM)) $sql .= " AND T.CDDTFM='$CDDTFM'";
        if (!empty($TDOCFM)) $sql .= " AND T.TDOCFM='$TDOCFM'";
        if (!empty($NRDFFM)) $sql .= " AND T.NRDFFM='$NRDFFM'";

        if (!empty($numerobolla)) $sql .= " AND T.NRBOFM=$numerobolla";
        if (!empty($numerofattura)) $sql .= " AND T.NRFTFM=$numerofattura";
        if (!empty($codicecliente)) {
            $codicecliente = aggiustacodicecliente($codicecliente);
            $sql .= " AND T.CDCFFM=$codicecliente";
        }

        $sql .= " ORDER BY T.DT01FM desc";
        if ($limit>0) $sql .= " LIMIT $limit";
        // echo "$sql<br>";
        $query = $this->db->query($sql);
        $res = $query->result();
        return $res;
    }

    public function getRighe($CDDTFM, $TDOCFM, $NRDFFM)
    {
        $this->db = $this->load->database('AS400SP', true);
        $campi = array(
            'R.TPORFM', // tipo documento
            'R.TPORFM AS TDOCOO',
            'R.NRORFM',
            'R.NRORFM AS NROROO',
            'R.NRRGFM', // numero riga
            'R.NRRGFM AS NRRGOO', // numero riga
            'R.NRGOFM', // numero riga ?
            'R.TIMOFM',
            'R.TIMOFM AS TIMOOO', // tipo movimento
            'R.CDARFM',
            'R.CDARFM AS CDAROO', // codice articolo
            'R.DSARFM',
            'R.DSARFM AS DSAROO', // descrizione
            'R.CDUMFM',
            'R.CDUMFM AS CDUMOO', // unità di misura
            'R.QTFTFM',
            'R.QTFTFM AS QTOROO', // quantità
            'R.PZNEFM1 AS PRZUOO', // prezzo netto val base (?)
            'R.PZNEFM', // prezzo netto
            'PZNLFM AS PZNEOO', // prezzo listino
            'R.NRDFFM', // numero doc fatturazione
            'R.SCN1FM',
            'R.SCN1FM AS SCN1OO',
        );
        $sql = "SELECT " . implode(',', $campi) . " FROM  " . $this->db->database2 . ".FTMOV02U as R";
        // $sql = "SELECT * FROM  " . $this->db->database2 . ".FTMOV02U AS R";
        $sql .= " WHERE NRRGFM>0 AND R.CDDTFM='$CDDTFM' AND R.TDOCFM='$TDOCFM' AND R.NRDFFM='$NRDFFM'";

        // $sql .= " ORDER BY T.DT01FM desc";
        // echo "$sql<br>";
        $query = $this->db->query($sql);
        $res = $query->result();
        // var_dump($res);
        return $res;
    }

    /**
     * Ritorna le bolle/fatture che non hanno un ordine web associato (dal portale)
     * Queste serviranno per creare un ordine sul portale
     * @return void
     */
    public function getBFSenzaOrdini() {
        $this->db = $this->load->database('AS400SP', true);
        // cerco i documenti senza ordine web dalle righe
        // problema: se una bolla è creata da ordine web e poi aggiungono prodotti, TPORFM è " " e la query mi restituisce anche quelle e non va bene
        $campi = array(
            'R.CDDTFM',
            'R.TDOCFM',
            'R.NRDFFM',
            'R.TPORFM',
            'T.DT01FM',
        );
        $sql = "SELECT DISTINCT " . implode(',', $campi) . " FROM  " . $this->db->database2 . ".FTMOV02U as R";
        $sql .= " JOIN ".$this->db->database2 . ".FTMOV01U as T ON T.CDDTFM=R.CDDTFM AND T.TDOCFM=R.TDOCFM AND T.NRDFFM=R.NRDFFM";
        $sql .= " WHERE T.NRRGFM=0 AND T.TDOCFM!='C' AND R.NRRGFM>0 AND R.TPORFM!='W'";
        $sql .= " ORDER BY T.DT01FM desc";
        $sql .= " LIMIT 10";

        $query = $this->db->query($sql);
        $res = $query->result();
        var_dump($res);

        // cerco le testate relative
        $testate=[];
        foreach ($res as $rigabf) {
            $t = $this->getTestate([
                'CDDTFM'=> $rigabf->CDDTFM,
                'TDOCFM'=> $rigabf->TDOCFM,
                'NRDFFM'=> $rigabf->NRDFFM
            ]);
            $testate[]=$t;
        }

        // cerco le righe di ogni testata e creo array finale combinato
        $bf=[];
        foreach ($testate as $t) {
            $testata=$t[0];
            $r = $this->getRighe($testata->CODICEDITTA, $testata->TIPODOCUMENTO, $testata->NUMERODOCUMENTOFATTURAZ);
            $bf[]=array('testata' => $testata, 'righe' => $r);
        }

        return $bf;
    }

    /**
     * Cerca bolle/fatture che hanno una riga con docerp passato, limit=1 per prendere una riga
     * dalle info ricavate si va a cercare la testata e recuperarla
     *
     * @param [type] $docerp
     * @return void
     */
    public function getBolleFatture_erp($docerp) {
        if (empty($docerp)) return;

        list($tipodocumento, $numerodocumento)=split_codice_erp($docerp);
        # dalle righe si cercano info sulla testata, poi si recupera testata e info
        $this->db = $this->load->database('AS400SP', true);
        $campi = array(
            'R.CDDTFM',
            'R.TDOCFM',
            'R.NRDFFM',
        );
        $sql = "SELECT " . implode(',', $campi) . " FROM  " . $this->db->database2 . ".FTMOV02U as R";
        // $sql = "SELECT * FROM  " . $this->db->database2 . ".FTMOV02U AS R";
        $sql .= " WHERE NRRGFM>0";
        $sql .= " AND R.TPORFM='$tipodocumento' AND R.NRORFM='$numerodocumento'";

        $sql .= " LIMIT 1";
        // echo "$sql<br>";
        $query = $this->db->query($sql);
        $res = $query->result();

        var_dump($res);

    }

public function getRigheByOrdineERP($numeroOrdineERP) {
    // Recupera le righe delle bolle e fatture
    $campi = array(
        'R.CDDTFM',
        'R.TDOCFM',
        'R.NRDFFM',
        'R.TPORFM',
        'R.NRORFM',
        'R.NRRGFM',
        'R.TIMOFM',
        'R.CDARFM',
        'R.DSARFM',
        'R.CDUMFM',
        'R.QTFTFM',
        'R.PZNEFM1',
        'R.PZNEFM',
        'R.PZNLFM',
        'R.SCN1FM'
    );

    $sql = "SELECT " . implode(',', $campi) . " FROM " . $this->db->database2 . ".FTMOV02U as R";
    $sql .= " WHERE R.NRRGFM > 0 AND R.TPORFM = 'W' AND R.NRORFM = ?";
    $sql .= " ORDER BY R.NRDFFM, R.NRRGFM";

    $query = $this->db->query($sql, array($numeroOrdineERP));
    $righe_bolle_fatture = $query->result();

    return $righe_bolle_fatture;
}

public function getRigheByOrdineERP_old($numeroOrdineERP) {
    // Recupera le righe delle bolle e fatture
    $campi = array(
        'R.CDDTFM',
        'R.TDOCFM',
        'R.NRDFFM',
        'R.TPORFM',
        'R.NRORFM',
        'R.NRRGFM',
        'R.TIMOFM',
        'R.CDARFM',
        'R.DSARFM',
        'R.CDUMFM',
        'R.QTFTFM',
        'R.PZNEFM1',
        'R.PZNEFM',
        'R.PZNLFM',
        'R.SCN1FM'
    );

    $sql = "SELECT " . implode(',', $campi) . " FROM " . $this->db->database2 . ".FTMOV02U as R";
    $sql .= " WHERE R.NRRGFM > 0 AND R.TPORFM = 'W' AND R.NRORFM = ?";
    $sql .= " ORDER BY R.NRDFFM, R.NRRGFM";

    $query = $this->db->query($sql, array($numeroOrdineERP));
    $righe_bolle_fatture = $query->result();

    // Recupera l'ordine dal database del portale
    $this->load->model('Ordinimodel');
    $ordine = $this->Ordinimodel->getordine_erp('W' . $numeroOrdineERP);

    if ($ordine) {
        // Recupera i prodotti dell'ordine
        $prodotti_ordine = $this->Ordinimodel->getordiniprodotti($ordine->idordine);
    } else {
        $prodotti_ordine = array();
    }

    // Combina i risultati
    $risultato = array(
        'righe_bolle_fatture' => $righe_bolle_fatture,
        'prodotti_ordine' => $prodotti_ordine
    );

    return $risultato;
}



}
