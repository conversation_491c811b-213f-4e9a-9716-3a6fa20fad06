<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Scadenzariomodel extends CI_Model
{

	public function checkxls()
	{
		$out = array();
		$campi_ok = array('agente', 'conto');
		$campi_xls = getallcampi_xls('./upload/scadenzario.xlsx', 'xlsx');

		foreach ($campi_xls as $key => $value) {
			$out['campixls'][] = $value;
		}

		$campixlsok = (array_intersect($campi_xls, $campi_ok));
		$out['campinodb'] = array_diff($campi_ok, $campixlsok);

		// var_dump(array_diff($campi_ok, $campi_xls));
		// var_dump($campi_ok);
		// var_dump($campi_xls); exit;

		return $out;
	}

	public function aggiornascadenzario()
	{
		$runfilename = 'aggiornascadenzario';
		$filexls = './upload/scadenzario.xlsx';
		// $campiscadenzario = array('<PERSON><PERSON>', 'Con<PERSON>', 'Ragione Sociale', 'Anno', '<PERSON><PERSON>', 'Scadenza', 'Rata', 'Importo #', 'Data', 'Importo <PERSON>uto', 'Importo a Scadere');
		$out = '';
		$numrighe = 0;

		runfile_crea($runfilename);

		$this->db->delete('scadenzario', ['fonte' => 'LC']);

		$reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
		$reader->setReadDataOnly(true);
		$spreadsheet = $reader->load($filexls);
		$worksheet = $spreadsheet->getActiveSheet();
		$highestColumn = $worksheet->getHighestColumn();
		$highestRow = $worksheet->getHighestRow();
		$highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

		// $rigacampi = $worksheet->rangeToArray('A1:K1');
		// foreach ($campiscadenzario as $campo) {
		// 	$posizionecampi[$campo] = array_search($campo, $rigacampi[0]) + 1;
		// }
		// var_dump($rigacampi);
		// var_dump($posizionecampi);
		// exit;
		$set = array();
		for ($row = 1; $row <= $highestRow; ++$row) {
			if (!runfile_esiste($runfilename)) exit;
			$value = (string)$worksheet->getCellByColumnAndRow(1, $row)->getValue();
			if ($value != '') {
				$datascadenza = datamysql((string)$worksheet->getCellByColumnAndRow(6, $row)->getValue());
				$set[] = array(
					'agente' => $value,
					'conto' => substr(trim((string)$worksheet->getCellByColumnAndRow(2, $row)->getValue()), -5),
					'ragsoc' => (string)$worksheet->getCellByColumnAndRow(3, $row)->getValue(),
					'anno' => (string)$worksheet->getCellByColumnAndRow(4, $row)->getValue(),
					'partita' => (string)$worksheet->getCellByColumnAndRow(5, $row)->getValue(),
					'scadenza' => $datascadenza,
					'rata' => (string)$worksheet->getCellByColumnAndRow(7, $row)->getValue(),
					'importo' => (string)$worksheet->getCellByColumnAndRow(8, $row)->getValue(),
					'data' => datamysql((string)$worksheet->getCellByColumnAndRow(9, $row)->getValue()),
					'importoscaduto' => (string)$worksheet->getCellByColumnAndRow(10, $row)->getValue(),
					'importoascadere' => (string)$worksheet->getCellByColumnAndRow(11, $row)->getValue(),
					'fonte' => 'LC'
				);
				// $this->db->insert('listino', $set);
				$numrighe++;
			}
		}
		$this->db->insert_batch('scadenzario', $set);

		// var_dump($set);

		runfile_elimina($runfilename);
		// exit;
		return $numrighe;
	}

	/**
	 * Scadenzari
	 *
	 * @param string $codicecliente Codice cliente
	 * @return arayobj $scadenze
	 */
	public function getscadenzari($codicecliente = '')
	{
		$agenti = $this->Agentimodel->getagenti();
		$scadenze = array();
		$i = 1;
		foreach ($agenti as $a) {
			if ($this->session->ruolo == 'Agente' && $a->idutente != $this->session->idutente) {
				continue;
			}
			if ($a->codice != '') {
				$codici = explode(';', $a->codice);
				foreach ($codici as $codice) {
					$this->db->select('S.*, C.*, S.ragsoc, date_format(S.data, "%d-%m-%Y") as dataita, date_format(S.scadenza, "%d-%m-%Y") as scadenzaita, S.id as idscadenzario, IF(SP.conto is not null, 1, 0) as pagato');
					$this->db->from('scadenzario S');
					// $this->db->join('utenti U', 'U.codice = S.agente');
					$this->db->join('clienti C', 'C.codice = S.conto', 'left');
					$this->db->join('scadenzariopagato SP', 'SP.conto = S.conto AND SP.partita=S.partita AND SP.scadenza=S.scadenza', 'left');

					// $this->db->limit(20);
					// $this->db->where('SP.conto', null);
					$this->db->where('S.agente', $codice);
					// if ($codicecliente != '') $this->db->where('C.codice', $codicecliente);
					if ($codicecliente != '') $this->db->where('S.conto', $codicecliente);
					$this->db->order_by('S.conto', 'asc');
					$this->db->order_by('data', 'asc');
					$this->db->order_by('partita', 'asc');

					// echo $this->db->get_compiled_select();
					// exit;

					$rs = $this->db->get();
					$rows = $rs->result();
					foreach ($rows as $k => $r) {
						$rows[$k]->nome = $a->nome . ' ' . $a->cognome;
						$rows[$k]->idutente = $a->idutente;
					}
					// echo $codice."<br>";
					$scadenze = array_merge($rows, $scadenze);
					// var_dump($scadenze);
				}
			}
			$i++;
			// if ($i>=12) break;
		}
		// exit;


		// $rs=$this->db->get();
		// $rows=$rs->result();
		// usort($scadenze, function($a, $b){
		// 	return strcmp($a->conto, $b->conto);
		// });

		return $scadenze;
	}

	public function getscadenzario($id)
	{
		$this->db->select('S.*, TRIM(S.agente) as agente, C.*, S.ragsoc, date_format(S.data, "%d-%m-%Y") as dataita, date_format(S.scadenza, "%d-%m-%Y") as scadenzaita, date_format(SP.datapagato, "%d-%m-%Y") as datapagatoita, IF(SP.conto is not null, 1, 0) as pagato');
		$this->db->from('scadenzario S');
		$this->db->join('scadenzariopagato SP', 'SP.conto = S.conto AND SP.partita=S.partita AND SP.scadenza=S.scadenza', 'left');
		$this->db->join('clienti C', 'C.codice = S.conto', 'left');
		$this->db->where('S.id', $id);
		$rs = $this->db->get();
		$row = $rs->row();
		return $row;
	}

	public function getscadenzarioByFields($conto, $partita, $scadenza)
	{
		$this->db->select('S.*, TRIM(S.agente) as agente, C.*, S.ragsoc, date_format(S.data, "%d-%m-%Y") as dataita, date_format(S.scadenza, "%d-%m-%Y") as scadenzaita, date_format(SP.datapagato, "%d-%m-%Y") as datapagatoita, IF(SP.conto is not null, 1, 0) as pagato');
		$this->db->from('scadenzario S');
		$this->db->join('scadenzariopagato SP', 'SP.conto = S.conto AND SP.partita=S.partita AND SP.scadenza=S.scadenza', 'left');
		$this->db->join('clienti C', 'C.codice = S.conto', 'left');
		$this->db->where('S.conto', $conto);
		$this->db->where('S.partita', $partita);
		$this->db->where('S.scadenza', $scadenza);
		$rs = $this->db->get();
		$row = $rs->row();
		return $row;
	}

	public function getScadenzarioByCliente($codice_cliente)
	{
		$this->db->select('S.*, TRIM(S.agente) as agente, C.*, S.ragsoc, date_format(S.data, "%d-%m-%Y") as dataita, date_format(S.scadenza, "%d-%m-%Y") as scadenzaita, date_format(SP.datapagato, "%d-%m-%Y") as datapagatoita, IF(SP.conto is not null, 1, 0) as pagato');
		$this->db->from('scadenzario S');
		$this->db->join('scadenzariopagato SP', 'SP.conto = S.conto AND SP.partita=S.partita AND SP.scadenza=S.scadenza', 'left');
		$this->db->join('clienti C', 'C.codice = S.conto', 'left');
		$this->db->where('S.conto', $codice_cliente);
		$rs = $this->db->get();
		return $rs->result(); // nota: ritorna un array di risultati, dato che potrebbe esserci più di uno scadenzario per cliente
	}


	public function setpagato($id)
	{
		$sc = $this->getscadenzario($id);
		$data = array(
			'conto' => $sc->conto,
			'partita' => $sc->partita,
			'scadenza' => $sc->scadenza,
			'datapagato' => date('Y-m-d'),
			'idutente' => $this->session->idutente
		);
		$this->db->replace('scadenzariopagato', $data);
	}
}

/* End of file Scadenzariomodel.php */
/* Location: ./application/models/Scadenzariomodel.php */
