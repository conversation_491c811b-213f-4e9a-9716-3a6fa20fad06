<?php
defined('BASEPATH') or exit('No direct script access allowed');

class ScadenzarioChatModel extends CI_Model
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model('Agentimodel');
        $this->load->library('email');
    }

    private function _configureEmail($recipientEmail, $mittente)
    {
        $config = [
            'mailtype' => 'html',
            'charset'  => 'utf-8',
            'newline'  => "\r\n"
        ];
        $this->email->initialize($config);
        $this->email->from($mittente->email, 'Notifiche Scadenzario ('.$mittente->nome.' '.$mittente->cognome.')');
        if (ENVIRONMENT === 'development') {
            $this->email->to('<EMAIL>');
        } else {
            $this->email->to($recipientEmail);
        }
        $this->email->reply_to('<EMAIL>', 'Notifiche Scadenzario');
    }

    private function _filtraPerAgente()
    {
        if ($this->session->ruolo != 'Admin') {
            $this->db->group_start();
            $agent_codes = explode(';', $this->session->userdata('codice'));
            foreach ($agent_codes as $code) {
                $this->db->or_where("s.agente", $code);
            }
            $this->db->group_end();
        }
    }

    private function _baseQueryChat()
    {
        $this->db->select('sc.*, TRIM(s.agente) as agente, s.ragsoc as scadenzario_ragsoc, s.conto, s.partita, s.scadenza');
        $this->db->from('scadenzario_chat sc');
        $this->db->join('scadenzario s', 's.conto = sc.conto AND s.partita = sc.partita AND s.scadenza = sc.scadenza', 'left');
    }

    public function getChats()
    {
        $this->_baseQueryChat();
        $this->db->select('(SELECT GROUP_CONCAT(DISTINCT CONCAT(u.nome, " ", u.cognome) SEPARATOR ", ")
             FROM scadenzario_chat_messaggi scm 
             JOIN utenti u ON u.idutente = scm.id_utente 
             WHERE scm.id_chat = sc.id) as interlocutori');
        
        $this->_filtraPerAgente();
        $this->db->order_by('sc.data_creazione', 'DESC');
        
        $chats = $this->db->get()->result();

        // Process agent names using PHP
        foreach ($chats as &$chat) {
            if (!empty($chat->agente)) {
                $agent_codes = explode(';', $chat->agente);
                $agent_names = [];
                foreach ($agent_codes as $code) {
                    $agent = $this->Agentimodel->getagentebycodice(trim($code));
                    if ($agent) {
                        $agent_names[] = $agent->nome . ' ' . $agent->cognome;
                    }
                }
                $chat->nome_agente = implode(', ', $agent_names);
            } else {
                $chat->nome_agente = '';
            }
        }
        
        return $chats;
    }

    public function chatEsistentePerScadenzario($conto, $partita, $scadenza)
    {
        $this->db->where('conto', $conto);
        $this->db->where('partita', $partita);
        $this->db->where('scadenza', $scadenza);
        $this->db->where('chiusa', 0); // Solo chat aperte
        $esistente = $this->db->get('scadenzario_chat')->row();
        
        return $esistente ? true : false;
    }

    public function creaChat($scadenzario, $titolo, $responsabile_predefinito = null)
    {
        $data = [
            'conto' => $scadenzario->conto,
            'partita' => $scadenzario->partita,
            'scadenza' => $scadenzario->scadenza,
            'titolo' => $titolo,
            'chiusa' => 0,
            'data_creazione' => date('Y-m-d H:i:s'),
            'responsabile_predefinito' => $responsabile_predefinito
        ];

        $this->db->insert('scadenzario_chat', $data);
        return $this->db->insert_id();
    }

    public function getChatDettaglio($id_chat)
    {
        $this->_baseQueryChat();
        $this->db->where('sc.id', $id_chat);
        $this->_filtraPerAgente();
        
        return $this->db->get()->row();
    }

    public function toggleChatStatus($id_chat)
    {
        // Get current status and chat details
        $chat = $this->getChatDettaglio($id_chat);
        $current_status = $chat->chiusa;

        // Toggle status
        $new_status = $current_status ? 0 : 1;
        
        // Prepare update data
        $update_data = [
            'chiusa' => $new_status,
            'data_chiusura' => $new_status ? date('Y-m-d H:i:s') : null
        ];

        // Update status and closing date
        $this->db->where('id', $id_chat);
        $this->db->update('scadenzario_chat', $update_data);
        if ($new_status == 1) {
            $this->notificaChatChiusa($id_chat);
        }
    }

    private function notificaChatChiusa($id_chat)
    {
        $destinatari = $this->getDestinatariNotifica($id_chat);
        $chat = $this->getChatDettaglio($id_chat);
        foreach ($destinatari as $destinatario) {
            if ($destinatario->idutente == $this->session->idutente) {
                continue;
            }
            $this->_configureEmail($destinatario->email);
            $this->email->reply_to($this->session->email, $this->session->nome);
            $this->email->subject("[SCADENZARIO] Chat chiusa: {$chat->titolo}");
            $body = "<p>La chat <strong>{$chat->titolo}</strong> è stata chiusa.</p>";
            $body .= "<p>Per vedere i dettagli, clicca su questo <a href='" . base_url("ScadenzarioChat/dettaglio/{$id_chat}") . "'>link</a>.</p>";
            $this->email->message($body);
            $this->email->send();
        }
    }

    public function getMessaggiChat($id_chat)
    {
        $this->db->select('scm.*, u.nome, u.cognome');
        $this->db->from('scadenzario_chat_messaggi scm');
        $this->db->join('utenti u', 'u.idutente = scm.id_utente', 'left');
        $this->db->where('scm.id_chat', $id_chat);
        $this->db->order_by('scm.data_ora', 'ASC');

        $query = $this->db->get();
        return $query->result();
    }

    public function inviaMessaggio($id_chat, $messaggio)
    {
        $data = [
            'id_chat' => $id_chat,
            'id_utente' => $this->session->idutente,
            'messaggio' => $messaggio
        ];

        $result = $this->db->insert('scadenzario_chat_messaggi', $data);
        
        if($result) {
            $this->inviaNotifiche($id_chat, $messaggio);
        }
        
        return $result;
    }

    private function inviaNotifiche($id_chat, $messaggio)
    {
        $destinatari = $this->getDestinatariNotifica($id_chat);
        $chat = $this->getChatDettaglio($id_chat);
        $mittente = $this->db->get_where('utenti', ['idutente' => $this->session->idutente])->row();
        
        foreach($destinatari as $destinatario) {
            if($destinatario->email && $destinatario->idutente != $this->session->idutente) {
                $this->inviaNotificaEmail($destinatario, $chat, $mittente, $messaggio, $id_chat);
            }
        }
    }

    public function getDestinatariNotifica($id_chat)
    {
        $destinatari = [];
        
        // 1. Recupera i partecipanti attivi alla chat
        $partecipanti = $this->getPartecipantiChat($id_chat);
        foreach ($partecipanti as $partecipante) {
            if ($partecipante->idutente != $this->session->idutente && $partecipante->email) {
                $destinatari[] = $partecipante;
            }
        }
        
        // Se non ci sono partecipanti, gestisci casi specifici
        if(empty($destinatari)) {
            $chat = $this->getChatDettaglio($id_chat);
            if ($this->session->ruolo === 'Admin') {
                // Se l'utente è Admin, usa l'agente associato allo scadenzario
                $this->load->model('Scadenzariomodel');
                $scadenzario = $this->Scadenzariomodel->getscadenzarioByFields($chat->conto, $chat->partita, $chat->scadenza);
                if (!empty($scadenzario->agente)) {
                    $agent = $this->Agentimodel->getagentebycodice(trim($scadenzario->agente));
                    if ($agent && $agent->email && $agent->idutente != $this->session->idutente) {
                        $destinatari[] = $agent;
                    }
                }
            } else {
                // Se l'utente è Agente, utilizza il campo responsabile_predefinito salvato nella chat
                if (!empty($chat->responsabile_predefinito)) {
                    $admin = $this->db->get_where('utenti', ['idutente' => $chat->responsabile_predefinito])->row();
                    if ($admin && $admin->email) {
                        $destinatari[] = $admin;
                    }
                }
            }
        }
        
        // 2. Recupera gli agenti collegati allo scadenzario
        $chat = $this->getChatDettaglio($id_chat);
        $this->load->model('Scadenzariomodel');
        $scadenzario = $this->Scadenzariomodel->getscadenzarioByFields($chat->conto, $chat->partita, $chat->scadenza);
        
        if ($scadenzario && !empty($scadenzario->agente)) {
            $agenti_codici = explode(';', $scadenzario->agente);
            foreach ($agenti_codici as $codice) {
                $agente = $this->Agentimodel->getagentebycodice(trim($codice));
                if ($agente && $agente->email && $agente->idutente != $this->session->idutente) {
                    $destinatari[] = (object)[
                        'idutente' => $agente->idutente,
                        'email' => $agente->email,
                        'nome' => $agente->nome,
                        'cognome' => $agente->cognome
                    ];
                }
            }
        }
        
        // 3. Rimuovi duplicati mantenendo l'ultima occorrenza
        $destinatari_unici = [];
        foreach ($destinatari as $destinatario) {
            $destinatari_unici[$destinatario->idutente] = $destinatario;
        }
        
        return array_values($destinatari_unici);
    }

    private function inviaNotificaEmail($destinatario, $chat, $mittente, $messaggio, $id_chat)
    {
        $this->_configureEmail($destinatario->email, $mittente);
        // $this->email->reply_to($mittente->email, "{$mittente->nome} {$mittente->cognome}");
        $this->email->subject("[SCADENZARIO] Nuovo messaggio in: {$chat->titolo}");

        $data_email = [
            'mittente' => $mittente,
            'destinatario' => $destinatario,
            'messaggio' => nl2br(htmlspecialchars($messaggio)),
            'link_chat' => base_url("ScadenzarioChat/dettaglio/{$id_chat}"),
            'dettagli_scadenzario' => [
                'cliente' => $chat->scadenzario_ragsoc ?? 'N/D',
                'conto' => $chat->conto ?? 'N/D',
                'partita' => $chat->partita ?? 'N/D'
            ]
        ];

        $corpo = $this->load->view('emails/notifica_messaggio', $data_email, TRUE);
        
        $this->email->message($corpo);
        return $this->email->send();
    }

    public function getChatByScadenzario($conto, $partita, $scadenza)
    {
        $this->_baseQueryChat();
        $this->db->where('sc.conto', $conto);
        $this->db->where('sc.partita', $partita);
        $this->db->where('sc.scadenza', $scadenza);
        $this->_filtraPerAgente();
        
        return $this->db->get()->row();
    }

    public function getPartecipantiChat($id_chat)
    {
        $this->db->select('DISTINCT(u.idutente), u.email, u.nome, u.cognome');
        $this->db->from('scadenzario_chat_messaggi scm');
        $this->db->join('utenti u', 'u.idutente = scm.id_utente');
        $this->db->where('scm.id_chat', $id_chat);
        
        return $this->db->get()->result();
    }
}
