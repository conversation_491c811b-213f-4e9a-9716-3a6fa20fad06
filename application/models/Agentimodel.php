<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Agentimodel extends CI_Model
{

	public function getagenti()
	{
		list($query, $totale) = $this->Utentimodel->getutenti();
		$agenti = $query->result();

		// Sort agents by full name
		usort($agenti, function($a, $b) {
			return strcmp(
				trim($a->nome . ' ' . $a->cognome), 
				trim($b->nome . ' ' . $b->cognome)
			);
		});

		return $agenti;
	}

	public function getagente($idagente)
	{
		$agente = $this->Utentimodel->getutente($idagente);
		return $agente;
	}

	public function getagentebycodice($codice)
	{
		$rsutente = $this->Utentimodel->getrsutente(['codice' => $codice]);
		if ($rsutente) {
			$utente = $rsutente->row();
			return $utente;
		} else return false;
	}

	public function getagente_byemail($email)
	{
		$agente = $this->Utentimodel->getutentebyemail($email);
		return $agente;
	}

	public function getordini($idagente)
	{
		$this->db->select('O.*, C.ragsoc, (select count(*) from ordiniprodotti OP where OP.idordine=O.idordine) as numprodotti,
			(select sum(prezzo*quantita) from ordiniprodotti OP where OP.idordine=O.idordine) as totaleordine');
		$this->db->from('ordini O');
		$this->db->join('clienti C', 'C.idcliente = O.idcliente');

		$this->db->where('O.idagente', $idagente);

		$rs = $this->db->get();
		$rows = $rs->result();
		return $rows;
	}

	public function agente_elimina($idagente)
	{
		$this->db->delete('utenti', ['idutente' => $idagente]);
		$this->session->set_flashdata(['msg' => 'Agente eliminato', 'msgtype' => 'info']);
		$set = array('idagente' => null);
		$this->db->update('ordini', $set, ['idagente' => $idagente]);
		$this->session->set_flashdata(['msg' => 'Ordini aggiornati', 'msgtype' => 'info']);
		$this->db->update('clienti', $set, ['idagente' => $idagente]);
		$this->session->set_flashdata(['msg' => 'Clienti assegnati a: nessun agente', 'msgtype' => 'info']);
	}
}

/* End of file Agentimodel.php */
/* Location: ./application/models/Agentimodel.php */
