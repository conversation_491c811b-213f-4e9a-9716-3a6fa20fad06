<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Cataloghimodel extends CI_Model {

	public function getcataloghi() {
		$this->db->select('*');
		$this->db->from('cataloghi');
		if ($this->session->ruolo!='Admin') $this->db->where('pubblicato', 1);
		$rs=$this->db->get();
		$rows=$rs->result();
		return $rows;
	}

	public function getcatalogo($id) {
		$this->db->select('*');
		$this->db->from('cataloghi');
		$this->db->where('id', $id);
		if ($this->session->ruolo!='Admin') $this->db->where('pubblicato', 1);
		$rs=$this->db->get();
		$row=$rs->row();
		return $row;
	}

	public function addcatalogo($data) {
		$insert['titolo']=$data['titolo'];
		$insert['descrizione']=$data['descrizione'];
		$insert['nomefile']=$data['file_name'];
		$insert['dimensione']=$data['dimensione'];
		$this->db->insert('cataloghi', $insert);
		return $this->db->insert_id();
	}

	public function delcatalogo($id) {
		$catalogo=$this->getcatalogo($id);
		$this->db->delete('cataloghi', ['id'=>$id]);
		if (is_file(CATALOGHIPATH.$catalogo->nomefile)) unlink(CATALOGHIPATH.$catalogo->nomefile);
	}

	public function setpubblicato($id, $stato) {
		$this->db->update('cataloghi', ['pubblicato'=>(int)$stato], ['id'=>$id]);
		// echo $this->db->last_query();exit;
	}

	public function getsizecatalogo($id) {
		$catalogo=$this->getcatalogo($id);
		$filename=CATALOGHIPATH.$catalogo->nomefile;
		if (is_file($filename)) {
			$size=filesize($filename);
		}
		else $size=0;
		return $size;
	}

	public function getspazioutilizzato() {
		$this->db->select('sum(dimensione) as spazio');
		$this->db->from('cataloghi');
		$rs=$this->db->get();
		$row=$rs->row();
		return $row->spazio;
	}

}

/* End of file Cataloghimodel.php */
/* Location: ./application/models/Cataloghimodel.php */