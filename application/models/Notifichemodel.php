<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Notifichemodel extends CI_Model {

    public function __construct() {
        parent::__construct();
    }

    /**
     * Recupera le notifiche in base ai criteri specificati
     */
    public function get_notifiche($filters = array()) {
        $this->db->select('notifiche.*, scadenzario.agente, scadenzario.conto, scadenzario.partita, scadenzario.scadenza, scadenzario.ragsoc');
        $this->db->from('notifiche');
        $this->db->join('scadenzario', 'scadenzario.conto = notifiche.conto AND scadenzario.partita = notifiche.partita AND scadenzario.scadenza = notifiche.scadenza');
        
        // Filtri aggiuntivi
        if (!empty($filters['conto'])) {
            $this->db->where('notifiche.conto', $filters['conto']);
            $this->db->where('notifiche.partita', $filters['partita']);
            $this->db->where('notifiche.scadenza', $filters['scadenza']);
        }
        if (!empty($filters['stato'])) {
            $this->db->where('notifiche.stato', $filters['stato']);
        }
        $this->db->order_by('notifiche.data_programmata', 'DESC');
        return $this->db->get()->result();
    }

    // Aggiungi nuovo metodo per le notifiche da inviare
    public function get_notifiche_da_inviare() {
        $this->db->where('stato', 'programmata');
        $this->db->where('data_programmata <=', date('Y-m-d H:i:s'));
        return $this->db->get('notifiche')->result();
    }

    public function insert($data) {
        $defaults = array(
            'stato' => 'programmata',
            'creata_il' => date('Y-m-d H:i:s'),
            'destinatari' => implode(',', $data['destinatari']), // Converti array in stringa
            'conto' => $data['conto'],
            'partita' => $data['partita'],
            'scadenza' => $data['scadenza']
        );
        
        // Remove old id_scadenzario if present
        if (isset($data['id_scadenzario'])) {
            unset($data['id_scadenzario']);
        }
        
        return $this->db->insert('notifiche', array_merge($data, $defaults));
    }

    public function update($id, $data) {
        if(isset($data['destinatari'])) {
            $data['destinatari'] = implode(',', $data['destinatari']);
        }
        $this->db->where('id', $id);
        return $this->db->update('notifiche', $data);
    }
    public function update_stato($id, $stato, $data_invio = null) {
        $update_data = ['stato' => $stato];
        if($data_invio) {
            $update_data['data_invio'] = $data_invio;
        }
        return $this->db->update('notifiche', $update_data, ['id' => $id]);
    }

    public function get_notifica($id) {
        return $this->db->get_where('notifiche', ['id' => $id])->row();
    }

    public function elimina($id) {
        return $this->db->delete('notifiche', ['id' => $id]);
    }
}
