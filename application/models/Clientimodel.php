<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Clientimodel extends CI_Model
{
	public function __construct()
	{
		$this->out = '';
	}

	public function getclienti($idagente = '')
	{
		$this->db->select('C.*, concat(U.nome, " ", U.cognome) as agente');
		$this->db->from('clienti C');
		$this->db->join('utenti U', 'U.idutente = C.idagente', 'left');
		if ($this->session->ruolo == 'Agente') {
			$this->db->where('C.idagente', $this->session->idutente);
		}
		if ($this->session->ruolo == 'Admin') {
			if ($idagente != '') $this->db->where('C.idagente', $idagente);
		}
		$rs = $this->db->get();
		$rows = $rs->result();

		return $rows;
	}

	public function getclienti_ajax($idagente = '', $postData = null)
	{
		if ($postData) {
			$draw = $postData['draw'];
			$start = $postData['start'];
			$rowperpage = $postData['length']; // Rows display per page
			$columnIndex = $postData['order'][0]['column']; // Column index
			$columnName = $postData['columns'][$columnIndex]['data']; // Column name
			$columnSortOrder = $postData['order'][0]['dir']; // asc or desc
			$searchValue = $postData['search']['value']; // Search value
		} else {
			$response = array();
			$draw = '';
			$start = 0;
			$rowperpage = 10;
			$columnIndex = '';
			$columnName = '';
			$columnSortOrder = '';
			$searchValue = '';
		}

		## Search
		$searchQuery = "";
		if ($searchValue != '') {
			$camppiricerca = array('C.ragsoc', 'C.codice', 'C.piva', 'C.cf', 'U.nome', 'U.cognome', 'U.username');

			$camppiricerca_like = array_map(function ($val) use ($searchValue) {
				return "$val like '%$searchValue%'";
			}, $camppiricerca);
			$searchQuery = ' (' . implode(' or ', $camppiricerca_like) . ')';
		}

		## Total number of records without filtering
		$this->db->select('count(*) as allcount');
		$this->db->from('clienti C');
		if ($this->session->ruolo == 'Agente') {
			$this->db->where('C.idagente', $this->session->idutente);
		}
		if ($this->session->ruolo == 'Admin') {
			if ($idagente != '') $this->db->where('C.idagente', $idagente);
		}

		$records = $this->db->get()->result();
		$totalRecords = $records[0]->allcount;

		## Total number of record with filtering
		$this->db->select('count(*) as allcount');
		$this->db->from('clienti C');
		$this->db->join('utenti U', 'U.idutente = C.idagente', 'left');
		if ($this->session->ruolo == 'Agente') {
			$this->db->where('C.idagente', $this->session->idutente);
		}
		if ($this->session->ruolo == 'Admin') {
			if ($idagente != '') $this->db->where('C.idagente', $idagente);
		}

		if ($searchQuery != '') $this->db->where($searchQuery);
		$records = $this->db->get()->result();
		$totalRecordwithFilter = $records[0]->allcount;

		## Fetch records
		$this->db->select('C.*, concat(U.nome, " ", U.cognome) as agente');
		$this->db->from('clienti C');
		$this->db->join('utenti U', 'U.idutente = C.idagente', 'left');
		if ($this->session->ruolo == 'Agente') {
			$this->db->where('C.idagente', $this->session->idutente);
		}
		if ($this->session->ruolo == 'Admin') {
			if ($idagente != '') $this->db->where('C.idagente', $idagente);
		}

		if ($searchQuery != '') $this->db->where($searchQuery);
		$this->db->order_by($columnName, $columnSortOrder);
		$this->db->limit($rowperpage, $start);
		$records = $this->db->get()->result();

		$data = array();

		foreach ($records as $record) {

			$data[] = array(
				"idcliente" => $record->idcliente,
				"ragsoc" => $record->ragsoc,
				"piva" => $record->piva,
				"cf" => $record->cf,
				"sdi" => $record->sdi,
				"pec" => $record->pec,
				"codice" => $record->codice,
				"agente" => $record->agente,
			);
		}

		## Response
		$response = array(
			"draw" => intval($draw),
			"iTotalRecords" => $totalRecords,
			"iTotalDisplayRecords" => $totalRecordwithFilter,
			"aaData" => $data
		);

		return $response;
	}

	/**
	 * Lista di tutti i clienti
	 * @param string $stato (attivi/nonattivi)
	 * attivi: hanno ordini nei 2 anni
	 * nonattivi: non ordinano da più di 2 anni
	 *
	 * @return void
	 */
	public function getelencoclienti($stato = '')
	{
		$this->db->select('O.idcliente');
		$this->db->from('ordini O');
		$this->db->where('(O.dataordine >= DATE(NOW() - INTERVAL 730 DAY))', NULL, false);
		$this->db->distinct();
		$where_clause = $this->db->get_compiled_select();

		$this->db->select('C.*');
		$this->db->from('clienti C');
		if ($stato == "attivi") {
			// clienti attivi
			$this->db->where("C.idcliente IN ($where_clause)", NULL, false);
		} else {
			// clienti NON attivi
			$this->db->where("C.idcliente NOT IN ($where_clause)", NULL, false);
		}

		// echo $this->db->get_compiled_select();
		// exit;

		$rs = $this->db->get();
		$rows = $rs->result();

		return $rows;
	}

	public function getClientibyRagSoc($ragsoc)
	{
		$this->db->select('*');
		$this->db->from('clienti');
		$this->db->like('ragsoc', $ragsoc);
		if ($this->session->ruolo == 'Agente') {
			$this->db->where('idagente', $this->session->idutente);
		}
		if ($this->session->ruolo == 'Admin') {
			if ($idagente != '') $this->db->where('idagente', $idagente);
		}
		$rs = $this->db->get();
		$rows = $rs->result();
		return $rows;
	}

	public function nuovocliente($data)
	{
		if (($data['piva'] != '') && ($this->getclientebypiva($data['piva']))) {
			return 'Cliente esistente';
		} else {
			$insertdata['ragsoc'] = $data['ragsoc'];
			$insertdata['codice'] = $data['codice'];
			$insertdata['piva'] = $data['piva'];
			$insertdata['cf'] = $data['cf'];
			$insertdata['email'] = $data['email'];
			$insertdata['telefono'] = $data['telefono'];
			$insertdata['pagamento'] = $data['pagamento'];
			$insertdata['indirizzo'] = $data['indirizzo'];
			$insertdata['cap'] = $data['cap'];
			$insertdata['citta'] = $data['citta'];
			$insertdata['provincia'] = $data['provincia'];
			$insertdata['altradestinazione'] = $data['altradestinazione'];
			$insertdata['note'] = $data['note'];
			$insertdata['esentesdipec'] = ($data['esentesdipec'] == 1) ? 1 : 0;
			$insertdata['sdi'] = $data['sdi'];
			$insertdata['pec'] = $data['pec'];
			$insertdata['tipodocumento'] = $data['tipodocumento'];

			if (isset($data['idagente'])) $insertdata['idagente'] = $data['idagente'];
			else $insertdata['idagente'] = $this->session->idutente;
			$this->db->insert('clienti', $insertdata);
		}
		return true;
	}

	/**
	 * Seleziona tutti campi della tabella cliente e nomecognome agente
	 *
	 * @param int $idcliente
	 * @return obj $row
	 */
	public function getcliente($idcliente)
	{
		$this->db->select('C.*, concat(U.nome, " ", U.cognome) as agente');
		$this->db->from('clienti C');
		$this->db->join('utenti U', 'U.idutente = C.idagente', 'left');
		if ($this->session->ruolo == 'Agente') {
			$this->db->where('C.idagente', $this->session->idutente);
		}
		$this->db->where('C.idcliente', $idcliente);
		$rs = $this->db->get();
		$row = $rs->row();

		return $row;
	}

	/**
	 * Ritorna cliente dal codice o false
	 *
	 * @param      <type>   $codice  The codice
	 *
	 * @return     boolean  ( description_of_the_return_value )
	 */
	public function getclientebycodice($codice)
	{
		$rs = $this->db->get_where('clienti', ['codice' => $codice]);
		if ($rs->num_rows() == 0) {
			$row = false;
		} else $row = $rs->row();
		return $row;
	}

	/**
	 * Tutti i clienti senza codice
	 *
	 * @return false|array
	 */
	public function getclientisenzacodice()
	{
		$rs = $this->db->get_where('clienti', ['codice' => '']);
		if ($rs->num_rows() == 0) {
			$rows = false;
		} else $rows = $rs->result();
		return $rows;
	}

	/**
	 * Ritorna cliente dal piva o false
	 *
	 * @param      <type>   $piva  The piva
	 *
	 * @return     boolean  ( description_of_the_return_value )
	 */
	public function getclientebypiva($piva)
	{
		$rs = $this->db->get_where('clienti', ['piva' => $piva]);
		if ($rs->num_rows() == 0) {
			$row = false;
		} else $row = $rs->row();
		return $row;
	}

	public function getclientejson($idcliente)
	{
		$cliente = $this->getcliente($idcliente);
		$json = json_encode($cliente);
		return $json;
	}

	public function aggiornacliente($idcliente, $data)
	{
		// var_dump($data);exit;
		$data['esentesdipec'] = (isset($data['esentesdipec'])) ? 1 : 0;
		$this->db->update('clienti', $data, ['idcliente' => $idcliente]);
		if ($this->session->ruolo == 'Admin' && $data['idagente'] != '')
			$this->db->update('ordini', ['idagente' => $data['idagente']], ['idcliente' => $idcliente]);
	}

	public function getprodottiordinati($idcliente)
	{
		// SELECT O.dataordine, OP.codice, OP.prezzo, OP.quantita FROM ordini O
		// JOIN ordiniprodotti OP USING (idordine)
		// ORDER BY O.dataordine desc
		// 12/02/2020 modificata query invece di prezzo si prende prezzoordine per evitare sconto su sconto su sconto..

		$this->db->select('O.dataordine, TRIM(OP.codice) AS codice, OP.prezzoordine as prezzo, OP.quantita, OP.sconto, OP.descrizione, OP.um, OP.sconto, P.tags');
		$this->db->from('ordini O');
		$this->db->join('ordiniprodotti OP', 'idordine');
		$this->db->join('listino P', 'OP.codice=P.codice', 'left');
		$this->db->where('OP.prezzo>0');
		$this->db->where('idcliente', $idcliente);
		$this->db->order_by('O.dataordine', 'desc');
		$rs = $this->db->get();
		$rows = $rs->result();

		$ultimiordinati = array();
		foreach ($rows as $v) {
			if (!array_key_exists($v->codice, $ultimiordinati) && $v->codice != '00 001') $ultimiordinati[$v->codice] = $v;
		}

		return $ultimiordinati;
	}

	public function getprodottipersonalizzatiordinati($idcliente)
	{
		$this->db->select('O.dataordine, OP.id AS idordiniprodotto, TRIM(OP.codice) AS codice, OP.prezzoordine as prezzo, OP.quantita, OP.sconto, OP.descrizione, OP.um, OP.sconto, "" AS tags');
		$this->db->from('ordini O');
		$this->db->join('ordiniprodotti OP', 'idordine');
		$this->db->where('OP.prezzo>0');
		$this->db->where('idcliente', $idcliente);
		$this->db->where('codice', '00 001');
		$this->db->order_by('O.dataordine', 'desc');
		$rs = $this->db->get();
		$rows = $rs->result();

		$tmparray = array();
		$ultimipersonalizzati = array();

		foreach ($rows as $v) {
			if (!array_key_exists($v->descrizione, $tmparray) && $v->codice == '00 001') $tmparray[$v->descrizione] = $v;
		}

		foreach ($tmparray as $key => $value) {
			$ultimipersonalizzati[] = $value;
		}

		// var_dump($ultimipersonalizzati); exit;
		return $ultimipersonalizzati;
	}
	/**
	 * Elimina cliente
	 * Gli ordini e dettaglio ordini vengono eliminati da MySQL con chievi esterne
	 *
	 * @param      <type>  $idcliente  The idcliente
	 */
	public function cliente_elimina($idcliente)
	{
		$this->db->delete('clienti', ['idcliente' => $idcliente]);
	}

	public function checkxlsclienti()
	{
		$out = array();
		$campi_ok = array('codice', 'agente');
		$campi_xls = getallcampi_xls('./upload/clienti.xls');

		foreach ($campi_xls as $key => $value) {
			$out['campixls'][] = $value;
		}

		$campixlsok = (array_intersect($campi_xls, $campi_ok));
		$out['campinodb'] = array_diff($campi_ok, $campixlsok);

		// var_dump(array_diff($campi_ok, $campi_xls));
		// var_dump($campi_ok);
		// var_dump($campi_xls); exit;

		return $out;
	}

	public function aggiornaclienti()
	{
		$runfilename = 'aggiornaclienti';
		$filexls = './upload/clienti.xls';
		$campicliente = array(
			'Codice', 'Descrizione Conto', 'Indirizzo', 'CAP', 'Località', 'Indirizzo E-Mail', 'Partita', 'agente'
		);
		$out = '';
		$numclienti = 0;
		$agentinontrovati = array();

		runfile_crea($runfilename);

		$reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
		$reader->setReadDataOnly(true);
		$spreadsheet = $reader->load($filexls);
		$worksheet = $spreadsheet->getActiveSheet();
		$highestColumn = $worksheet->getHighestColumn();
		$highestRow = $worksheet->getHighestRow();
		$highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

		$rigacampi = $worksheet->rangeToArray('A1:L1');
		foreach ($campicliente as $campo) {
			$posizionecampi[$campo] = array_search($campo, $rigacampi[0]) + 1;
		}
		// var_dump($rigacampi);
		// var_dump($posizionecampi); exit;
		// si parte da 4 perchè prima ci sono righe che nn ci servono
		$out = 'Clienti esistenti:';
		for ($row = 4; $row <= $highestRow; ++$row) {
			if (!runfile_esiste($runfilename)) exit;
			$codice = (string)$worksheet->getCellByColumnAndRow($posizionecampi['Codice'], $row)->getValue();
			$codice = str_replace('3010', '', $codice); // 3010 ricorre su tutti ma qui quella parte di codicecliente non serve
			$cliente = $this->getclientebycodice($codice);
			if (!$cliente) {
				$emailagente = trim((string)$worksheet->getCellByColumnAndRow($posizionecampi['agente'], $row)->getValue());
				$agente = $this->Agentimodel->getagente_byemail($emailagente);
				if ($agente) {
					if ($codice != '' && isset($agente->idutente)) {
						$set = array(
							'codice' => $codice,
							'ragsoc' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Descrizione Conto'], $row)->getValue(),
							'indirizzo' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Indirizzo'], $row)->getValue(),
							'cap' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['CAP'], $row)->getValue(),
							'citta' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Località'], $row)->getValue(),
							'email' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Indirizzo E-Mail'], $row)->getValue(),
							'piva' => (string)$worksheet->getCellByColumnAndRow($posizionecampi['Partita'], $row)->getValue(),
							'idagente' => $agente->idutente,
						);
						$this->db->insert('clienti', $set);
						$numclienti++;
					}
				} else {
					$agentinontrovati[$emailagente] = 1;
				}
			} else {
				$out .= $codice . ', ';
			}
		}

		// var_dump($set);

		runfile_elimina($runfilename);
		$out .= '<p>';
		foreach ($agentinontrovati as $key => $value) {
			$out .= 'Agente non trovato: ' . $key . ' (clienti relativi non inseriti)<br>';
		}

		return array($out, $numclienti);
	}

	/**
	 * Verifico se il cliente fosse bloccato in JG
	 * se bloccato, cancello codice in modo che arrivi l'ordine ma anche la mail a LC in modo che possano controllare il cliente sul portale e cambiare il codice cliente
	 *
	 * @param mixed $cliente
	 * @return bool
	 *
	 */
	public function checkClienteBloccato($cliente)
	{
		$this->load->model('AS400/ASClientiModel');
		$flag_cliente_bloccato = false;
		if ($cliente->codice != '') {
			$clienteJG = $this->ASClientiModel->getcliente($cliente->codice);
			if (isset($clienteJG->FLCH02) && trim($clienteJG->FLCH02) == 'CLIENTE BLOCCATO') {
				// cancella codice cliente
				$data = array(
					'esentesdipec' => $cliente->esentesdipec,
					'codice' => '',
				);
				$this->Clientimodel->aggiornacliente($cliente->idcliente, $data);
				$flag_cliente_bloccato = true;
			}
		}
		return $flag_cliente_bloccato;
	}
}

/* End of file Clientimodel.php */
/* Location: ./application/models/Clientimodel.php */