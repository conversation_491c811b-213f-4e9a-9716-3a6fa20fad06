<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Preventivimodel extends CI_Model {

	public function getpreventivi() {
		$this->db->select('P.*, concat(U.nome," ",U.cognome) as agente, (select count(*) from preventiviprodotti PP where PP.idpreventivo=P.idpreventivo) as numprodotti,
			(select sum(prezzo*(IF (quantita=0, 1, quantita))) from preventiviprodotti PP where PP.idpreventivo=P.idpreventivo) as totalepreventivo');
		$this->db->from('preventivi P');
		$this->db->join('utenti U', 'U.idutente = P.idagente');

		if ($this->session->ruolo=='Agente') $this->db->where('P.idagente', $this->session->idutente);
		// if ($idcliente)	$this->db->where('P.idcliente', $idcliente);

		$rs=$this->db->get();
		$rows=$rs->result();
		return $rows;
	}

	public function getpreventivo($idpreventivo) {
		$this->db->select('*');
		$this->db->from('preventivi');
		if ($this->session->ruolo=='Agente') $this->db->where('idagente', $this->session->idutente);
		$this->db->where('idpreventivo', $idpreventivo);
		$rs=$this->db->get();
		$row=$rs->row();
		return $row;
	}

	public function getpreventiviprodotti($idpreventivo) {
		$this->db->select('OP.*, L.prezzo as prezzolistino');
		$this->db->from('preventiviprodotti OP');
		$this->db->join('listino L', 'L.codice = OP.codice', 'left');
		$this->db->where('idpreventivo', $idpreventivo);
		$rs=$this->db->get();
		$rows=$rs->result();
		return $rows;
	}

	/**
	 * Ritorna le colonne che hanno sconto
	 * @param int $idpreventivo
	 * @return	array
	 */
	public function getColonneSconto($idpreventivo) {
			$preventivo=$this->getpreventiviprodotti($idpreventivo);
			$colonnesconto=array(0,0,0,0);
			foreach ($preventivo as $prodotto) {
				if($prodotto->sconto>0) $colonnesconto[1]=1;
				if($prodotto->sconto2>0) $colonnesconto[2]=1;
				if($prodotto->sconto3>0) $colonnesconto[3]=1;
			}
			return $colonnesconto;
	}

	/**
	 * Se visualizzare colonna quantità in scheda o pdf
	 * @param int $idpreventivo
	 * @return	boolean
	 */
	public function visQuantita($idpreventivo) {
			$preventivo=$this->getpreventiviprodotti($idpreventivo);
			$qta=false;
			foreach ($preventivo as $prodotto) {
				if($prodotto->quantita>0) $qta=true;
				break;
			}
			return $qta;
	}


	public function salvapreventivonuovo($data) {
		// var_dump($data);exit;
		$insert=array();
		$insert['noteagente']=$data['noteagente'];
		$insert['cliente']=$data['cliente'];
		$insert['idagente']=$this->session->idutente;

		$this->db->insert('preventivi', $insert);
		$idpreventivo=$this->db->insert_id();
		// echo $idpreventivo;
		// var_dump($data);
		// $idpreventivo=1;
		// aggiungi prodotti
		$insert=array();
		$i=0;
		foreach ($data['codice'] as $key => $codice) {
			$prodotto=$this->Prodottimodel->getprodottojson($codice);
			$insert[$i]['idpreventivo']=$idpreventivo;
			$insert[$i]['codice']=$codice;
			$insert[$i]['descrizione']=$data['descrizione'][$key];
			$insert[$i]['prezzo']=sostituisciVirgoleConPunti($data['prezzo'][$key]);
			$insert[$i]['prezzopreventivo']=sostituisciVirgoleConPunti($data['prezzopreventivo'][$key]);
			$insert[$i]['sconto']=($data['sconto'][$key]>0) ? $data['sconto'][$key] : null;
			$insert[$i]['sconto2']=($data['sconto2'][$key]>0) ? $data['sconto2'][$key] : null;
			$insert[$i]['sconto3']=($data['sconto3'][$key]>0) ? $data['sconto3'][$key] : null;
			$insert[$i]['quantita']=$data['quantita'][$key];
			$insert[$i]['um']=$data['um'][$key];
			$insert[$i]['note']=$data['note'][$key];
			$insert[$i]['prodotto']=$prodotto;
			$i++;
		}
		// var_dump($insert);
		$this->db->insert_batch('preventiviprodotti', $insert);
		return $idpreventivo;
	}

	public function preventivi_modifica_ex($data) {
		$idpreventivo=$data['idpreventivo'];
		$preventivo=$this->getpreventivo($idpreventivo);
		if ($preventivo->idagente!=$this->session->idutente && $this->session->ruolo=='Agente') return false;

		$update=array();
		$where=array();
		$update['noteagente']=$data['noteagente'];
		$update['cliente']=$data['cliente'];

		$this->db->update('preventivi', $update, ['idpreventivo'=>$idpreventivo]);
		// echo $idpreventivo;
		// var_dump($data);
		// $idpreventivo=1;
		// aggiungi prodotti ordinati
		$this->db->delete('preventiviprodotti', ['idpreventivo'=>$idpreventivo]);
		$insert=array();
		$i=0;
		foreach ($data['codice'] as $key => $codice) {
			$prodotto=$this->Prodottimodel->getprodottojson($codice);
			$insert[$i]['idpreventivo']=$idpreventivo;
			$insert[$i]['codice']=$codice;
			$insert[$i]['descrizione']=$data['descrizione'][$key];
			$insert[$i]['prezzo']=$data['prezzo'][$key];
			$insert[$i]['prezzopreventivo']=$data['prezzopreventivo'][$key];
			$insert[$i]['sconto']=($data['sconto'][$key]>0) ? $data['sconto'][$key] : null;
			$insert[$i]['sconto2']=($data['sconto2'][$key]>0) ? $data['sconto2'][$key] : null;
			$insert[$i]['sconto3']=($data['sconto3'][$key]>0) ? $data['sconto3'][$key] : null;
			$insert[$i]['quantita']=$data['quantita'][$key];
			$insert[$i]['um']=$data['um'][$key];
			$insert[$i]['note']=$data['note'][$key];
			$insert[$i]['prodotto']=$prodotto;
			$i++;
		}
		// var_dump($insert);
		$this->db->insert_batch('preventiviprodotti', $insert);
	}

	public function eliminapreventivo($idpreventivo) {
		$preventivo=$this->getpreventivo($idpreventivo);
		if ($preventivo->idagente==$this->session->idutente) {
			$this->db->delete('preventivi', ['idpreventivo'=>$idpreventivo]);
			$this->db->delete('preventiviprodotti', ['idpreventivo'=>$idpreventivo]);
		}
	}

}

/* End of file Preventivimodel.php */
/* Location: ./application/models/Preventivimodel.php */