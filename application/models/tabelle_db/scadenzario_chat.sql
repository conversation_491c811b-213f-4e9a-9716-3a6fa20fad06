CREATE TABLE `scadenzario_chat` (
	`id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
	`conto` VARCHAR(10) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`partita` INT(10) UNSIGNED NULL DEFAULT NULL,
	`scadenza` DATE NULL DEFAULT NULL,
	`titolo` VARCHAR(255) NOT NULL COLLATE 'latin1_swedish_ci',
	`chiusa` TINYINT(1) NULL DEFAULT '0',
	`data_chiusura` DATETIME NULL DEFAULT NULL,
	`data_creazione` TIMESTAMP NOT NULL DEFAULT current_timestamp(),
	`responsabile_predefinito` INT(10) UNSIGNED NULL DEFAULT NULL,
	PRIMARY KEY (`id`) USING BTREE
)
