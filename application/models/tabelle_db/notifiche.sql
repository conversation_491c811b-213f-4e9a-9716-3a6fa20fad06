CREATE TABLE `notifiche` (
	`id` INT(11) NOT NULL AUTO_INCREMENT,
	`conto` VARCHAR(10) NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	`partita` INT(10) UNSIGNED NULL DEFAULT NULL,
	`scadenza` DATE NULL DEFAULT NULL,
	`testo` TEXT NOT NULL COLLATE 'utf8mb3_general_ci',
	`destinatari` TEXT NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	`data_programmata` DATETIME NULL DEFAULT NULL,
	`stato` ENUM('programmata','inviata','fallita') NOT NULL DEFAULT 'programmata' COLLATE 'utf8mb3_general_ci',
	`created_at` TIMESTAMP NULL DEFAULT current_timestamp(),
	`updated_at` TIMESTAMP NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
	`creata_il` DATETIME NULL DEFAULT NULL,
	`data_invio` DATETIME NULL DEFAULT NULL,
	`titolo` VARCHAR(255) NULL DEFAULT NULL COLLATE 'utf8mb3_general_ci',
	PRIMARY KEY (`id`) USING BTREE
)
COLLATE='utf8mb3_general_ci'
ENGINE=InnoDB
AUTO_INCREMENT=9
;