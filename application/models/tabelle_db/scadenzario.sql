CREATE TABLE `scadenzario` (
	`id` INT(10) UNSIGNED NOT NULL AUTO_INCREMENT,
	`agente` VARCHAR(5) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`conto` VARCHAR(10) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`ragsoc` VARCHAR(50) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`anno` YEAR NULL DEFAULT NULL,
	`partita` INT(10) UNSIGNED NULL DEFAULT NULL,
	`scadenza` DATE NULL DEFAULT NULL,
	`rata` VARCHAR(50) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`importo` DECIMAL(10,2) NULL DEFAULT NULL,
	`data` DATE NULL DEFAULT NULL,
	`importoscaduto` DECIMAL(10,2) NULL DEFAULT NULL,
	`importoascadere` DECIMAL(10,2) NULL DEFAULT NULL,
	`fonte` ENUM('JG','LC') NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	PRIMARY KEY (`id`) USING BTREE
)
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB
AUTO_INCREMENT=869754
;
