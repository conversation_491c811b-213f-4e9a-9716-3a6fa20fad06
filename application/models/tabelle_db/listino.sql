CREATE TABLE `listino` (
	`codice` VARCHAR(10) NOT NULL COLLATE 'latin1_swedish_ci',
	`descrizione` VARCHAR(40) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`prezzo` DECIMAL(6,2) NULL DEFAULT NULL,
	`um` CHAR(2) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`tags` VARCHAR(50) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	PRIMARY KEY (`codice`) USING BTREE,
	INDEX `tags` (`tags`) USING BTREE,
	FULLTEXT INDEX `descrizione` (`descrizione`)
)