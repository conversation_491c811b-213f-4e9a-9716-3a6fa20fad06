CREATE TABLE `ordiniprodotti` (
	`id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT,
	`idordine` MEDIUMINT(8) UNSIGNED NULL DEFAULT NULL,
	`codice` VARCHAR(10) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`descrizione` VARCHAR(40) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`prezzoordine` DECIMAL(7,2) NULL DEFAULT NULL,
	`sconto` DECIMAL(5,2) NULL DEFAULT NULL,
	`prezzo` DECIMAL(7,2) NULL DEFAULT NULL COMMENT 'prezzo finale scontato',
	`quantita` SMALLINT(5) UNSIGNED NULL DEFAULT NULL,
	`um` CHAR(2) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`note` TINYTEXT NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`prodotto` TINYTEXT NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`datasospeso` DATE NULL DEFAULT NULL,
	`tiporiga` CHAR(2) NULL DEFAULT '01' COMMENT 'TP_RIGA in JG' COLLATE 'latin1_swedish_ci',
	`qtaevasa` INT(11) NULL DEFAULT NULL,
	PRIMARY KEY (`id`) USING BTREE,
	INDEX `FK_ordiniprodotti_ordini` (`idordine`) USING BTREE,
	INDEX `codice` (`codice`) USING BTREE,
	FULLTEXT INDEX `descrizione` (`descrizione`),
	CONSTRAINT `FK_ordiniprodotti_ordini` FOREIGN KEY (`idordine`) REFERENCES `ordini` (`idordine`) ON UPDATE RESTRICT ON DELETE CASCADE
)