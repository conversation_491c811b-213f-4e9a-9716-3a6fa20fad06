CREATE TABLE `utenti` (
	`idutente` SMALLINT(5) UNSIGNED NOT NULL AUTO_INCREMENT,
	`idruolo` INT(11) NULL DEFAULT NULL,
	`username` VA<PERSON>HA<PERSON>(15) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`password` VARCHAR(32) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`email` VARCHAR(50) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`nome` VARCHAR(50) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`cognome` VARCHAR(50) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`datalogin` DATETIME NULL DEFAULT NULL,
	`datacambiopass` DATETIME NULL DEFAULT NULL,
	`datacreato` DATETIME NULL DEFAULT current_timestamp(),
	`codice` VARCHAR(20) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`telefono` VARCHAR(20) NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	PRIMARY KEY (`idutente`) USING BTREE,
	UNIQUE INDEX `email` (`email`) USING BTREE
)
COLLATE='latin1_swedish_ci'
ENGINE=InnoDB
AUTO_INCREMENT=48
;
