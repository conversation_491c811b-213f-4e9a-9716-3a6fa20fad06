CREATE TABLE `ordini` (
	`idordine` MEDIUMINT(8) UNSIGNED NOT NULL AUTO_INCREMENT,
	`idagente` SMALLINT(5) UNSIGNED NOT NULL DEFAULT '0',
	`idcliente` MEDIUMINT(8) UNSIGNED NOT NULL DEFAULT '0',
	`dataordine` DATETIME NULL DEFAULT current_timestamp(),
	`cliente` TINYTEXT NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`datainviato` DATETIME NULL DEFAULT NULL,
	`datamodifica` DATETIME NULL DEFAULT NULL,
	`chiuso` BIT(1) NULL DEFAULT NULL,
	`noteagente` TEXT NULL DEFAULT NULL COLLATE 'latin1_swedish_ci',
	`TDOCOO` CHAR(1) NULL DEFAULT NULL COMMENT 'tipo ordine A,M,T,W' COLLATE 'latin1_swedish_ci',
	`NROROO` CHAR(7) NULL DEFAULT NULL COMMENT 'num ordine ERP' COLLATE 'latin1_swedish_ci',
	`tipodocumento` CHAR(1) NULL DEFAULT NULL COMMENT 'da JG bolle fatture' COLLATE 'latin1_swedish_ci',
	PRIMARY KEY (`idordine`) USING BTREE,
	INDEX `FK_ordini_clienti` (`idcliente`) USING BTREE,
	INDEX `cod_erp` (`NROROO`, `TDOCOO`) USING BTREE,
	CONSTRAINT `FK_ordini_clienti` FOREIGN KEY (`idcliente`) REFERENCES `clienti` (`idcliente`) ON UPDATE CASCADE ON DELETE CASCADE
)