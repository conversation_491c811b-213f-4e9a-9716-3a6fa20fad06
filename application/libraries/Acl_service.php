<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Acl_service {
    protected $CI;

    public function __construct() {
        $this->CI =& get_instance();
        $this->CI->load->model('Scadenzariomodel');
        $this->CI->load->model('Agentimodel');
        $this->CI->load->model('Utentimodel');
    }

    /**
     * Verifica i permessi per lo scadenzario
     * 
     * @param int $id ID dello scadenzario
     * @param string $permission Tipo di permesso (modifica, visualizza, ecc.)
     * @return stdClass
     */
    public function check_scadenzario_permission($id, $permission = 'modifica') {
        $acl = new stdClass();
        
        // Permesso di default
        $acl->{$permission} = false;

        // Admin ha sempre tutti i permessi
        if ($this->CI->session->ruolo == 'Admin') {
            $acl->{$permission} = true;
            return $acl;
        }

        // Recupera lo scadenzario
        $sc = $this->CI->Scadenzariomodel->getscadenzario($id);
        if (!$sc) {
            return $acl;
        }

        // Recupera l'agente corrente
        $agente = $this->CI->Agentimodel->getagente($this->CI->session->idutente);
        
        // Verifica se l'agente corrente ha i permessi
        $codici = explode(';', $agente->codice);
        if (in_array(trim($sc->agente), $codici)) {
            $acl->{$permission} = true;
        }

        return $acl;
    }

    /**
     * Metodo generico per verificare i permessi
     * 
     * @param string $resource Risorsa da controllare
     * @param string $permission Tipo di permesso
     * @param mixed $context Contesto aggiuntivo (opzionale)
     * @return bool
     */
    public function has_permission($resource, $permission, $context = null) {
        // Implementazione base, può essere estesa
        if ($this->CI->session->ruolo == 'Admin') {
            return true;
        }

        // Logica specifica per risorse
        switch($resource) {
            case 'scadenzario':
                return $this->check_scadenzario_permission($context, $permission)->{$permission};
            
            case 'scadenzario_chat':
                return $this->check_scadenzario_chat_permission($context, $permission);
            
            // Aggiungi altri casi specifici
            default:
                return false;
        }
    }

    /**
     * Verifica i permessi per la chat dello scadenzario
     * 
     * @param int $id_scadenzario ID dello scadenzario
     * @param string $permission Tipo di permesso (crea, visualizza, ecc.)
     * @return bool
     */
    public function check_scadenzario_chat_permission($id_scadenzario, $permission = 'crea') {
        // Admin ha sempre tutti i permessi
        if ($this->CI->session->ruolo == 'Admin') {
            return true;
        }

        // Recupera lo scadenzario
        $this->CI->load->model('Scadenzariomodel');
        $sc = $this->CI->Scadenzariomodel->getscadenzario($id_scadenzario);
        
        if (!$sc) {
            return false;
        }

        // Recupera l'agente corrente
        $this->CI->load->model('Agentimodel');
        $agente = $this->CI->Agentimodel->getagente($this->CI->session->idutente);
        
        // Verifica se l'agente corrente ha i permessi
        $codici = explode(';', $agente->codice);
        return in_array(trim($sc->agente), $codici);
    }
}
