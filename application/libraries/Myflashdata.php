<?php if (!defined('BASEPATH')) exit('No direct script access allowed');

class MyFlashdata
{
    protected $CI;
    private $msgtypes = array('success', 'info', 'warning', 'danger', 'error');
    private $flashdata_msgs = array();

    public function __construct()
    {
        $this->CI = &get_instance();
        $this->CI->load->library('session');
    }

    public function addFlashdataMsg($msg, $msgtype = 'info')
    {
        if (!in_array($msgtype, $this->msgtypes)) {
            $msgtype = 'info';
        }
        $this->flashdata_msgs[] = ['msg' => $msg, 'msgtype' => $msgtype];
        $this->CI->session->messages = $this->flashdata_msgs;
    }

    public function getFlashdataMsgs()
    {
        return $this->flashdata_msgs;
    }

    public function htmlfromsession()
    {
        $messages = $this->CI->session->messages;
        $this->CI->session->unset_userdata('messages');
        if (is_null($messages) || count($messages) == 0) return '';

        $html = '';
        foreach ($messages as $message) {
            $html .= '<div class="alert alert-' . $message['msgtype'] . ' alert-dismissible alert-autodismiss" role="alert">';
            $html .= '<button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>';
            $html .= '<i class="icon fa fa-' . $message['msgtype'] . '"></i>';
            $html .= $message['msg'];
            $html .= '</div>';
        }
        $html .= <<<EOT
        <script type="text/javascript">
        $(document).ready(function() {
            // Rimuovere il div dopo tot secondi
            setTimeout(dismissAlert, 1000);
            function dismissAlert() {
                $('.alert-autodismiss').fadeOut(1000, function() {
                    $(this).remove();
                });
            }
          });
          </script>
EOT;
        return $html;
    }
}
