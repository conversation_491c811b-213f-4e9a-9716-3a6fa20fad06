<?php
function asset_url($url)
{
    return base_url('assets/' . $url);
}

/**
 * Ritorna data in formato yyyy-mm-dd
 * se data è vuoto ritorna 0000-00-00
 *
 * @param [type] $data formato dd.mm.yyyy il separatore non è importante
 * @return void
 */
function datamysql($data)
{
    if ($data != '') {
        if (preg_match("/(\d{2}).(\d{2}).(\d{4})/", $data, $m)) {
            return $m[3] . '-' . $m[2] . '-' . $m[1];
        } else return $data;
    } else return null;
}
/**
 * Converte la data dal formato Ymd nel formato yyyy-mm-dd
 * se la data non è nel formato Ymd ritorna la data $date_str
 *
 * @param [type] $date_str
 * @return string
 */
function dataYmd_mysql($date_str)
{
    $date_obj = DateTime::createFromFormat('Ymd', $date_str);
    if ($date_obj instanceof DateTime) {
        return $date_obj->format('Y-m-d');
    } else return $date_str;
}

function generatePassword($length = 8)
{
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $count = mb_strlen($chars);

    for ($i = 0, $result = ''; $i < $length; $i++) {
        $index = rand(0, $count - 1);
        $result .= mb_substr($chars, $index, 1);
    }

    return $result;
}

function eliminatags($testo)
{
    $testo = strip_tags($testo);
    $testo = nl2br($testo);
    return $testo;
}

function checkcreacartella($path)
{
    if (!is_dir($path)) {
        $parts = explode('/', $path);
        $cartella = '';
        foreach ($parts as $dir) {
            if ($dir != '') {
                $cartella .= $dir . '/';
                if (!is_dir($cartella)) mkdir($cartella);
            }
        }
    }
}

function getallcampi_xls($filexls, $tipo = 'xls')
{
    if ($tipo == 'xls') $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xls();
    else $reader = new \PhpOffice\PhpSpreadsheet\Reader\Xlsx();
    $reader->setReadDataOnly(true);
    $spreadsheet = $reader->load($filexls);
    $worksheet = $spreadsheet->getActiveSheet();
    $highestColumn = $worksheet->getHighestColumn();
    $highestRow = $worksheet->getHighestRow();
    $highestColumnIndex = \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($highestColumn);

    $campi_xls = array();
    $out = '';
    for ($col = 1; $col <= $highestColumnIndex; ++$col) {
        $campo = strtolower($worksheet->getCellByColumnAndRow($col, 1)->getValue());
        if ($campo != '') {
            $campi_xls[$col] = $campo;
        }
    }
    return $campi_xls;
}

function runfile_crea($tipo)
{
    $filename = $tipo . '.run';
    $fh = fopen($filename, 'w');
    fclose($fh);
    return $filename;
}

function runfile_elimina($tipo)
{
    $filename = $tipo . '.run';
    if (is_file($filename)) unlink($filename);
}

function runfile_esiste($tipo)
{
    $filename = $tipo . '.run';
    return is_file($filename);
}

function runfile_scrivi($tipo, $dati)
{
    $filename = $tipo . '.run';
    if (is_file($filename)) file_put_contents($filename, $dati);
}

/**
 * Aggiusta codice cliente aggiungendo se serve le prime 5 cifre iniziali per db AS400
 *
 * @param [type] $codice_cliente
 * @return void
 */
function aggiustacodicecliente($codice_cliente)
{
    $codice = '03010' . substr('0000000000' . $codice_cliente, -5);
    return $codice;
}

function inviamail($email, $oggetto, $testo)
{
    $CI = &get_instance();

    $CI->load->library('email');
    $config['mailtype'] = 'html';
    $CI->email->initialize($config);

    $CI->email->from($CI->config->item('mailfrom'));
    $CI->email->to($email);

    $CI->email->subject($oggetto);
    $CI->email->message($testo);
    return $CI->email->send();
}

/**
 * La funzione "split_codice_erp" prende in input una stringa "codice" che rappresenta un codice ERP.

 * La funzione divide questo codice in due parti: il "tipo_ordine" e il "numordine".
 *
 * - Se il primo carattere di "codice" è un numero, allora il "tipo_ordine" è "W" e tutto il "codice" diventa il "numordine".
 * - In caso contrario, il "tipo_ordine" è il primo carattere di "codice" e il resto di "codice" costituisce il "numordine".
 *
 * Entrambi i valori ("tipo_ordine" e "numordine") vengono inseriti in un array che viene restituito come output della funzione.
 *
 * @param [type] $codice
 * @return void
 */
function split_codice_erp($codice)
{
    if (is_int(substr($codice, 0, 1))) {
        $tipo_ordine = "W";
        $numordine = $codice;
    } else {
        $tipo_ordine = substr($codice, 0, 1);
        $numordine = trim(substr($codice, 1));
    }
    return array($tipo_ordine, $numordine);
}

function sostituisciVirgoleConPunti($numero) {
    $numero_con_punti = str_replace(',', '.', $numero);
    return $numero_con_punti;
}