<div class="content">
	<div class="row">
		<div class="col-md-6">
			<div class="panel panel-primary"> <!-- card card-solid -> panel panel-primary -->
				<?php $pdf_path = FCPATH . 'upload/scadenzario/fatture/' . $sc->anno . '/' . $sc->conto . '/' . $sc->id . '/fattura.pdf'; ?>
				<div class="panel-heading">
					<div class="row">
						<div class="col-md-6">
							<h3 class="panel-title">Dettaglio Scadenza</h3>
						</div>
						<div class="col-md-6">
							<div class="pull-right">
								<!-- Bottone Torna alla lista dovrebbe essere il primo a sinistra poiché è un'azione di navigazione principale -->
								<a href="<?php echo base_url('scadenzario') ?>" class="btn btn-default btn-sm">
									<i class="fa fa-arrow-left"></i> Torna alla lista
								</a>

								<!-- Gruppo bottoni per la gestione della fattura -->
								<?php if (file_exists($pdf_path)): ?>
									<a href="<?php echo base_url('upload/scadenzario/fatture/' . $sc->anno . '/' . $sc->conto . '/' . $sc->id . '/fattura.pdf') ?>"
										target="_blank"
										class="btn btn-default btn-sm">
										<i class="fa fa-file-pdf"></i> Visualizza Fattura
									</a>
									<button onclick="deletePdf(<?php echo $sc->id ?>)" class="btn btn-danger btn-sm">
										<i class="fa fa-trash"></i> Elimina PDF
									</button>
								<?php else: ?>
										<a href="<?php echo base_url('scadenzario/upload_fattura_form/' . $sc->id) ?>" class="btn btn-warning btn-sm">
											<i class="fa fa-upload"></i> Carica Fattura
										</a>
								<?php endif; ?>
							</div>
						</div>
					</div>
				</div>
				<div class="panel-body"> <!-- panel-body -> panel-body -->
					Agente #: <?php echo $sc->agente ?><br>
					Cliente: <?php echo $sc->ragsoc ?> (Conto: <?php echo $sc->conto ?>)
					<?php
					if ($sc->idcliente == '') {
						echo 'Cliente non registato nel portale (<a href="' . base_url('agente/cliente_nuovo/' . $sc->conto . '/' . base64_encode($sc->ragsoc)) . '">registra</a>)';
					}
					?>
					<br>
					Anno: <?php echo $sc->anno ?><br>
					Partita: <?php echo $sc->partita ?><br>
					Scadenza: <?php echo $sc->scadenzaita ?><br>
					Rata: <?php echo $sc->rata ?><br>
					Importo: <?php echo $sc->importo ?><br>
					Data documento: <?php echo $sc->dataita ?><br>
					Importo scaduto: <?php echo $sc->importoscaduto ?><br>
					Importo a scadere: <?php echo $sc->importoascadere ?><br>
				</div>

				<div class="panel-footer"> <!-- panel-footer -> panel-footer -->
					<?php if (!$nobtns) : ?>
						<a href="<?php echo base_url($existing_chat ? 'ScadenzarioChat/dettaglio/' . $existing_chat->id : 'ScadenzarioChat/nuova/' . $sc->id) ?>"
							class="btn btn-info">
							<?php echo $existing_chat ? 'Apri Chat' : 'Nuova Chat'; ?>
						</a>
						<?php if (!$sc->pagato) : ?>
							<a href="<?php echo base_url('scadenzario/pagato/' . $sc->id) ?>" class="btn btn-success pull-right" onclick="return confirm('Registra riscossione?')">Registra come pagato</a>
						<?php else : ?>
							<a href="<?php echo base_url('scadenzario/notificapagato/' . $sc->id) ?>" class="btn btn-success pull-right">Notifica email</a>
						<?php endif ?>

					<?php else : ?>
						<?php if ($sc->pagato) : ?>
							Riscosso da <?php echo $agente->nome . ' ' . $agente->cognome ?> il <?php echo $sc->datapagatoita ?>
						<?php endif ?>
					<?php endif ?>
				</div>
			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12 col-lg-6 col-sm-12">
			<div class="panel panel-default"> <!-- panel panel-default -> panel panel-default -->
				<div class="panel-heading"> <!-- panel-heading -> panel-heading -->
					Notifiche
					<a href="<?php echo base_url('notifiche/create?id_scadenzario=' . $sc->id) ?>" class="btn btn-xs btn-success pull-right">Crea Notifica</a>
				</div>
				<div class="panel-body"> <!-- panel-body -> panel-body -->
					<table class="table table-bordered table-striped">
						<thead>
							<tr>
								<th>Titolo</th>
								<th>Testo</th>
								<th>Data Programmata</th>
								<th>Data Invio</th>
								<th>Stato</th>
								<th>Azioni</th>
							</tr>
						</thead>
						<tbody>
							<?php if (isset($notifiche) && !empty($notifiche)): ?>
								<?php foreach ($notifiche as $notifica): ?>
									<tr>
										<td><?php echo $notifica->titolo; ?></td>
										<td><?php echo $notifica->testo ?></td>
										<td><?php echo date("d/m/Y H:i", strtotime($notifica->data_programmata)); ?></td>
										<td><?php echo $notifica->data_invio ? date("d/m/Y H:i", strtotime($notifica->data_invio)) : '-'; ?></td>
										<td>
											<span class="label label-<?php
																		echo $notifica->stato == 'inviata' ? 'success' : ($notifica->stato == 'programmata' ? 'warning' : 'default');
																		?>">
												<?php echo ucfirst($notifica->stato); ?>
											</span>

											<?php if ($notifica->stato == 'programmata' && strtotime($notifica->data_programmata) < time()): ?>
												<i class="fa fa-exclamation-triangle text-danger" title="In ritardo"></i>
											<?php endif; ?>
										</td>
										<td>
											<a href="<?php echo base_url('notifiche/modifica/' . $notifica->id); ?>" class="btn btn-xs btn-primary">
												<i class="fa fa-pencil"></i> Modifica
											</a>
											<a href="<?php echo base_url('notifiche/elimina/' . $notifica->id); ?>" class="btn btn-xs btn-danger" onclick="return confirm('Sei sicuro di voler eliminare questa notifica?');">
												<i class="fa fa-trash"></i> Elimina
											</a>
										</td>
									</tr>
								<?php endforeach; ?>
							<?php else: ?>
								<tr>
									<td colspan="7">Nessuna notifica da visualizzare</td>
								</tr>
							<?php endif; ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	function deletePdf(id) {
		if (confirm('Sei sicuro di voler eliminare il PDF e la relativa cartella?')) {
			window.location.href = '<?php echo base_url('scadenzario/delete_pdf/') ?>' + id;
		}
	}
</script>