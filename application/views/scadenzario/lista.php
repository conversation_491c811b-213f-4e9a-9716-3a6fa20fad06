<style type="text/css">
	.tablefooter {
		border-top: 2pt solid black;
	}

	div.dataTables_filter {
		text-align: left !important;
	}
</style>
<?php
$campi = array(
	'ragsoc' => 'ragsoc',
	'conto' => '#cliente',
	'anno' => 'anno',
	'partita' => 'partita',
	'scadenzaita' => 'scadenza',
	'rata' => 'rata',
	'importo' => 'importo',
	'data' => 'data',
	'importoscaduto' => 'importo scaduto',
	'importoascadere' => 'importo a scadere',
);
$conti = 6;
?>
<div class="box">
	<div class="overlay">
		<i class="fa fa-refresh fa-spin"></i>
	</div>
	<box class="body">
		<table class="table table-hover" id="tabelladati">
			<thead>
				<tr>
					<?php foreach ($campi as $campo => $titolo) {
						echo '<th class="text-nowrap font-weight-bold">' . $titolo . '</th>';
					} ?>
					<th> </th>
				</tr>
			</thead>
			<tbody>
				<?php foreach ($sc as $row) {
					echo '<tr>';
					foreach ($campi as $campo => $v) {
						echo '<td class="text-nowrap">' . $row->{$campo} . '</td>';
					} ?>
					<td data-search="<?php if (!$row->pagato) echo "0";
										else echo "1"; ?>"><?php if (!$row->pagato) : ?>
							<a href="<?php echo base_url('scadenzario/modifica/' . $row->idscadenzario) ?>" class="btn btn-primary">Apri</a>
						<?php else : ?>
							<a href="<?php echo base_url('scadenzario/modifica/' . $row->idscadenzario) ?>" class="btn btn-success">Riscosso</a>
						<?php endif; ?>
					</td>
				<?php
					echo '</tr>';
				} ?>
			</tbody>
		</table>
	</box>
</div>
<script type="text/javascript">
	var table;
	$(function() {
		table = $("#tabelladati").on('processing.dt', function(e, settings, processing) {
			$('#processingIndicator').css('display', processing ? 'block' : 'none');
		}).on('init.dt', function() {
			$(".overlay").remove();
			// console.log( 'Table initialisation complete: '+new Date().getTime() );
		}).DataTable({
			"dom": '<"filtri"f>tip',
			"language": {
				"url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
			},
			responsive: false,
			"scrollX": true,
			"scrollY": "50vh",
			paging: false,
			"scrollCollapse": true,
			// "orderFixed": [
			// 	[0, 'asc']
			// ],
			orderFixed: [
				[0, 'asc'],
				[7, 'asc'],
				[3, 'asc'],
			],
			"columnDefs": [{
					"orderable": false,
					"targets": [0, 1, 2, 3, 4, 5, 6, 7, 8]
				},
				// {
				// 	targets: [0, 1],
				// 	visible: false
				// },
				// { responsivePriority: 1, targets: [5] },
				// { responsivePriority: 2, targets: -1 }
				{
					"targets": [7],
					"render": $.fn.dataTable.render.moment('YYYY-MM-DD', 'DD-MM-YYYY')
				}
			],

			stateSave: false,
			rowGroup: {
				dataSrc: <?php echo array_search('ragsoc', array_keys($campi)) ?>,
				startRender: function(rows, group) {
					var codice = rows.data().pluck(<?php echo array_search('conto', array_keys($campi)) ?>);
					return $('<tr>')
						.append('<td style="border-top: 2px solid black" colspan="<?php echo count($campi) ?>">' + group + ' #' + codice[0])
						.append('</td>')
						.append('<td style="border-top: 2px solid black; text-align: right;"><a style="font-size: 1.5em" href="<?= base_url('scadenzario/cliente2pdf/') ?>' + codice[0] + '" alt="PDF" target="_blank"><i class="fa fa-file-pdf"></i></a>')
						.append('</td>')
						.append('</tr>');
				},
				endRender: function(rows, group) {
					var importo = rows
						.data()
						.pluck(<?php echo $conti ?>)
						.reduce(function(a, b) {
							// console.log('a='+a+' b='+b);
							return a * 1 + b * 1;
						});
					importo = $.fn.dataTable.render.number(',', '.', 2, '€').display(importo);

					var scaduto = rows
						.data()
						.pluck(<?php echo $conti + 2 ?>)
						.reduce(function(a, b) {
							return a * 1 + b * 1;
						});
					scaduto = $.fn.dataTable.render.number(',', '.', 2, '€').display(scaduto);

					var ascadere = rows
						.data()
						.pluck(<?php echo $conti + 3 ?>)
						.reduce(function(a, b) {
							return a * 1 + b * 1;
						});
					ascadere = $.fn.dataTable.render.number(',', '.', 2, '€').display(ascadere);

					return $('<tr class="tablefooter">')
						.append('<th style="border-top: 1px solid black" colspan="<?php echo $conti ?>">Totale ' + group)
						.append('<th style="border-top: 1px solid black" >' + importo + '</th>')
						.append('<th style="border-top: 1px solid black"/>')
						.append('<th style="border-top: 1px solid black" >' + scaduto + '</th>')
						.append('<th style="border-top: 1px solid black" >' + ascadere + '</th>')
						.append('<th style="border-top: 1px solid black" ></th>')
						// .append( '<th colspan="<?php echo count($campi) ?>">Importo: '+importo+' Scaduto: '+scaduto+' Scadere: '+ascadere)
						// .append('</th>' )
						.append('</tr>');
				},

			},
			"initComplete": function(settings, json) {
				$('#tabelladati_filter').append(' <div class="form-group"><label for="filtri">Filtra: <select name="filtro" id="filtro" class="chosen-select"> <option value="">Tutti</option> <option value="1">Riscosso</option> <option value="0">Non riscosso</option></select></label></div>');
				var filtro = $('select[name="filtro"]');
				$(filtro).change(function() {
					table.columns(<?php echo $conti + 4 ?>).search(filtro.val());
					table.draw();
				})
			}
		});
	});
</script>
<?php
// var_dump($sc);