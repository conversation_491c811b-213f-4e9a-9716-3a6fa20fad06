<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <style>
    .tabella {
      font-size: 10pt;
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .tabella th {
      padding-top: 12px;
      padding-bottom: 12px;
      text-align: left;
      background-color: #26a766;
      color: white;
      vertical-align: middle;
    }

    .tabella th.totale {
      /*text-align: right;*/
      font-size: 12pt;
      font-weight: bold;
    }

    .tabella td {
      font-size: 10pt;
    }

    .tabella .align-right {
      text-align: right;
    }

    .tabella .align-center {
      text-align: center;
    }

    .tfooter {
      border-top: 2px solid #000;
      background-color: #dddddd;
    }
  </style>
</head>

<body>
  <table class="tabella">
    <thead>
      <tr>
        <th class="align-center">anno</th>
        <th class="align-center">partita</th>
        <th class="align-center">scadenza</th>
        <th class="align-center">rata</th>
        <th class="align-right">importo</th>
        <th class="align-center">data</th>
        <th class="align-right">importo scaduto</th>
        <th class="align-right">importo a scadere</th>
      </tr>
    </thead>
    <tbody>
      <?php
      $tot_importo = 0;
      $tot_importoscaduto = 0;
      $tot_importoascadere = 0;
      foreach ($sc as $row) {
        $tot_importo += $row->importo;
        $tot_importoscaduto += $row->importoscaduto;
        $tot_importoascadere += $row->importoascadere;
      ?>
        <tr>
          <td class="align-center"><?= $row->anno ?></td>
          <td class="align-center"><?= $row->partita ?></td>
          <td class="align-center"><?= $row->scadenzaita ?></td>
          <td class="align-center"><?= $row->rata ?></td>
          <td class="align-right">€<?= $row->importo ?></td>
          <td class="align-center"><?= $row->dataita ?></td>
          <td class="align-right">€<?= $row->importoscaduto ?></td>
          <td class="align-right">€<?= $row->importoascadere ?></td>
        </tr>
      <?php } ?>
    </tbody>
    <tfoot>
      <tr class="tfooter">
        <td></td>
        <td></td>
        <td></td>
        <td></td>
        <td class="align-right">€<?= number_format($tot_importo, 2, '.', '') ?></td>
        <td></td>
        <td class="align-right">€<?= number_format($tot_importoscaduto, 2, '.', '') ?></td>
        <td class="align-right">€<?= number_format($tot_importoascadere, 2, '.', '') ?></td>
      </tr>
    </tfoot>
  </table>
  <br>
  <br>
  <br>
  <h4>Agente</h4>
  <?php
  if ($agente) {
    echo $agente->nome . ' ' . $agente->cognome . "<br>";
    echo $agente->email . "<br>";
    echo $agente->telefono;
  }
  ?>
</body>

</html>