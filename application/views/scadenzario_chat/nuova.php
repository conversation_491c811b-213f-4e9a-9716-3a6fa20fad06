<div class="container">
    <div class="row">
        <div class="col-md-8 col-md-offset-2 col-sm-10 col-sm-offset-1">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <h3 class="panel-title">
                        <i class="fa fa-comments-o margin-right-10"></i>Nuova Chat per Scadenzario
                    </h3>
                </div>
                <div class="panel-body">
                    <div class="alert alert-info">
                        <i class="fa fa-info-circle margin-right-10"></i>
                        <strong>Scadenzario:</strong> <?php echo htmlspecialchars($scadenzario->ragsoc); ?> 
                        <small class="text-muted">(Rif. <?php echo htmlspecialchars($scadenzario->id); ?>)</small>
                    </div>

                    <?php echo form_open('ScadenzarioChat/crea', ['class' => 'form-horizontal']); ?>
                        <input type="hidden" name="id_scadenzario" value="<?php echo $id_scadenzario; ?>">
                        
                        <div class="form-group">
                            <label for="titolo" class="col-sm-3 control-label">Titolo della Chat</label>
                            <div class="col-sm-9">
                                <div class="input-group">
                                    <span class="input-group-addon"><i class="fa fa-comment"></i></span>
                                    <input type="text" 
                                           class="form-control" 
                                           id="titolo" 
                                           name="titolo" 
                                           placeholder="Inserisci un titolo per la chat" 
                                           required 
                                           maxlength="255"
                                           pattern=".{3,}">
                                </div>
                                <small class="help-block text-muted">Minimo 3 caratteri</small>
                            </div>
                        </div>

                        <?php if ($this->session->ruolo !== 'Admin'): ?>
                        <div class="form-group">
                            <label for="responsabile_predefinito" class="col-sm-3 control-label">Admin responsabile</label>
                            <div class="col-sm-9">
                                <select name="responsabile_predefinito" id="responsabile_predefinito" class="form-control" required>
                                    <option value="">-- Seleziona Admin --</option>
                                    <?php foreach($admins as $admin): ?>
                                        <option value="<?php echo $admin->idutente; ?>"><?php echo $admin->nome . ' ' . $admin->cognome; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                        <?php endif; ?>

                        <div class="form-group">
                            <div class="col-sm-offset-3 col-sm-9">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-paper-plane margin-right-10"></i>Crea Chat
                                </button>
                                <a href="<?php echo base_url('scadenzario'); ?>" class="btn btn-default">
                                    <i class="fa fa-times margin-right-10"></i>Annulla
                                </a>
                            </div>
                        </div>
                    <?php echo form_close(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.margin-right-10 {
    margin-right: 10px;
}
</style>

<script>
$(document).ready(function() {
    $('form').on('submit', function(e) {
        var $titolo = $('#titolo');
        if ($titolo.val().length < 3) {
            e.preventDefault();
            $titolo.closest('.form-group').addClass('has-error');
            $titolo.focus();
        }
    });
});
</script>
