<div class="container-fluid">
    <div class="row">
        <div class="col-xs-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h3 class="panel-title">Lista Chat</h3>
                </div>
                <div class="panel-body">
                    <div class="row mb-15">
                        <div class="col-xs-12">
                            <form id="chat-filter-form" class="form-inline">
                                <div class="form-group mr-10">
                                    <select id="chat-status-filter" class="form-control input-sm">
                                        <option value="">Tutti gli stati</option>
                                        <option value="aperta">Aperte</option>
                                        <option value="chiusa">Chiuse</option>
                                    </select>
                                </div>
                                <div class="form-group mr-10">
                                    <input type="text" id="cliente-filter" class="form-control input-sm" placeholder="Filtra per cliente">
                                </div>
                                <?php if($this->session->ruolo == 'Admin'): ?>
                                <div class="form-group mr-10">
                                    <select id="agente-filter" class="form-control input-sm">
                                        <option value="">Tutti gli agenti</option>
                                        <?php foreach($agenti as $agente): ?>
                                            <option value="<?php echo $agente->idutente; ?>"><?php echo $agente->nome . ' ' . $agente->cognome; ?></option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <?php endif; ?>
                                <div class="form-group mr-10">
                                    <div class="input-group">
                                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                                        <input type="text" id="search-filter" class="form-control input-sm" placeholder="Cerca...">
                                        <span class="input-group-btn">
                                            <button id="reset-filters" class="btn btn-default btn-sm" type="button">
                                                <i class="fa fa-times"></i>
                                            </button>
                                        </span>
                                    </div>
                                    <button id="apply-filter" class="btn btn-primary btn-sm" type="button">
                                        Filtra
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <table id="chat-list-table" class="table table-bordered table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Cliente</th>
                                <th>Titolo chat</th>
                                <th>Stato</th>
                                <th>Data Creazione</th>
                                <?php if($this->session->ruolo == 'Admin'): ?>
                                <th>Agente</th>
                                <?php endif; ?>
                                <th>Interlocutori</th>
                                <th>Azioni</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($chats as $chat): ?>
                            <tr>
                                <td><?php echo $chat->scadenzario_ragsoc; ?></td>
                                <td><?php echo $chat->titolo; ?></td>
                                <td><?php echo $chat->chiusa ? '<span class="label label-danger">Chiusa</span>' : '<span class="label label-success">Aperta</span>'; ?></td>
                                <td><?php echo date('d-m-Y H:i', strtotime($chat->data_creazione)); ?></td>
                                <?php if($this->session->ruolo == 'Admin'): ?>
                                <td><?php echo $chat->nome_agente ?: '<em>Nessun agente</em>'; ?></td>
                                <?php endif; ?>
                                <td><?php echo $chat->interlocutori ?: '<em>Nessun messaggio</em>'; ?></td>
                                <td>
                                    <a href="<?php echo base_url('ScadenzarioChat/dettaglio/' . $chat->id); ?>" class="btn btn-xs btn-info">Apri</a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Filtro dinamico per la tabella delle chat
    $('#chat-filter-form').on('change', 'select, input', function() {
        // filterChatTable(); // Disabilitato per attivare solo con click su "Filtra"
    });

    $('#apply-filter').on('click', function() {
        filterChatTable();
    });

    function filterChatTable() {
        var status = $('#chat-status-filter').val();
        var cliente = $('#cliente-filter').val().toLowerCase();
        var agente = $('#agente-filter').val();
        var searchText = $('#search-filter').val().toLowerCase();

        $('#chat-list-table tbody tr').each(function() {
            var row = $(this);
            var rowStatus = row.find('td:nth-child(3) .label').hasClass('label-success') ? 'aperta' : 'chiusa';
            var rowCliente = row.find('td:nth-child(1)').text().toLowerCase();
            var rowAgente = row.find('td:nth-child(5)').text().toLowerCase();
            var rowText = row.text().toLowerCase();

            var statusMatch = !status || rowStatus === status;
            var clienteMatch = !cliente || rowCliente.includes(cliente);
            var agenteMatch = !agente || rowAgente.includes($('#agente-filter option:selected').text().toLowerCase());
            var searchMatch = !searchText || rowText.includes(searchText);

            row.toggle(statusMatch && clienteMatch && agenteMatch && searchMatch);
        });
    }

    // Autocomplete per cliente
    $('#cliente-filter').autocomplete({
        source: function(request, response) {
            $.ajax({
                url: '<?php echo base_url("ScadenzarioChat/getClienti"); ?>',
                dataType: "json",
                data: { term: request.term },
                success: function(data) {
                    response(data);
                }
            });
        },
        minLength: 2
    });

    // Reset dei filtri
    $('#reset-filters').on('click', function() {
        $('#chat-status-filter').val('');
        $('#cliente-filter').val('');
        $('#search-filter').val('');
        filterChatTable();
    });
});
</script>
