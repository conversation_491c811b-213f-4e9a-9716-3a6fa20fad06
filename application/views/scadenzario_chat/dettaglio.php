<div class="container-fluid chat-container">
    <div class="row">
        <div class="col-12">
            <div class="card chat-card">
                <div class="card-header chat-header">
                    <div class="chat-header-content">
                        <div>
                            <i class="fas fa-comments mr-2"></i>
                            <span class="chat-title"><?php echo htmlspecialchars($chat->titolo); ?></span>
                            <div class="chat-details text-muted" style="font-size: 0.8em; margin-top: 5px;">
                                <strong>Cliente:</strong> <?php echo htmlspecialchars($scadenzario->ragsoc); ?> 
                                | <strong>Anno:</strong> <?php echo htmlspecialchars($scadenzario->anno); ?> 
                                | <strong>Conto:</strong> <?php echo htmlspecialchars($scadenzario->conto); ?> 
                                | <strong>Partita:</strong> <?php echo htmlspecialchars($scadenzario->partita); ?>
                            </div>
                        </div>
                        <div class="chat-status">
                            <?php if ($this->session->ruolo == 'Admin'): ?>
                                <a href="<?php echo base_url('ScadenzarioChat/toggleChatStatus/' . $chat->id); ?>" class="badge <?php echo $chat->chiusa ? 'label-danger' : 'label-success'; ?> toggle-chat-status">
                                    <i class="fas <?php echo $chat->chiusa ? 'fa-lock' : 'fa-unlock'; ?> mr-1"></i>
                                    <?php echo $chat->chiusa ? 'Chiusa il ' . date('d/m/Y H:i', strtotime($chat->data_chiusura)) : 'Aperta'; ?>
                                </a>
                            <?php else: ?>
                                <span class="badge <?php echo $chat->chiusa ? 'label-danger' : 'label-success'; ?>">
                                    <i class="fas <?php echo $chat->chiusa ? 'fa-lock' : 'fa-unlock'; ?> mr-1"></i>
                                    <?php echo $chat->chiusa ? 'Chiusa il ' . date('d/m/Y H:i', strtotime($chat->data_chiusura)) : 'Aperta'; ?>
                                </span>
                            <?php endif; ?>
                        </div>
                        <div class="chat-reload">
                            <a href="#" class="badge label-primary" onclick="window.location.reload();"><i class="fas fa-sync-alt"></i> Ricarica</a>
                        </div>
                    </div>
                </div>
                
                <div class="card-body chat-body">
                    <div class="chat-messages">
                        <?php 
                        $current_date = null;
                        foreach ($messaggi as $messaggio): 
                            $message_date = date('Y-m-d', strtotime($messaggio->data_ora));
                            if ($current_date !== $message_date): 
                                $current_date = $message_date;
                        ?>
                            <div class="message-date-separator">
                                <span><?php echo date('d/m/Y', strtotime($current_date)); ?></span>
                            </div>
                            <?php endif; ?>
                            <div class="message-wrapper <?php echo $messaggio->id_utente == $this->session->idutente ? 'sent' : 'received'; ?>">
                                <div class="message-box">
                                    <div class="message-header">
                                        <span class="message-sender">
                                            <i class="fas fa-user mr-1"></i>
                                            <?php echo htmlspecialchars($messaggio->nome . ' ' . $messaggio->cognome); ?>
                                        </span>
                                        <span class="message-time">
                                            <i class="fas fa-clock mr-1"></i>
                                            <?php echo date('H:i', strtotime($messaggio->data_ora)); ?>
                                        </span>
                                    </div>
                                    <div class="message-content">
                                        <?php echo nl2br(htmlspecialchars($messaggio->messaggio)); ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                
                <?php if (!$chat->chiusa): ?>
                <div class="card-footer chat-footer">
                    <?php echo form_open('ScadenzarioChat/invia_messaggio', ['id' => 'chat-message-form', 'class' => 'chat-form']); ?>
                        <input type="hidden" name="id_chat" value="<?php echo $chat->id; ?>">
                        <div class="chat-input-group">
                            <textarea name="messaggio" placeholder="Scrivi un messaggio..." class="chat-textarea" required></textarea>
                            <button type="submit" class="chat-send-btn">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    <?php echo form_close(); ?>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>
<style>
.chat-header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var chatMessages = document.querySelector('.chat-messages');
    chatMessages.scrollTop = chatMessages.scrollHeight;

    var form = document.getElementById('chat-message-form');
    var textarea = form.querySelector('textarea[name="messaggio"]');
    
    textarea.addEventListener('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });

    form.addEventListener('submit', function(e) {
        if (textarea.value.trim() === '') {
            e.preventDefault();
            textarea.focus();
        }
    });
});
</script>
