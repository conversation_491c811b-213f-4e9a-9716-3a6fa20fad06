<?php
function chkbox(&$row, $campo) {
  $chkboxid='row-'.$row['idutente'].'-'.$campo;
  $v=$row[$campo];
  $row[$campo]=form_checkbox($chkboxid, "1", $v, 'id="'.$chkboxid.'"');
}
function slct_ruolo(&$row, $campo) {
  $options = array(
          ''              => '',
          'Admin'         => 'Admin',
          'RdC'           => 'RdC',
          'Educatore'     => 'Educatore',
  );
  $chkboxid='row-'.$row['idutente'].'-'.$campo;
  $v=$row[$campo];
  $row[$campo]=form_dropdown($chkboxid, $options, $v, 'id="'.$chkboxid.'" val="'.$v.'"');
}
$add = '<!-- DataTables -->

  <link rel="stylesheet" href="'.asset_url('plugins/datatables/datatables.min.css').'">';
$this->load->view('common/header', ['title' => 'Gestione ruoli e utenti', 'add' => $add]); ?>
    <!-- Main content -->
<section class="content no-padding-xs">
  <div class="row">
    <div class="col-xs-12">
      <div class="box">
        <div class="box-body">


<?php
$template = array(
        'table_open'  => '<table id="tabelladati" class="table table-striped">'
);

$this->table->set_template($template);
$campi=array_keys($data[0]);
foreach ($data as $row) {
  chkbox($row, 'approvato');
  chkbox($row, 'bloccato');
  slct_ruolo($row, 'ruolo');

  $chkboxid='row-'.$row['idutente'].'-idutente';
  array_unshift($row, form_checkbox($chkboxid, "1", false, 'id="'.$chkboxid.'"')); //
  $this->table->add_row($row);
}
array_unshift($campi, '');
$this->table->set_heading($campi);
echo $this->table->generate();
echo form_button('submit', 'modifica', 'class="btn" id="button"');
?>


</div>
</div>
</div>
</div>
</section>



<?php
$add = '
<!-- DataTables -->
<script src="'.asset_url('plugins/datatables/datatables.min.js').'"></script>

<!-- page script -->
<script>
  $(function () {
    var table = $(\'#tabelladati\').DataTable({
      "paging": true,
      "lengthChange": true,
      "searching": true,
      "ordering": true,
      "info": true,
      "autoWidth": false,
      // columnDefs: [ {
      //             orderable: false,
      //             className: \'select-checkbox\',
      //             targets:   0
      //         } ],
      // select: {
      //             style:    \'os\',
      //             selector: \'td:first-child\'
      //         },
      buttons: [
              \'copy\', \'excel\', \'pdf\'
          ],
      "language": {
                    "url": "'.asset_url('plugins/datatables/datatables.italian.lang').'"
                  }
      //order: [[ 1, \'asc\' ]]
    });
  table.buttons().container()
      .appendTo( $(\'.col-sm-6:eq(0)\', table.table().container() ) );
  $(\'#button\').click( function() {
          var data = table.$(\'input, select\').serialize();
          alert(
              "The following data would have been submitted to the server: \n\n"+
              data.substr( 0, 120 )+\'...\'
          );
          return false;
      } );
  });
</script>';
$this->load->view('common/footer', ['add' => $add]); ?>