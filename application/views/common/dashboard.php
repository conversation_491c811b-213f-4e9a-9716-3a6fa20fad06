<!-- Main content -->
<section class="content no-padding-xs">
	<!-- Small boxes (Stat box) -->
	<div class="row">
		<!-- Left col -->
		<section class="col-lg-7 connectedSortable">
			<!-- Chat box -->
			<div class="box box-success">
				<div class="box-header">
					<h2><i class="fa fa-dashboard"> </i> Pannello di controllo</h2>
					<div class="" style="padding-left: 40px;color: #aaa;padding-bottom: 15px;">
						<?php
						$mesi = array(
							1 => 'gennaio', 'febbraio', 'marzo', 'aprile',
							'maggio', 'giugno', 'luglio', 'agosto',
							'settembre', 'ottobre', 'novembre', 'dicembre'
						);
						$giorni = array(
							'domenica', 'lunedì', 'martedì', 'mercoledì',
							'giovedì', 'venerdì', 'sabato'
						);
						list($sett, $giorno, $mese, $anno) = explode('-', date('w-d-n-Y'));
						echo "<span style='font-size: 20px;'>";
						echo $giorni[$sett], ' - ', $giorno, ' ', $mesi[$mese], ' ', $anno;
						echo "</span>";
						echo "<br/>";
						?>
					</div>
					<?php if ($this->session->ruolo == 'Admin') : ?>
						<div style="text-align:left; margin-left:40px;">
							<span style="color:#999;">AGENTI: <?php echo $nagenti ?></span>
						</div>
					<?php endif; ?>
					<div style="text-align:left; margin-left:40px;">
						<span style="color:#999;">CLIENTI: <?php echo $nclienti ?></span>
					</div>
					<div style="text-align:left; margin-left:40px;">
						<span style="color:#999;">ORDINI: <?php echo $totaleordini ?></span>
						<h3 style="margin-top: 0;color: #3C8DBC;"></h3>
						<ul>
							<li>Da inviare: <?php echo $totaledainviare ?></li>
							<li>Inviati: <?php echo $totaleinviati ?></li>
						</ul>
					</div>
					<?php if (is_file('./upload/listino.xls')) echo "Ultimo aggiornamento del listino: " . date("d/m/Y H:i:s", filectime('./upload/listino.xls')); ?><br>
					<?php if (is_file('./upload/scadenzario.xlsx')) echo "Ultimo aggiornamento dello scadenzario: " . date("d/m/Y H:i:s", filectime('./upload/scadenzario.xlsx')); ?>
				</div>
				<!-- /.chat -->
			</div>
			<!-- /.box (chat box) -->
		</section>
		<!-- /.Left col -->
		<!-- right col (We are only adding the ID to make the widgets sortable)-->
		<section class="col-lg-5 connectedSortable">
		</section>
		<!-- right col -->
	</div>
	<!-- /.row (main row) -->
</section>
<!-- /.content -->
<?php /*
$data = ["add" => "<!-- AdminLTE dashboard demo (This is only for demo purposes) -->
<script src='".asset_url('dist/js/pages/dashboard.js')."'></script>"];
$this->load->view('parts/footer', $data); ?>*/