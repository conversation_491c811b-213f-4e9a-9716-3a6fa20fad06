<?php
$add = '<!-- DataTables -->
  <link rel="stylesheet" href="'.asset_url('plugins/datatables/dataTables.bootstrap.css').'">';
$this->load->view('common/header', ['title' => 'Data Tables', 'add' => $add]); ?>
    <!-- Main content -->
    <section class="content no-padding-xs">
      <div class="row">
        <div class="col-xs-12">
          <div class="box">
            <div class="box-header">
              <h3 class="box-title"><?php echo $title ?></h3>
            </div>
            <!-- /.box-header -->
            <div class="box-body">
              <table id="tabelladati" class="table table-bordered table-hover">
                <thead>
<?php
  $campi=array_keys(get_object_vars($data[0]));
  echo '<tr>';
  foreach ($campi as $campo) {
    echo '<th name="'.$campo.'">'.$campo.'</th>';
  }
  echo '</tr>';
?>
                </thead>
                <tbody>
<?php foreach ($data as $row) {
    echo '<tr>';
    foreach ($campi as $campo) {
      echo '<td>'.$row->{$campo}.'</td>';
    }
    echo '</tr>';
} ?>

                </tbody>
                <tfoot>
<?php
  echo '<tr>';
  foreach ($campi as $campo) {
    echo '<th>'.$campo.'</th>';
  }
  echo '</tr>';
?>
                </tfoot>
              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->
<?php
$add = '
<!-- DataTables -->
<script src="'.asset_url('plugins/datatables/jquery.dataTables.min.js').'"></script>
<script src="'.asset_url('plugins/datatables/dataTables.bootstrap.min.js').'"></script>
<!-- page script -->
<script>
  $(function () {
    $("#tabelladati").DataTable({
      initComplete: function () {
        this.api().columns().every( function () {
            var column = this;
            // console.log(this.index());
            //var colcerca=[4,5];
            if (typeof colcerca === \'undefined\' || colcerca === null) {
                var colcerca=[];
            }
            if (colcerca.includes(this.index())) {
              var select = $(\'<select><option value="">ruolo</option></select>\')
                  .appendTo( $(column.header()).empty() )
                  .on( \'change\', function () {
                      var val = $.fn.dataTable.util.escapeRegex(
                          $(this).val()
                      );

                      column
                          .search( val ? \'^\'+val+\'$\' : \'\', true, false )
                          .draw();
                  } );

              column.data().unique().sort().each( function ( d, j ) {
                  select.append( \'<option value="\'+d+\'">\'+d+\'</option>\' )
              } );
            }
        } );
      }
    });
    $(\'#example2\').DataTable({
      "paging": true,
      "lengthChange": false,
      "searching": false,
      "ordering": true,
      "info": true,
      "autoWidth": false
    });
  });
</script>';
$this->load->view('common/footer', ['add' => $add]); ?>