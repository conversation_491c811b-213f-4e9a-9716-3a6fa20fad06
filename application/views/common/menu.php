  <aside class="main-sidebar">
    <section class="sidebar">
      <div class="user-panel">
        <div class="pull-left image">
          <img src="<?php echo $this->session->foto ?>" class="img-circle" alt="User Image">
        </div>
        <div class="pull-left info">
          <p><?= $this->session->name; ?></p>
          <a href="#"><i class="fa fa-circle text-success"></i> Online</a>
        </div>
      </div>
      <ul class="sidebar-menu">
        <li class="header">MAIN MENU</li>
        <?php if ($this->session->ruolo == 'Admin') : ?>
          <li class="treeview">
            <a href="#">
              <i class="fa fa-users"></i>
              <span>Agenti</span>
              <span class="pull-right-container">
                <i class="fa fa-angle-left pull-right"></i>
              </span>
            </a>
            <ul class="treeview-menu">
              <li><a href="<?= base_url('Admin/agente_nuovo'); ?>"><i class="fa fa-plus-circle"></i> <span>Nuovo</span></a></li>
              <li><a href="<?= base_url('Admin/agente_lista'); ?>"><i class="fa fa-user"></i> <span>Lista</span></a></li>
            </ul>
          </li>
        <?php endif; ?>

        <li class="treeview">
          <a href="#">
            <i class="fa fa-users"></i>
            <span>Clienti</span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?= base_url('agente/cliente_nuovo'); ?>"><i class="fa fa-plus-circle"></i> <span>Nuovo</span></a></li>
            <li><a href="<?= base_url('agente/cliente_lista'); ?>"><i class="fa fa-user"></i> <span>Lista</span></a></li>
            <li><a href="<?= base_url('agente/cliente_elencoglobale/attivi'); ?>"><i class="fa fa-user"></i> <span>Lista attivi</span></a></li>
            <li><a href="<?= base_url('agente/cliente_elencoglobale/nonattivi'); ?>"><i class="fa fa-user"></i> <span>Lista non attivi</span></a></li>
            <li><a href="<?= base_url('agente/ordini_lista'); ?>"><i class="fa fa-file"></i> <span>Ordini</span></a></li>
            <?php /** if ($this->session->ruolo=='Admin') : ?>
            <li class="header"><a href="#">Caricamento</a></li>
            <li><a href="<?=base_url('admin/caricaexcel_clienti');?>"><i class="fa fa-upload"></i> <span>Carica clienti</span></a></li>
            <li><a href="<?=base_url('admin/clienti2db');?>"><i class="fa fa-database"></i> <span>Aggiorna clienti</span></a></li>
            <?php endif; */ ?>
          </ul>
        </li>
        <li class="treeview">
          <a href="#">
            <i class="fa fa-barcode"></i>
            <span>Listino</span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul class="treeview-menu">
            <?php /**
            <?php if ($this->session->ruolo=='Admin') : ?>
            <li><a href="<?=base_url('admin/caricaexcel');?>"><i class="fa fa-upload"></i> <span>Carica</span></a></li>
            <li><a href="<?=base_url('admin/listino2db');?>"><i class="fa fa-database"></i> <span>Aggiorna listino</span></a></li>
            <?php endif; ?>
             */ ?>
            <li><a href="<?= base_url('agente/listino'); ?>"><i class="fa fa-file-excel-o"></i> <span>Visualizza</span></a></li>
          </ul>
        </li>
        <li class="treeview">
          <a href="#">
            <i class="fa fa-file-pdf-o"></i>
            <span>Cataloghi</span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul class="treeview-menu">
            <?php if ($this->session->ruolo == 'Admin') : ?>
              <li><a href="<?= base_url('admin/cataloghicarica'); ?>"><i class="fa fa-upload"></i> <span>Carica</span></a></li>
            <?php endif; ?>
            <li><a href="<?= base_url('agente/cataloghi'); ?>"><i class="fa fa-file-excel-o"></i> <span>Elenco</span></a></li>
          </ul>
        </li>
        <li class="treeview">
          <a href="#">
            <i class="fa fa fa-calendar"></i>
            <span>Scadenzario</span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?= base_url('scadenzario'); ?>"><i class="fa fa-list"></i> <span>Lista</span></a></li>
            <li><a href="<?= base_url('ScadenzarioChat'); ?>"><i class="fa fa-comments"></i> <span>Lista Chat</span></a></li>
            <li><a href="<?= base_url('scadenzario/allClientsToPdf'); ?>"><i class="fa fa-file-pdf-o"></i> <span>PDF Tutti i clienti</span></a></li>
            <?php if ($this->session->ruolo == 'Admin') : ?>
              <hr style="height: 1px;">
              <li><a href="<?= base_url('scadenzario/caricaexcel'); ?>"><i class="fa fa-upload"></i> <span>Carica XLS</span></a></li>
              <li><a href="<?= base_url('scadenzario/scadenzario2db'); ?>"><i class="fa fa-database"></i> <span>Aggiorna da XLS</span></a></li>
              <hr style="height: 1px;">
              <li><a href="<?= base_url('scadenzario/importJG'); ?>"><i class="fa fa-database"></i> <span>Aggiorna da JG</span></a></li>
            <?php endif; ?>
          </ul>
        </li>
        <li class="treeview">
          <a href="#">
            <i class="fa fa fa-list"></i>
            <span>Preventivi</span>
            <span class="pull-right-container">
              <i class="fa fa-angle-left pull-right"></i>
            </span>
          </a>
          <ul class="treeview-menu">
            <li><a href="<?= base_url('preventivi/preventivi_lista'); ?>"><i class="fa fa-list"></i> <span>Lista</span></a></li>
            <li><a href="<?= base_url('preventivi/preventivi_nuovo'); ?>"><i class="fa fa-plus-circle"></i> <span>Nuovo</span></a></li>
          </ul>
        </li>
        <?php if ($this->session->ruolo == 'Agente') : ?>
          <li class="nav-item"><a href="<?= base_url('Agente/agente_modifica'); ?>"><i class="fa fa-user"></i> <span>Modifica profilo</span></a></li>
        <?php endif; ?>
        <?php if ($this->session->ruolo == 'Admin') : ?>
          <li class="treeview">
            <a href="#">
              <i class="fa fa-database"></i>
              <span>Sincronizzazione</span>
              <span class="pull-right-container">
                <i class="fa fa-angle-left pull-right"></i>
              </span>
            </a>
            <ul class="treeview-menu">
              <li><a href="<?= base_url('#'); ?>"><i class="fa fa-database"></i> <span>Listino</span></a></li>
              <li><a href="<?= base_url('#'); ?>"><i class="fa fa-database"></i> <span>Ordini</span></a></li>
            </ul>
          </li>
        <?php endif; ?>
      </ul>
    </section>
    <!-- /.sidebar -->
  </aside>