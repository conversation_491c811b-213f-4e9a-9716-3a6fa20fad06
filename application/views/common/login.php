<div class="login">
<div class="row">
  <?php //if($this->session->flashdata('msg')): ?>
    <div class="alert alert-<?php echo $this->session->flashdata('msgtype') ?>" role="alert"><?php echo $this->session->flashdata('msg') ?></div>
  <?php //endif ?>
  <div style="text-align:center;" class="col-lg-12 col-md-12 col-sm-12">
     <img alt="Logo luccacarta" class="thumbnail" src="<?php echo asset_url('img/logoalpha.png') ?>"  />
  </div>
</div>

<div class="row">

      <div class="col-lg-12 col-md-12 col-sm-12">
	  <?php //include ("pannello_popup.php"); ?>
        <div class="panel panel-primary">
          <div class="panel-heading">
          ACCESSO RISERVATO <BR/>
		login
          </div>
          <div class="panel-body">
            <form action="<?php echo base_url('login/do'); ?>" method="post">
              <div class="input-group">
                <!-- <label for="username">Nome utente</label> -->
                <span class="input-group-addon" title="Nome utente"><i class="fa fa-fw fa-user" ></i></span>
                <input type="text" class="form-control" id="username" name="username" placeholder="Nome utente">
              </div>
              <br>
              <div class="input-group">
                <span class="input-group-addon" title="Password"><i class="fa fa-fw fa-lock"></i></span>
                <input type="password" name="password" class="form-control" id="password" placeholder="Password">
              </div>
              <br>

              <button type="submit" class="btn btn-primary btn-block">Accedi</button>
              <br>

              <div class="col-md-12" style="text-align: right;">
                <a href="<?php echo base_url('registrazione/passworddimenticata') ?>">Password dimenticata</a>
              </div>
            </form>
          </div>
        </div>
		<div class="underlogin">© Copyright <?php echo date('Y');?> - Luccacarta<br/>
				<?php
					$mesi = array(1=>'gennaio', 'febbraio', 'marzo', 'aprile',
									'maggio', 'giugno', 'luglio', 'agosto',
									'settembre', 'ottobre', 'novembre','dicembre');

					$giorni = array('domenica','lunedì','marted','mercoledì',
									'giovedì','venerdì','sabato');

					list($sett,$giorno,$mese,$anno) = explode('-',date('w-d-n-Y'));

					echo $giorni[$sett],' - ',$giorno,' ',$mesi[$mese],' ',$anno;
					echo "<br/>";
					echo date('H:i:s');
				?>
		</div>
      </div>
<?php include ("credits.php"); ?>
  </div>
  </div>
	  

  
<?php $this->load->view('common/footer'); ?>