<?php $this->load->view('common/head') ?>

<body class="hold-transition skin-green <?= (!isset($simple) ? 'sidebar-mini' : '') ?> <?= (isset($add_body_class) ? $add_body_class : ''); ?>">
  <?php if (!isset($simple)) { ?>
    <div class="wrapper">
      <header class="main-header">
        <!-- Logo -->
        <a href="<?= base_url(); ?>" class="logo">
          <!-- mini logo for sidebar mini 50x50 pixels -->
          <span class="logo-mini"><b>LC</b></span>
          <!-- logo for regular state and mobile devices -->
          <span class="logo-lg"><b>Luccacarta</b></span>
        </a>
        <!-- Header Navbar: style can be found in header.less -->
        <nav class="navbar navbar-static-top">
          <!-- Sidebar toggle button-->
          <a href="#" class="sidebar-toggle" data-toggle="offcanvas" role="button">
            <span class="sr-only">Toggle navigation</span>
          </a>
          <div class="navbar-custom-menu">
            <ul class="nav navbar-nav">
              <li class="dropdown user user-menu">
                <a href="#" class="dropdown-toggle" data-toggle="dropdown">
                  <!-- <img src="<?php echo $this->session->foto ?>" class="user-image" alt="User Image"> -->
                  <i class="fa fa-user"></i><i class="fa fa-caret-down"></i>
                  <span class="hidden-xs"><?= $this->session->name; ?></span>
                </a>
                <ul class="dropdown-menu">
                  <!-- User image -->
                  <li class="user-header">
                    <img src="<?php echo $this->session->foto ?>" class="img-circle" alt="User Image">
                    <p>
                      <?= $this->session->username; ?> - <?php echo $this->session->ruolo ?>
                      <small>Registrato il: <?php echo $this->session->datacreato ?></small>
                      <small>Ultimo login: <?php echo $this->session->datalogin ?></small>                      
                    </p>
                  </li>
                  <!-- Menu Body -->
                  <!-- Menu Footer-->
                  <li class="user-footer">
                      <div class="pull-left">
                        <a href="<?= base_url('Agente/agente_modifica') ?>" class="btn btn-default btn-flat">Profilo</a>
                      </div>
                    <div class="pull-right">
                      <a href="<?= base_url('logout'); ?>" class="btn btn-default btn-flat">Esci</a>
                    </div>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </nav>
      </header>
      <!-- Left side column. contains the logo and sidebar -->
      <?php $this->load->view('common/menu') ?>
      <!-- Content Wrapper. Contains page content -->
      <div class="content-wrapper">

        <!-- Content Header (Page header) -->
        <section class="content-header">
          <h1>
            <?= $title; ?>
            <!-- <small>Pannello di Controllo</small> -->
          </h1>
          <?php if (isset($breadcrumb)) echo $breadcrumb ?>
        </section>
      <?php } ?>

      <!-- Area messaggi -->
      <?php if ($this->session->flashdata('msg')) {
        if ($this->session->flashdata('msgtype') == '') $msgtype = 'info';
      ?>
        <div class="alert alert-<?php echo $this->session->flashdata('msgtype') ?> alert-dismissible" role="alert">
          <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
          <i class="icon fa fa-<?php echo $this->session->flashdata('msgtype') ?>"></i>
          <?php echo $this->session->flashdata('msg') ?>
        </div>
      <?php } ?>
      <?php if ($this->session->flashdata('err')) { ?>
        <div class="alert alert-danger alert-dismissible">
          <button type="button" class="close" data-dismiss="alert" aria-hidden="true">×</button>
          <h4><i class="icon fa fa-exclamation-circle"></i> Errore</h4>
          <?php echo $this->session->flashdata('err') ?>
        </div>
      <?php } ?>
      <!-- .Area messaggi -->
      <?php
      $myflashdata = new MyFlashdata;
      echo $myflashdata->htmlfromsession();
      ?>