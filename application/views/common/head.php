<!DOCTYPE html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>Luccacar<PERSON> | <?php echo (isset($title)) ? strip_tags($title) : ''; ?></title>
    <!-- Tell the browser to be responsive to screen width -->
    <meta content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no" name="viewport">
    <!-- Bootstrap 3.3.6 -->
    <link rel="stylesheet" href="<?=asset_url('bootstrap/css/bootstrap.min.css')?>">
    <link rel="stylesheet" href="<?php echo asset_url('plugins/select2/select2.min.css') ?>">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Ionicons <?php echo $this->config->item('protocollo'); ?>://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css -->
    <link rel="stylesheet" href="<?php echo asset_url('css/ionicons.min.css') ?>">
    <?=(isset($add) ? $add : '');?>
    <!-- Theme style -->
    <link rel="stylesheet" href="<?=asset_url('dist/css/AdminLTE.css');?>">
    <link rel="stylesheet" href="<?=asset_url('stile.css?v=20240509');?>">
    <!-- AdminLTE Skins. Choose a skin from the css/skins
    folder instead of downloading all of them to reduce the load. -->
    <link rel="stylesheet" href="<?=asset_url('dist/css/skins/skin-green.css');?>">
    <!-- iCheck -->
    <link rel="stylesheet" href="<?=asset_url('plugins/iCheck/flat/blue.css');?>">
    <!-- Morris chart -->
    <link rel="stylesheet" href="<?=asset_url('plugins/morris/morris.css');?>">
    <!-- jvectormap -->
    <link rel="stylesheet" href="<?=asset_url('plugins/jvectormap/jquery-jvectormap-1.2.2.css');?>">
    <!-- Date Picker -->
    <link rel="stylesheet" href="<?=asset_url('plugins/datepicker/datepicker3.css');?>">
    <!-- Daterange picker -->
    <link rel="stylesheet" href="<?=asset_url('plugins/daterangepicker/daterangepicker.css');?>">
    <!-- bootstrap wysihtml5 - text editor -->
    <link rel="stylesheet" href="<?=asset_url('plugins/bootstrap-wysihtml5/bootstrap3-wysihtml5.min.css');?>">
    <link rel="stylesheet" href="<?=asset_url('plugins/iCheck/square/blue.css');?>">
    <link rel="stylesheet" href="<?=asset_url('plugins/datatables-1.12/datatables.min.css');?>">


    <link rel="stylesheet" href="<?=asset_url('plugins/ion.rangeSlider/css/ion.rangeSlider.min.css');?>">

    <!-- <link rel="stylesheet" href="<?=asset_url('plugins/ionslider/ion.rangeSlider.css')?>"> -->
    <!-- ion slider Nice -->
    <!-- <link rel="stylesheet" href="<?=asset_url('plugins/ionslider/ion.rangeSlider.skinNice.css')?>"> -->
    <!-- bootstrap slider -->
    <link rel="stylesheet" href="<?=asset_url('plugins/bootstrap-slider/slider.css')?>">

    <link rel="stylesheet" href="<?=asset_url('plugins/chosen/chosen.min.css')?>">

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
    <script src="<?php echo $this->config->item('protocollo'); ?>://oss.maxcdn.com/html5shiv/3.7.3/html5shiv.min.js"></script>
    <script src="<?php echo $this->config->item('protocollo'); ?>://oss.maxcdn.com/respond/1.4.2/respond.min.js"></script>
    <![endif]-->
    <!-- jQuery 2.2.3 -->
    <!-- <script src="<?=asset_url('plugins/jQuery/jquery-2.2.3.min.js');?>"></script> -->
    <!-- <script src="<?php echo $this->config->item('protocollo'); ?>://cdnjs.cloudflare.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script> -->
    <script src="<?=asset_url('plugins/jQuery/jquery-3.3.1.min.js');?>"></script>
    <script src="<?=asset_url('js/jquery.cookie.js');?>"></script>
    <?php //if(@$jQueryui !== false) { ?>
    <!-- jQuery UI 1.11.4 -->
    <script src="<?php echo $this->config->item('protocollo'); ?>://code.jquery.com/ui/1.11.4/jquery-ui.min.js"></script>
    <!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
    <script>
      $.widget.bridge('uibutton', $.ui.button);
    </script>
    <?php //} ?>
  </head>
