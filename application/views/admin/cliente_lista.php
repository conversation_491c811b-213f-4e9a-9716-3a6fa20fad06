

    <!-- Main content -->
    <section class="content no-padding-xs">
      <div class="row">
        <div class="col-xs-12">
          <div class="box">

            <!-- /.box-header -->
            <div class="box-body">
              <table id="tabelladati" class="table table-bordered table-hover">
                <thead>
<?php
  if (count($data)>0) :
  // $campi=array_keys(get_object_vars($data[0]));
  if ($this->session->ruolo=='Admin') {
    $campi=array(
      'idcliente'=>'# cliente',
      'ragsoc'=>'ragione sociale',
      'piva'=>'piva',
      'sdi'=>'sdi',
      'pec'=>'pec',
      'codice'=>'codice',
      'agente'=>'agente',
      );
  }
  else {
    $campi=array(
      'idcliente'=>'# cliente',
      'ragsoc'=>'ragione sociale',
      'piva'=>'piva',
      'sdi'=>'sdi',
      'pec'=>'pec',
      'codice'=>'codice',
      );
  }
  echo '<tr>';
  foreach ($campi as $campo=>$label) {
    echo '<th name="'.$campo.'">'.$label.'</th>';
  }
  echo '<td></td>';
  echo '</tr>';
?>
                </thead>
                <tbody>
<?php foreach ($data as $row) {
    echo '<tr>';
    foreach ($campi as $campo=>$label) {
      $dato=$row->{$campo};
      echo '<td>'.$dato.'</td>';
    }
    echo '<td nowrap>';
      echo '<a href="'.base_url('agente/cliente_ordini/'.$row->idcliente).'" class="btn btn-default ">Ordini</a> ';
      if ($this->session->ruolo=='Agente')
        echo '<a href="'.base_url('agente/ordini_nuovo/'.$row->idcliente).'" class="btn btn-success ">Nuovo ordine</a> ';
      if ($row->codice=='') $disabled='disabled';
      else $disabled='';
      echo '<a href="'.base_url('scadenzario/index/'.$row->codice).'" class="btn btn-warning " '.$disabled.'>Scadenze</a> ';
      echo '<a href="'.base_url('agente/cliente_modifica/'.$row->idcliente).'" class="btn btn-info " title="Modifica"><i class="fa fa-edit"></i></a> ';
      if ($this->session->ruolo=='Admin')
        echo ' <a onclick="return confirm(\'Sicuro di eliminare questo cliente e i suoi ordini?\');" href="'.base_url('admin/cliente_elimina/'.$row->idcliente).'" class="btn btn-danger " title="Elimina"><i class="fa fa-trash"></i></a>';
    echo '</td>';
    echo '</tr>';
}
endif;
?>

                </tbody>

              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->

<link rel="stylesheet" href="<?php echo asset_url('plugins/iCheck/all.css') ?>">

<script src="<?php echo asset_url('plugins/iCheck/icheck.min.js') ?>"></script>
<script type="text/javascript">
  var table;
$(function () {
  table=$("#tabelladati").DataTable({
    "language": {
      "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
    },
    responsive: false,
    "scrollX": true,
    // "orderFixed": [[0, 'asc']],

    stateSave: false,
    "order": [[2, "asc"]],
    "columnDefs": [
        {
          "targets" : [0],
          "visible": false,
        },
    ],
  });
});
</script>

