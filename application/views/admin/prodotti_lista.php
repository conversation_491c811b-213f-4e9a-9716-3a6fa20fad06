

    <!-- Main content -->
    <section class="content no-padding-xs">
      <div id="processingIndicator">caricamento dati...</div>
      <div class="row">
        <div class="col-xs-12">
          <div class="box">
            <div class="overlay">
            <i class="fa fa-refresh fa-spin"></i>
            </div>
            <!-- /.box-header -->
            <div class="box-body">
              <table id="tabelladati" class="table table-bordered table-hover">
                <thead>
<?php
  if (count($data)>0) :
  $campi=array_keys(get_object_vars($data[0]));
  echo '<tr>';
  foreach ($campi as $campo) {
    echo '<th name="'.$campo.'">'.$campo.'</th>';
  }
  echo '</tr>';
?>
                </thead>
                <tbody>
<?php foreach ($data as $row) {
    echo '<tr>';
    foreach ($campi as $campo) {
      $dato=$row->{$campo};
      // if ($dato=="1") $dato='<i class=\'fa fa-fw fa-check-circle-o\'></i>';
      // elseif ($dato=="0") $dato='<i class=\'fa fa-fw fa-circle-o\'></i>';
      echo '<td>'.$dato.'</td>';
    }
    echo '</tr>';
}
endif;
?>

                </tbody>

              </table>

            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->

<script type="text/javascript">
  var table;
$(function () {
  table=$("#tabelladati").on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css( 'display', processing ? 'block' : 'none' );
    }).on( 'init.dt', function () {
        $(".overlay").remove();
        // console.log( 'Table initialisation complete: '+new Date().getTime() );
    }).DataTable({
    "language": {
      "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
    },
    responsive: true,
    // "orderFixed": [[0, 'asc']],

    stateSave: false,
    "order": [[0, "asc"]],
  });
});
</script>

