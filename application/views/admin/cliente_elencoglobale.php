

    <!-- Main content -->
    <section class="content no-padding-xs">
      <div class="row">
        <div class="col-xs-12">
          <div class="box">

            <!-- /.box-header -->
            <div class="box-body table-responsive">
              <table id="tabelladati" class="table table-bordered table-hover">
                <thead>
<?php
  if (count($data)>0) :
  // $campi=array_keys(get_object_vars($data[0]));
  if ($this->session->ruolo=='Admin') {
    $campi=array(
      'idcliente'=>'# cliente',
      'ragsoc'=>'ragione sociale',
      'indirizzo'=>'indirizzo',
      'citta'=>'città',
      'email'=>'email',
      'telefono'=>'telefono',
      'codice'=>'codice',
      'agente'=>'agente',
      );
  }
  else {
    $campi=array(
      'idcliente'=>'# cliente',
      'ragsoc'=>'ragione sociale',
      'indirizzo'=>'indirizzo',
      'citta'=>'città',
      'email'=>'email',
      'telefono'=>'telefono',
      'codice'=>'codice',
      );
  }
  echo '<tr>';
  foreach ($campi as $campo=>$label) {
    echo '<th name="'.$campo.'">'.$label.'</th>';
  }
  echo '</tr>';
?>
                </thead>
                <tbody>
<?php foreach ($data as $row) {
    echo '<tr>';
    foreach ($campi as $campo=>$label) {
      if (isset($row->{$campo})) $dato=$row->{$campo};
      else $dato='';
      echo '<td>'.$dato.'</td>';
    }
    echo '</tr>';
}
endif;
?>

                </tbody>

              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->

<link rel="stylesheet" href="<?php echo asset_url('plugins/iCheck/all.css') ?>">

<script src="<?php echo asset_url('plugins/iCheck/icheck.min.js') ?>"></script>
<script type="text/javascript">
  var table;
$(function () {
  table=$("#tabelladati").DataTable({
    "language": {
      "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
    },
    responsive: false,
    "scrollX": true,
    // "orderFixed": [[0, 'asc']],

    stateSave: false,
    "order": [[1, "asc"]],
    "columnDefs": [
        {
          "targets" : [0],
          "visible": false,
        },
    ],
  });
});
</script>

