<!-- Main content -->
<section class="content no-padding-xs">
    <div class="row">
        <div class="col-xs-12">
            <div class="box">

                <!-- /.box-header -->
                <div class="box-body">
                    <table id="tabelladati" class="table table-bordered table-hover">
                        <thead>
                            <?php
                            $campi = array(
                                'idcliente' => '# cliente',
                                'ragsoc' => 'ragione sociale',
                                'piva' => 'piva',
                                'cf' => 'cf',
                                'sdi' => 'sdi',
                                'pec' => 'pec',
                                'codice' => 'codice',
                                'agente' => 'agente',
                            );
                            echo '<tr>';
                            foreach ($campi as $campo => $label) {
                                echo '<th name="' . $campo . '">' . $label . '</th>';
                            }
                            echo '<th></th>';
                            echo '</tr>';
                            ?>
                        </thead>
                        <tbody>
                        </tbody>
                    </table>
                </div>
                <!-- /.box-body -->
            </div>
            <!-- /.box -->
        </div>
        <!-- /.col -->
    </div>
    <!-- /.row -->
</section>
<!-- /.content -->

<link rel="stylesheet" href="<?php echo asset_url('plugins/iCheck/all.css') ?>">

<script src="<?php echo asset_url('plugins/iCheck/icheck.min.js') ?>"></script>
<script type="text/javascript">
    var table;
    $(function() {
        table = $("#tabelladati").DataTable({
            "language": {
                "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
            },
            'processing': true,
            'serverSide': true,
            'serverMethod': 'post',
            "saveState": true,
            'ajax': {
                'url': '<?= base_url('agente/get_clienti_ajax') ?>'
            },
            'columns': [{
                    data: 'idcliente'
                },
                {
                    data: 'ragsoc'
                },
                {
                    data: 'piva'
                },
                {
                    data: 'cf'
                },
                {
                    data: 'sdi'
                },
                {
                    data: 'pec'
                },
                {
                    data: 'codice'
                },
                {
                    data: 'agente'
                },
                {
                    defaultContent: ''
                },
            ],
            responsive: false,
            "scrollX": true,
            // "orderFixed": [[0, 'asc']],

            stateSave: false,
            "order": [
                [2, "asc"]
            ],
            "columnDefs": [{
                    "targets": [0],
                    "visible": false,
                },
                {
                    "targets": 8,
                    "orderable": false,
                    "render": function(data, type, row) {
                        out = '<a href="<?= base_url('agente/cliente_ordini/') ?>' + row.idcliente + '" class="btn btn-primary ">Ordini</a>';
                        <?php if ($this->session->ruolo == 'Agente') { ?>
                            out += ' <a href="<?= base_url('agente/ordini_nuovo/') ?>' + row.idcliente + '" class="btn btn-success ">Nuovo</a> ';
                        <?php } ?>
                        if (row.codice == '') disabled = 'disabled';
                        else disabled = '';
                        out += ' <a href="<?= base_url('scadenzario/index/') ?>' + row.codice + '" class="btn btn-warning "' + disabled + '>Scadenzario</a>';
                        out += ' <a href="<?= base_url('agente/cliente_modifica/') ?>' + row.idcliente + '" class="btn btn-info" title="Modifica"><i class="fa fa-edit"></i></a>';
                        <?php if ($this->session->ruolo == 'Admin') { ?>
                            out += ' <a onclick="return confirm(\'Sicuro di eliminare questo cliente e i suoi ordini?\');" href="<?= base_url('admin/cliente_elimina/') ?>' + row.idcliente + '" class="btn btn-danger" title="Elimina"><i class="fa fa-trash"></i></a>';
                        <?php } ?>
                        return out;
                    }
                },
            ],
        });
    });
</script>