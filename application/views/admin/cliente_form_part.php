<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="Ragione sociale">Ragione sociale</span>
    <input type="text" class="form-control" id="ragsoc" name="ragsoc" maxlength="30" placeholder="Ragione sociale" value="<?php if (isset($cliente)) echo $cliente->ragsoc;
                                                                                                                          else echo $ragsoc; ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="Codice cliente">Codice cliente</span>
    <input type="text" class="form-control" id="codice" name="codice" maxlength="20" placeholder="Codice cliente" value="<?php if (isset($cliente)) echo $cliente->codice;
                                                                                                                          else echo $codice; ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="P.iva">P.iva</span>
    <input type="text" class="form-control" id="piva" name="piva" placeholder="P.iva" maxlength="20" value="<?php if (isset($cliente)) echo $cliente->piva ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="CF">CF</span>
    <input type="text" class="form-control" id="cf" name="cf" placeholder="Codice Fiscale" maxlength="20" value="<?php if (isset($cliente)) echo $cliente->cf ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon">email</span>
    <input type="email" class="form-control" id="email" name="email" placeholder="email" maxlength="100" value="<?php if (isset($cliente)) echo $cliente->email ?>">
  </div>
</div>
<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon">Telefono</span>
    <input type="telefono" class="form-control" id="telefono" name="telefono" placeholder="telefono" maxlength="50" value="<?php if (isset($cliente)) echo $cliente->telefono ?>">
  </div>
</div>
<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon">Pagamento</span>
    <textarea class="form-control" id="pagamento" name="pagamento" placeholder="pagamento" rows="4"><?php if (isset($cliente)) echo $cliente->pagamento ?></textarea>
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="Indirizzo">Indirizzo</span>
    <input type="text" class="form-control" id="indirizzo" name="indirizzo" placeholder="indirizzo" maxlength="30" value="<?php if (isset($cliente)) echo $cliente->indirizzo ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="cap">cap</span>
    <input type="text" class="form-control" id="cap" name="cap" placeholder="cap" maxlength="8" value="<?php if (isset($cliente)) echo $cliente->cap ?>">
  </div>

</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="citta">Città</span>
    <input type="text" class="form-control" id="citta" name="citta" placeholder="citta" maxlength="25" value="<?php if (isset($cliente)) echo $cliente->citta ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="provincia">Provincia</span>
    <input type="text" class="form-control" id="provincia" name="provincia" placeholder="provincia" maxlength="4" value="<?php if (isset($cliente)) echo $cliente->provincia ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="altradestinazione">Altra destinazione</span>
    <textarea class="form-control" id="altradestinazione" name="altradestinazione" placeholder="altradestinazione" rows="4"><?php if (isset($cliente)) echo $cliente->altradestinazione ?></textarea>
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="note">Note aggiuntive</span>
    <textarea class="form-control" id="note" name="note" placeholder="note" rows="4"><?php if (isset($cliente)) echo $cliente->note ?></textarea>
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <label for="esentesdipec">Esente fatturazione elettronica </label>
    <?php echo form_checkbox('esentesdipec', 1, (isset($cliente)) ? $cliente->esentesdipec : '', 'id="esentesdipec"'); ?>
  </div>

</div>
<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="Codice SDI">Codice SDI</span>
    <input type="text" class="form-control" id="sdi" name="sdi" placeholder="Codice SDI" value="<?php if (isset($cliente)) echo $cliente->sdi ?>">
  </div>

</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="pec">pec</span>
    <input type="email" class="form-control" id="pec" name="pec" placeholder="pec" value="<?php if (isset($cliente)) echo $cliente->pec ?>">
  </div>
</div>

<div class="form-group has-feedback">
  <div class="input-group">
    <span class="formspanlabel input-group-addon" title="pec">Tipo di documento</span>
    <input type="text" class="form-control" id="tipodocumento" name="tipodocumento" placeholder="Tipo di documento" value="<?php if (isset($cliente)) echo $cliente->tipodocumento ?>">
  </div>
</div>

<?php if ($this->session->ruolo == 'Admin') : ?>
  <div class="form-group has-feedback">
    <div class="input-group">
      <span class="formspanlabel input-group-addon" title="agente assegnato">Agente assegnato</span>
      <?php echo form_dropdown('idagente', $agenti, (isset($cliente)) ? $cliente->idagente : '', 'class="form-control"'); ?>
    </div>

  </div>
<?php endif; ?>

<input type="hidden" name="idcliente" value="<?php if (isset($cliente)) echo $cliente->idcliente ?>">


<script type="text/javascript">
  $(function() {
    // jQuery.validator.setDefaults({
    //   debug: true,
    //   success: "valid"
    // });

    $('#formregistrazione').validate({
      errorClass: "text-danger",
      // errorElement: "div",
      highlight: function(element, errorClass) {
        $(element).fadeOut(function() {
          $(element).fadeIn();
        })
      },
      rules: {
        ragsoc: {
          required: "#codice:blank" // = required se #codice è vuoto
        },
        piva: {
          required: function(el) {
            if ($("#codice").is(':filled')) return false;
            if ($("#cf").is(':filled')) return false;
            return true;
          },
          digits: true,
        },
        cf: {
          required: function(el) {
            if ($("#codice").is(':filled')) return false;
            if ($("#piva").is(':filled')) return false;
            return true;
          },
          // pattern: "\w{6}\d{2}\w\d{2}\w\d{3}\w",
        },
        indirizzo: {
          required: "#codice:blank"
        },
        cap: {
          required: "#codice:blank"
        },
        citta: {
          required: "#codice:blank"
        },
        cap: {
          required: "#codice:blank"
        },
        provincia: {
          required: "#codice:blank"
        },
        sdi: {
          required: function(el) {
            if ($("#codice").is(':filled')) return false;
            if ($("#esentesdipec").is(':checked')) return false;
            if ($("#pec").val() != '') return false;
            return true
          }
        },
        pec: {
          required: function(el) {
            if ($("#codice").is(':filled')) return false;
            if ($("#esentesdipec").is(':checked')) return false;
            if ($("#sdi").val() != '') return false;
            return true
          }
        }
      },
      messages: {
        piva: {
          required: "Inserisci piva o il cf",
          digits: "Inserisci solo numeri",
        },
        cf: {
          required: "Inserisci piva o il cf",
          // pattern: "Formato non valido",
        },
      },
    });

    $('#esentesdipec').click(function() {
      if ($(this).is(':checked')) {
        $('#sdi').parent().hide();
        $('#pec').parent().hide();
      } else {
        $('#sdi').parent().show();
        $('#pec').parent().show();
      }
    })
  })
</script>