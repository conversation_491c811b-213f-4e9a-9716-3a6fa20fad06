

    <!-- Main content -->
    <section class="content no-padding-xs">
      <div class="row">
        <div class="col-xs-12">
          <div class="box">

            <!-- /.box-header -->
            <div class="box-body">
              <table id="tabelladati" class="table table-bordered table-hover">
                <thead>
<?php
  $campi=array_keys(get_object_vars($data[0]));
  echo '<tr>';
  foreach ($campi as $campo) {
    echo '<th name="'.$campo.'">'.$campo.'</th>';
  }
  echo '<td></td>';
  echo '</tr>';
?>
                </thead>
                <tbody>
<?php foreach ($data as $row) {
    echo '<tr>';
    foreach ($campi as $campo) {
      $dato=$row->{$campo};
      // if ($dato=="1") $dato='<i class=\'fa fa-fw fa-check-circle-o\'></i>';
      // elseif ($dato=="0") $dato='<i class=\'fa fa-fw fa-circle-o\'></i>';
      echo '<td>'.$dato.'</td>';
    }
    if ($row->ruolo=='Agente') {
      echo '<td>
        <a href="'.base_url('admin/ordini_lista/'.$row->idutente).'" class="btn btn-info btn-xs">Ordini</a>
        <a href="'.base_url('admin/agente_modifica/'.$row->idutente).'" class="btn btn-warning btn-xs">Profilo</a>
        <a onclick="return confirm(\'Sicuro di eliminare questo agente?\');" href="'.base_url('admin/agente_elimina/'.$row->idutente).'" class="btn btn-danger btn-xs">Elimina</a></td>';
    }
    else {
      echo '<td><a href="'.base_url('admin/agente_modifica/'.$row->idutente).'" class="btn btn-warning btn-xs">Profilo</a></td>';
    }
    echo '</tr>';
} ?>

                </tbody>

              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->

<link rel="stylesheet" href="<?php echo asset_url('plugins/iCheck/all.css') ?>">

<script src="<?php echo asset_url('plugins/iCheck/icheck.min.js') ?>"></script>
<script type="text/javascript">
  var table;
$(function () {
  table=$("#tabelladati").DataTable({
    "language": {
      "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
    },
    responsive: true,
    // "orderFixed": [[0, 'asc']],

    stateSave: false,
    "order": [[1, "asc"]],
    "columnDefs": [
        {
          "targets" : [0],
          "visible": false,
        },
        // {
        //   "targets" : [1],
        //   "render": function (data, type, row) {
        //      //console.log(row);
        //      return '<a href="<?php echo base_url() ?>admin/agente_modifica/'+row[0]+'">'+data+'</a>';
        //     }
        // },
        {
          "targets" : [7],
          "render" : $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY HH:mm')
        },
        {
          "targets" : [8],
          "orderable" : false,
        },
    ],
  });
});
</script>

