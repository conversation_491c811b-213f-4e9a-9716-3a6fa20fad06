<div class="row">
  <div class="col-md-2"></div>
  <div class="col-md-8">
    <div class="panel panel-primary">
      <div class="panel-heading">
        Nuovo agente
      </div>
      <div class="panel-body">
        <form action="<?php echo base_url('registrazione/registrazione'); ?>" method="post" data-toggle="validator" id="formregistrazione">
          
          <div class="form-group has-feedback">
            <div class="input-group">
              <span class="input-group-addon" title="Nome"><i class="fa fa-fw fa-info "></i></span>
              <input type="text" class="form-control" id="nome" name="nome" placeholder="Nome" value="" required>
            </div>
            <div class="help-block with-errors"></div>
          </div>
          <div class="form-group has-feedback">
            <div class="input-group">
              <span class="input-group-addon" title="Cognome"><i class="fa fa-fw fa-info "></i></span>
              <input type="text" class="form-control" id="cognome" name="cognome" placeholder="Cognome" value="" required>
            </div>
            <div class="help-block with-errors"></div>
          </div>
          <div class="form-group has-feedback">
            <div class="input-group">
              <span class="input-group-addon" title="email"><i class="fa fa-fw fa-envelope "></i></span>
              <input type="email" class="form-control" name="email" placeholder="Email" required data-remote="<?php echo base_url('registrazione/checkemail') ?>">
            </div>
            <div class="help-block with-errors"></div>
          </div>

          <div class="form-group has-feedback">
            <div class="input-group">
              <span class="input-group-addon" title="telefono"><i class="fa fa-fw fa-phone "></i></span>
              <input type="telefono" class="form-control" name="telefono" placeholder="Telefono">
            </div>
            <div class="help-block with-errors"></div>
          </div>

          <div class="form-group has-feedback">
            <div class="input-group">
              <span class="input-group-addon" title="Nome utente"><i class="fa fa-fw fa-user "></i></span>
              <input type="text" class="form-control" id="username" name="username" placeholder="Nome utente" value="" required data-remote="<?php echo base_url('registrazione/checkusername') ?>">
            </div>
            <div class="help-block with-errors"></div>
          </div>
          <div class="form-group has-feedback">
            <div class="input-group">
              <span class="input-group-addon" title="Password"><i class="fa fa-fw fa-lock"></i></span>
              <input type="password" name="password" class="form-control" id="password" placeholder="Password" required>
            </div>
            <div class="help-block with-errors"></div>
          </div>
          <div class="form-group has-feedback">
            <div class="input-group">
              <span class="input-group-addon" title="Ripeti password"><i class="fa fa-fw fa-lock"></i></span>
              <input type="password" class="form-control" name="password_confirm" placeholder="Ripeti password" required data-match="#password" data-error="Le password non coincidono">
            </div>
            <div class="help-block with-errors"></div>
          </div>
          <button type="submit" class="btn btn-primary btn-block">Aggiungi</button>
        </form>
      </div>
    </div>
  </div>
  <div class="col-md-2"></div>
</div>
<script type="text/javascript">
  $(function() {
    $('#formregistrazione').find("input[type=text], textarea").val("").text('');
  })
  _beforeSubmit = function(e) {
      console.log('submit button clicked.');
      // do other things before captcha validation
      // e represents reference to original form submit event
      // return true if you want to continue triggering captcha validation, otherwise return false
      return false;
  }
</script>