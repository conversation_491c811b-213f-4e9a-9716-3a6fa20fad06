<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

<div class="container">
    <div class="page-header">
        <h2>
            <i class="fa fa-bell-o text-primary"></i>
            <?php echo isset($notifica) ? 'Modifica Notifica' : 'Nuova Notifica Programmata' ?>
        </h2>
        <p class="text-muted">Compila i campi per schedulare una nuova notifica</p>
    </div>

    <div class="panel panel-primary">
        <div class="panel-heading">
            <h3 class="panel-title">Dettagli Notifica</h3>
        </div>
        
        <div class="panel-body">
            <form method="post" action="<?php echo isset($notifica) ? site_url('notifiche/modifica/'.$notifica->id) : site_url('notifiche/create'); ?>">
                <input type="hidden" name="id_scadenzario" value="<?php echo $id_scadenzario; ?>">
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="titolo">
                                Titolo Notifica <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control input-lg" id="titolo" name="titolo" 
                                value="<?php echo isset($notifica) ? $notifica->titolo : '' ?>" 
                                placeholder="Inserisci un titolo chiaro" required>
                            <small class="help-block">Max 120 caratteri</small>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="control-label" for="data_programmata">
                                Data/Ora Programma <span class="text-danger">*</span>
                            </label>
                            <div class="input-group date" id="datetimepicker">
                                <input type="datetime-local" class="form-control input-lg" id="data_programmata" name="data_programmata" 
                                    value="<?php echo isset($notifica) ? date('Y-m-d\TH:i', strtotime($notifica->data_programmata)) : '' ?>" 
                                    required>
                                <span class="input-group-addon">
                                    <span class="fa fa-calendar"></span>
                                </span>
                            </div>
                            <small class="text-muted">Formato: GG/MM/AAAA HH:MM</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label class="control-label" for="testo">
                        Testo della Notifica <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control" id="testo" name="testo" rows="4" 
                        placeholder="Inserisci il messaggio della notifica..." 
                        style="resize: vertical;" required><?php 
                        echo isset($notifica) ? $notifica->testo : '' ?></textarea>
                    <small class="help-block">Supporta HTML base per formattazione</small>
                </div>

                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h3 class="panel-title">Seleziona Destinatari</h3>
                        <small class="text-muted">Spunta le persone che riceveranno la notifica</small>
                    </div>
                    <div class="panel-body">
                        <?php
                        $selected = isset($notifica) ? explode(',', $notifica->destinatari) : [];
                        ?>
                        
                        <?php if(isset($agente) && $agente && $agente->idutente): ?>
                        <div class="checkbox checkbox-primary">
                            <input type="checkbox" id="agente" name="destinatari[]" 
                                value="<?php echo $agente->idutente ?>"
                                <?php echo in_array($agente->idutente, $selected) ? 'checked' : '' ?>>
                            <label for="agente">
                                <span class="fa fa-user-circle-o"></span>
                                <strong>Agente:</strong> <?php echo $agente->nome.' '.$agente->cognome ?>
                                <small class="text-muted">(Responsabile scadenzario)</small>
                            </label>
                        </div>
                        <?php endif; ?>

                        <?php if(isset($admin_users)): ?>
                        <hr>
                        <h5 class="text-uppercase text-muted">Amministratori:</h5>
                        <?php foreach($admin_users as $admin): ?>
                        <div class="checkbox checkbox-success">
                            <input type="checkbox" id="admin_<?php echo $admin->idutente ?>" 
                                name="destinatari[]" value="<?php echo $admin->idutente ?>"
                                <?php echo in_array($admin->idutente, $selected) ? 'checked' : '' ?>>
                            <label for="admin_<?php echo $admin->idutente ?>" class="text-muted">
                                <span class="fa fa-shield"></span>
                                <?php echo $admin->nome.' '.$admin->cognome ?>
                            </label>
                        </div>
                        <?php endforeach; endif; ?>
                    </div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-lg btn-primary pull-right">
                        <i class="fa fa-check-circle"></i> 
                        <?php echo isset($notifica) ? 'Aggiorna Notifica' : 'Programma Notifica' ?>
                    </button>
                    <a href="<?php echo site_url('scadenzario/modifica/' . $id_scadenzario); ?>" 
                       class="btn btn-link">
                        <i class="fa fa-times"></i> Annulla
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
.checkbox label {
    padding-left: 25px;
    margin-bottom: 10px;
    transition: all 0.2s ease;
}
.checkbox input[type="checkbox"] {
    margin-left: -25px;
    position: relative;
    top: 2px;
}
.checkbox-primary input[type="checkbox"]:checked + label {
    color: #337ab7;
}
</style>
