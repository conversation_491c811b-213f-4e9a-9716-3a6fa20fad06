<div class="container">
    <h2>Lista Notifiche</h2>
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th><PERSON><PERSON></th>
                <th>Data Programmata</th>
                <th>Data Invio</th>
                <th>Stato</th>
                <th>Azioni</th>
            </tr>
        </thead>
        <tbody>
            <?php if (isset($notifiche) && !empty($notifiche)): ?>
                <?php foreach ($notifiche as $notifica): ?>
                    <tr>
                        <td><?php echo $notifica->titolo; ?></td>
                        <td><?php echo date("d/m/Y H:i", strtotime($notifica->data_programmata)); ?></td>
                        <td><?php echo $notifica->data_invio ? date("d/m/Y H:i", strtotime($notifica->data_invio)) : '-'; ?></td>
                        <td>
                            <span class="label label-<?php 
                                echo $notifica->stato == 'inviata' ? 'success' : 
                                     ($notifica->stato == 'programmata' ? 'warning' : 'default'); 
                            ?>">
                                <?php echo ucfirst($notifica->stato); ?>
                            </span>
                            
                            <?php if($notifica->stato == 'programmata' && strtotime($notifica->data_programmata) < time()): ?>
                                <i class="fa fa-exclamation-triangle text-danger" title="In ritardo"></i>
                            <?php endif; ?>
                        </td>
                        <td>
                            <a href="<?php echo base_url('notifiche/modifica/' . $notifica->id); ?>" class="btn btn-xs btn-primary">
                                <i class="fa fa-pencil"></i> Modifica
                            </a>
                            <a href="<?php echo base_url('notifiche/elimina/' . $notifica->id); ?>" class="btn btn-xs btn-danger" onclick="return confirm('Sei sicuro di voler eliminare questa notifica?');">
                                <i class="fa fa-trash"></i> Elimina
                            </a>
                        </td>
                    </tr>
                <?php endforeach; ?>
            <?php else: ?>
                <tr>
                    <td colspan="7">Nessuna notifica da visualizzare</td>
                </tr>
            <?php endif; ?>
        </tbody>
    </table>
</div>
