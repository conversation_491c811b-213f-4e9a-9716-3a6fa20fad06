<section class="content no-padding-xs">
  <div class="box box-warning">
    <div class="box-header with-border">
      <h3 class="box-title">Cliente: <?php echo $cliente->ragsoc ?></h3>

      <div class="box-tools pull-right">
        <span id="cart">
          <span data-toggle="tooltip" title="0 Prodotti" data-selector="true" class="badge bg-yellow" id="numprodotti">0</span>
          <span class="glyphicon glyphicon-shopping-cart" style="color: orange"></span></span>
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
        </button>
      </div>
    </div>
    <div class="box-body table-responsive">
      <form action="<?php echo base_url('agente/ordini_salvamodifica') ?>" method="post" id="formordine">
        <table class="table" style="min-width: 1200px !important;">
          <thead>
            <tr>
              <th>Tipo riga</th>
              <th>Codice</th>
              <th>Descrizione</th>
              <th>Prezzo listino</th>
              <th>Prezzo ordine</th>
              <th>Sconto</th>
              <th>Prezzo finale</th>
              <th>Quantità</th>
              <th>Note</th>
              <th></th>
            </tr>
          </thead>
          <tbody id="ordinetemp">
            <?php
            foreach ($prodotti as $i => $p) {
              $readonly = 'readonly';
              if ($p->codice == '00 001') $readonly = ''; // prodotto custom
              $sospeso_bg = '';
              $note = $p->note;
              // 23/6/2022 Francesco dice che non serve
              // if (!is_null($p->datasospeso)) { // prodotto sospeso
              //   $sospeso_bg = ' style="background-color: #fee" ';
              //   $note = "SOSPESO";
              // }
              echo '<tr' . $sospeso_bg . ' id="' . $i . '">';
              echo '<td><select name="tiporiga[]" class="form-control">';
              foreach ($this->tiporiga as $key => $value) {
                if ($p->tiporiga == $key) $selected = 'selected';
                else $selected = '';

                echo '<option value="' . $key . '" ' . $selected . '>' . $value . '</option>';
              }
              echo '</select></td>';
              echo '<td><input type="text" name="codice[]" value="' . $p->codice . '" class="form-control inputcodice" readonly></td>';
              echo '<td><input type="text" name="descrizione[]" value="' . htmlentities($p->descrizione) . '" size="40" maxlength="40" class="form-control" ' . $readonly . '></td>';
              echo '<td>' . $p->prezzolistino . '</td>';
              echo '<td><input type="number" step="0.01" prezzolistino="' . $p->prezzolistino . '" ultimoprezzo="' . $p->prezzoordine . '" name="prezzoordine[]" value="' . $p->prezzoordine . '" class="form-control inputprezzoordine" required><input type="hidden" name="prezzolistino[]" value="' . $p->prezzolistino . '"></td>';
              echo '<td><input type="text" ultimosconto="' . $p->sconto . '" name="sconto[]" value="' . $p->sconto . '" class="form-control inputsconto"></td><td><input type="text" name="prezzo[]" value="' . $p->prezzo . '" class="form-control inputprezzo" readonly></td>';
              echo '<td nowrap><input type="number" step="1" name="quantita[]" size="4" value="' . $p->quantita . '" style="display:inline-block" class="form-control" placeholder="' . $p->um . '" required min="1"><small>' . $p->um . '</small></td>';
              echo '<td><input type="text" name="note[]" value="' . $note . '" class="form-control"></td>';
              echo '<td><a href="#" class="btn btn-danger" id="rimuoviprodotto' . $i . '">-</a></td>';
              echo '<input type="hidden" name="um[]" value="' . $p->um . '">';
              echo "</tr>";
            }
            ?>
          </tbody>
          <tfoot>
            <tr>
              <td colspan="7">
                <input type="submit" id="salvaordine" name="salvaordine" class="btn btn-success pull-right" value="Salva ordine">
                <a href="#" class="btn btn-primary" id="addcustprod">Aggiungi prodotto personalizzato</a>
              </td>
            </tr>
          </tfoot>
        </table>
        <?php
        echo form_hidden('idcliente', $cliente->idcliente);
        echo form_hidden('idordine', $ordine->idordine);
        echo form_label('Note aggiuntive per questo ordine:');
        echo form_textarea('noteagente', $ordine->noteagente, 'class="form-control" style="height:100px"');
        ?>
      </form>
    </div>
  </div>


  <div class="row">
    <div class="col-xs-12">
      <?php $this->load->view('agente/listino_part_ajax'); ?>
    </div>
    <!-- /.col -->
  </div>
  <!-- /.row -->
</section>
<!-- /.content -->