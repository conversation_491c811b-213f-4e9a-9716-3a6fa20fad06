<div class="box">
  <div class="overlay">
  <i class="fa fa-refresh fa-spin"></i>
  </div>
  <!-- /.box-header -->
  <div class="box-body">
    <p>Listino</p> <br>
    <table id="tabellaprodotti" class="table table-bordered table-hover">
      <thead>
    <?php
      if (count($listino)>0) :
      $campi=array_keys(get_object_vars($listino[0]));
      echo '<tr>';
      foreach ($campi as $campo) {
        echo '<th class="'.$campo.'">'.$campo.'</th>';
      }
      echo '<th data-name="bottoni"></th>';
      echo '</tr>';
    ?>
      </thead>
      <tbody>
    <?php foreach ($listino as $row) {
        echo '<tr>';
        foreach ($campi as $campo) {
          $dato=$row->{$campo};

          if ($campo=='um') {
            if ($row->ultimoprezzo!='') $ordinato='data-search="ordinatocliente"';
            else $ordinato='data-search=""';
          }
          elseif ($campo=='codice') {
            $ordinato='data-search="'.str_replace(' ', '', $dato).'"';
          }
          else $ordinato='';
          echo '<td '.$ordinato.' class="'.$campo.'">'.$dato.'</td>';
        }
        echo '<td><a href="#" title="Aggiungi" class="btn btn-primary aggiungi" onClick="aggiungi(this)"> + </a></td>';
        echo '</tr>';
    }
    endif;
    ?>
      </tbody>
    </table>
  </div>
  <!-- /.box-body -->
</div>
<!-- /.box -->

<script type="text/javascript">
  var table;
  var numprodotti=$('#ordinetemp').children('tr').length;
  var idbtndel=<?php echo $i ?>;
$(function () {
  // $(".aggiungi").click(function(){
  //   var parenttr = $(this).parent().parent();
  //   console.log('parenttr: ' + parenttr);
  //   var codice = parenttr.find('.codice').text();
  //   var descrizione = parenttr.find('.descrizione').text();
  //   var prezzo = parenttr.find('.prezzo').text();
  //   var um = parenttr.find('.um').text();
  //   numprodotti++;
  //   idbtndel++;

  //   $('#ordinetemp').append('<tr><td><input type="text" name="codice[]" value="'+codice+'" class="form-control" readonly></td><td><input type="text" name="descrizione[]" value="'+descrizione+'" size="50" class="form-control" readonly></td><td>'+prezzo+'</td><td><input type="text" name="prezzo[]" value="'+prezzo+'" class="form-control" required></td><td nowrap><input type="text" name="quantita[]" size="4" class="form-control" style="display:inline-block" placeholder="'+um+'" required><small>'+um+'</small></td><td><input type="text" name="note[]" class="form-control"></td><td><a href="#" class="btn btn-xs btn-danger" id="rimuoviprodotto'+idbtndel+'">Rimuovi</a></td></tr><input type="hidden" name="um[]" value="'+um+'">');
  //   setrimuoviprodotto(idbtndel);
  //   updatenumprod();
  // })

  $('#addcustprod').click(function(){
    numprodotti++;
    idbtndel++;
    $('#ordinetemp').append('<tr><td><input type="text" name="codice[]" value="" class="form-control inputcodice" id="codice'+idbtndel+'"></td><td><input type="text" name="descrizione[]" value="" size="50" class="form-control" required></td><td></td><td><input type="text" name="prezzo[]" value="" class="form-control inputprezzo" required><input type="hidden" name="prezzolistino[]" value=""></td><td><input type="text" name="sconto[]" value="" class="form-control inputsconto" disabled></td><td><input type="text" name="quantita[]" size="4" class="form-control" required></td><td><input type="text" name="note[]" class="form-control"></td><td><a href="#" class="btn btn-danger" id="rimuoviprodotto'+idbtndel+'">-</a></td></tr>');
    setrimuoviprodotto(idbtndel);
    updatenumprod();
    $('#codice'+idbtndel).focus();
  })

  attiva();
  sconti();
  updatenumprod();
  enablesalva();

  table=$("#tabellaprodotti").on( 'processing.dt', function ( e, settings, processing ) {
        $('#processingIndicator').css( 'display', processing ? 'block' : 'none' );
    }).on( 'init.dt', function () {
        $(".overlay").remove();
        // console.log( 'Table initialisation complete: '+new Date().getTime() );
    }).DataTable({
    dom: 'Bfrtip',
    "orderClasses": false,
    "deferRender": true,
    buttons: [
      {
          text: "Filtra: ordinati dal cliente",
          action: function(e, dt, node, config){
              dt.column('.um').search("ordinatocliente").draw();
          }
      },
      {
          text: "Filtra: tutti prodotti",
          action: function(e, dt, node, config){
              dt.column('.um').search("").draw();
          }
      },
    ],
    // "columns": [
    // {
    //   "className":      'details-control',
    //   "orderable":      false,
    //   "data":           null,
    //   "defaultContent": ''
    // },
    // {  "data": "codice" },
    // {  "data": "descrizione" },
    // {  "data": "prezzo" },
    // {  "data": "um" },
    // {  "data": "dataordine" },
    // {  "data": "ultimoprezzo" },
    // // {  "data": "ultimaqta" },
    // {  "data": "bottoni" },
    // ],
    "columnDefs": [
      // {
      //   "targets": "details",
      //   "className":      'details-control',
      //   "orderable":      false,
      //   "data":           null,
      //   "defaultContent": ''
      // },
      {
        "targets": "dataordine",
        "render" : $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY'),
        "visible": true
      },
      {
        "targets": "bottoni",
        "responsivePriority": 1,
        "visible": true
      },
    ],
    "language": {
      "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
    },
    responsive: true,
    cache: true,
    // "orderFixed": [[0, 'asc']],

    stateSave: false,
    "order": [[0, "asc"]],

  });
  $("input[name='sconto[]']").each(function(){
    var prezzolistino=$(this).parent().parent().find("input[name='prezzolistino[]']").val();
    if (prezzolistino=='') $(this).attr('disabled', '');
  });
});

function aggiungi(el){
    var parenttr = $(el).closest('tr[role="row"]');
    if (typeof $(parenttr).html()=='undefined') {
      parenttr = $(el).closest('tr').prev();
    }
    var codice = parenttr.find('.codice').text();
    var descrizione = parenttr.find('.descrizione').text();
    var prezzolistino = parenttr.find('.prezzo').text();
    var ultimoprezzo = parenttr.find('.ultimoprezzo').text();
    if (ultimoprezzo>0) prezzo=ultimoprezzo;
    else prezzo=ultimoprezzo;
    var um = parenttr.find('.um').text();
    numprodotti++;
    idbtndel++;

    $('#ordinetemp').append('<tr><td><input type="text" name="codice[]" value="'+codice+'" class="form-control inputcodice" readonly></td><td><input type="text" name="descrizione[]" value="'+descrizione+'" size="50" class="form-control" readonly></td><td>'+prezzolistino+'</td><td><input type="text" name="prezzo[]" value="'+prezzo+'" class="form-control inputprezzo" required><input type="hidden" name="prezzolistino[]" value="'+prezzolistino+'"></td><td><input type="text" name="sconto[]" value="" class="form-control inputsconto"></td><td nowrap><input type="text" name="quantita[]" size="4" class="form-control" style="display:inline-block" placeholder="'+um+'" required><small>'+um+'</small></td><td><input type="text" name="note[]" class="form-control"></td><td><a href="#" class="btn btn-danger" id="rimuoviprodotto'+idbtndel+'">-</a></td></tr><input type="hidden" name="um[]" value="'+um+'">');
    setrimuoviprodotto(idbtndel);
    updatenumprod();
    sconti();
  }

function setrimuoviprodotto (n) {
  $("#rimuoviprodotto"+n).click(function(){
    $(this).parent().parent().remove();
    numprodotti--;
    updatenumprod();
  });
}
function updatenumprod() {
  $('#numprodotti').text(numprodotti).attr('title', numprodotti+' prodotti');
  $('#cart').effect("shake", {
    times: 1
    }, 300);
  enablesalva();
}
function enablesalva() {
  if (numprodotti==0) {
    $('#salvaordine').attr('disabled','');
  }
  else {
    $('#salvaordine').removeAttr('disabled');
  }
}
function attiva() {
  $("a[id^='rimuoviprodotto']").click(function(){
    $(this).parent().parent().remove();
    numprodotti--;
    updatenumprod();
  })
}
function sconti() {
  Number.prototype.round = function(decimals) {
      return Number((Math.round(this + "e" + decimals)  + "e-" + decimals));
  }
  $("input[name='prezzo[]']").change(function(){
    var sconto=0;
    var prezzolistino=$(this).parent().find("input[name='prezzolistino[]']").val();
    var prezzo=$(this).val();
    if (prezzolistino!='' && prezzo<prezzolistino) sconto=100-(prezzo/prezzolistino*100);
    // console.log(prezzo + ' ' + prezzolistino);
    if (prezzolistino!='' && prezzo!='') {
      $(this).parent().parent().find("input[name='sconto[]']").val(''); // sconto.round(2)
      $(this).parent().parent().find("input[name='sconto[]']").attr('placeholder', sconto.round(2));
    }
  })
  $("input[name='sconto[]']").change(function(){
    var sconto=$(this).val();
    var prezzolistino=$(this).parent().parent().find("input[name='prezzolistino[]']").val();
    var prezzo=$(this).val();
    if (prezzolistino!='' && sconto!='') {
      prezzo=prezzolistino-(prezzolistino*sconto/100)
      $(this).parent().parent().find("input[name='prezzo[]']").val(prezzo.round(2).toFixed(2));
    }
    // console.log(sconto + ' ' + prezzo + ' ' + prezzolistino);
  })

}
</script>

