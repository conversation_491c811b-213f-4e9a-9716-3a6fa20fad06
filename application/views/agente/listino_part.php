<div class="box">
  <div class="overlay">
    <i class="fa fa-refresh fa-spin"></i>
  </div>
  <!-- /.box-header -->
  <div class="box-body table-responsive">
    <p>Listino</p> <br>
    <table id="tabellaprodotti" class="table table-bordered table-hover">
      <thead>
        <?php
        if (count($listino) > 0) :
          $campi = array_keys(get_object_vars($listino[0]));
          echo '<tr>';
          foreach ($campi as $campo) {
            switch ($campo) {
              case 'codice':
              case 'descrizione':
              case 'prezzo':
              case 'dataordine':
                $rp = 20;
                break;

              default:
                $rp = 10000;
                break;
            }
            echo '<th class="' . $campo . '" data-priority="' . $rp . '">' . $campo . '</th>';
          }
          echo '<th data-name="bottoni" data-priority="10"></th>';
          echo '</tr>';
        ?>
      </thead>
      <tbody>
      <?php foreach ($listino as $row) {
            echo '<tr>';
            foreach ($campi as $campo) {
              $dato = $row->{$campo};

              if ($campo == 'um') {
                if ($row->ultimoprezzo != '') $ordinato = 'data-search="ordinatocliente"';
                else $ordinato = 'data-search=""';
              } elseif ($campo == 'codice') {
                $ordinato = 'data-search="' . str_replace(' ', '', $dato) . '"';
              } else $ordinato = '';
              echo '<td ' . $ordinato . ' class="' . $campo . '">' . $dato . '</td>';
            }
            echo '<td><a href="#" title="Aggiungi" class="btn btn-primary aggiungi" onClick="aggiungi(this)"> + </a></td>';
            echo '</tr>';
          }
        endif;
      ?>
      </tbody>
    </table>
  </div>
  <!-- /.box-body -->
</div>
<!-- /.box -->

<script type="text/javascript">
  var table;
  var numprodotti = $('#ordinetemp').children('tr').length;
  var idbtndel = <?php echo $i ?>;
  $(function() {
    $('#addcustprod').click(function() {
      numprodotti++;
      idbtndel++;
      $('#ordinetemp').append('<tr id="' + idbtndel + '"><td><input type="text" name="codice[]" value="00 001" class="form-control inputcodice" id="codice' + idbtndel + '" readonly></td><td><input type="text" name="descrizione[]" id="descrizione' + idbtndel + '" value="" size="40" maxlength="40" class="form-control" required></td><td></td><td><input type="text" name="prezzoordine[]" value="" class="form-control inputprezzoordine" required><input type="hidden" name="prezzolistino[]" value=""></td><td><input type="text" name="sconto[]" value="" class="form-control inputsconto"></td><td><input type="text" name="prezzo[]" value="" class="form-control inputprezzo" readonly></td><td><input type="text" name="quantita[]" size="4" class="form-control" required></td><td><input type="text" name="note[]" class="form-control"></td><td><a href="#" class="btn btn-danger" id="rimuoviprodotto' + idbtndel + '">-</a></td></tr>');
      setrimuoviprodotto(idbtndel);
      updatenumprod();
      sconti();
      $('#descrizione' + idbtndel).focus();
    })

    attiva();
    sconti();
    updatenumprod();
    enablesalva();

    table = $("#tabellaprodotti").on('processing.dt', function(e, settings, processing) {
      $('#processingIndicator').css('display', processing ? 'block' : 'none');
    }).on('init.dt', function() {
      $(".overlay").remove();
      table.responsive.rebuild();
      table.responsive.recalc();
      table.columns.adjust().draw();
      // console.log( 'Table initialisation complete: '+new Date().getTime() );
    }).DataTable({
      dom: 'Bfrtip',
      "orderClasses": false,
      "deferRender": true,
      buttons: [{
          text: "Filtra: ordinati dal cliente",
          action: function(e, dt, node, config) {
            dt.column('.um').search("ordinatocliente").draw();
          }
        },
        {
          text: "Filtra: tutti prodotti",
          action: function(e, dt, node, config) {
            dt.column('.um').search("").draw();
          }
        },
      ],
      // "columns": [
      // {
      //   "className":      'details-control',
      //   "orderable":      false,
      //   "data":           null,
      //   "defaultContent": ''
      // },
      // {  "data": "codice" },
      // {  "data": "descrizione" },
      // {  "data": "prezzo" },
      // {  "data": "um" },
      // {  "data": "dataordine" },
      // {  "data": "ultimoprezzo" },
      // // {  "data": "ultimaqta" },
      // {  "data": "bottoni" },
      // ],
      "columnDefs": [
        // {
        //   "targets": "details",
        //   "className":      'details-control',
        //   "orderable":      false,
        //   "data":           null,
        //   "defaultContent": ''
        // },
        {
          "targets": "dataordine",
          "render": $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY'),
          "visible": true
        },
        {
          "targets": "bottoni",
          "visible": true
        },
        {
          "targets": "ultimosconto",
          "visible": true
        },
        {
          "targets": "tags",
          "visible": false
        },
        {
          "targets": "descrizione",
          "width": "20%"
        },
      ],
      "language": {
        "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
      },
      responsive: true,
      cache: true,
      // "orderFixed": [[0, 'asc']],

      stateSave: false,
      "order": [
        [0, "asc"]
      ],

    });
    $("input[name='sconto[]']").each(function() {
      calcolosconti(this);
    });
  });

  function aggiungi(el) {
    var parenttr = $(el).closest('tr');
    // if (typeof $(parenttr).html()=='undefined') {
    //   parenttr = $(el).closest('tr').prev();
    // }
    var codice = parenttr.find('.codice').text();
    var descrizione = parenttr.find('.descrizione').text();
    var prezzolistino = parenttr.find('.prezzo').text();
    var ultimoprezzo = parenttr.find('.ultimoprezzo').text();
    var ultimosconto = parenttr.find('.ultimosconto').text();
    if (ultimoprezzo > 0) prezzoordine = ultimoprezzo;
    else prezzoordine = prezzolistino;
    var prezzo = prezzoordine;
    var um = parenttr.find('.um').text();
    numprodotti++;
    idbtndel++;

    $('#ordinetemp').append('<tr id="' + idbtndel + '"><td><input type="text" name="codice[]" value="' + codice + '" class="form-control inputcodice" readonly></td><td><input type="text" name="descrizione[]" value="' + descrizione + '" size="50" class="form-control" readonly></td><td>' + prezzolistino + '</td><td><input type="text" name="prezzoordine[]" value="' + prezzoordine + '" class="form-control inputprezzoordine" required><input type="hidden" name="prezzolistino[]" value="' + prezzolistino + '"></td><td><input type="text" name="sconto[]" value="' + ultimosconto + '" class="form-control inputsconto"></td><td><input type="text" name="prezzo[]" value="' + prezzo + '" class="form-control inputprezzo" readonly></td><td nowrap><input type="text" name="quantita[]" size="4" class="form-control" style="display:inline-block" placeholder="' + um + '" required><small>' + um + '</small></td><td><input type="text" name="note[]" class="form-control"></td><td><a href="#" class="btn btn-danger" id="rimuoviprodotto' + idbtndel + '">-</a></td></tr><input type="hidden" name="um[]" value="' + um + '">');
    setrimuoviprodotto(idbtndel);
    updatenumprod();
    sconti();
    $('#' + idbtndel).find("input[name='prezzoordine[]']").focus();
    $('#' + idbtndel).find("input[name='sconto[]']").focus();
    $('#' + idbtndel).find("input[name='quantita[]']").focus();
  }

  function setrimuoviprodotto(n) {
    $("#rimuoviprodotto" + n).click(function() {
      $(this).parent().parent().remove();
      numprodotti--;
      updatenumprod();
    });
  }

  function updatenumprod() {
    $('#numprodotti').text(numprodotti).attr('title', numprodotti + ' prodotti');
    $('#cart').effect("shake", {
      times: 1
    }, 300);
    enablesalva();
  }

  function enablesalva() {
    if (numprodotti == 0) {
      $('#salvaordine').attr('disabled', '');
    } else {
      $('#salvaordine').removeAttr('disabled');
    }
  }

  function attiva() {
    $("a[id^='rimuoviprodotto']").click(function() {
      $(this).parent().parent().remove();
      numprodotti--;
      updatenumprod();
    })
  }

  function sconti() {
    Number.prototype.round = function(decimals) {
      return Number((Math.round(this + "e" + decimals) + "e-" + decimals));
    }
    $("input[name='prezzoordine[]'], input[name='sconto[]']").focusout(function() {
      calcolosconti(this);
    });


  }

  function calcolosconti (elem) {
    var currentrow = $(elem).closest('tr');

    var sconto = currentrow.find("input[name='sconto[]']").val();
    // console.log(sconto);
    var prezzolistino = currentrow.find("input[name='prezzolistino[]']").val();
    var prezzoordine = currentrow.find("input[name='prezzoordine[]']").val();
    if (sconto >= 100) {
      currentrow.find("input[name='prezzo[]']").val(0);
      currentrow.find("input[name='sconto[]']").val('');
      currentrow.find("input[name='prezzoordine[]']").val(0);
    } else {
      if (prezzoordine != '') {
        currentrow.find("input[name='prezzo[]']").val(prezzoordine);
      } else {
        currentrow.find("input[name='prezzo[]']").val(prezzolistino);
        currentrow.find("input[name='prezzoordine[]']").val(prezzolistino);
        currentrow.find("input[name='sconto[]']").val('');
      }
      if (prezzoordine != '' && sconto != '') {
        prezzo = prezzoordine - (prezzoordine * sconto / 100);
        if (prezzo > 0) currentrow.find("input[name='prezzo[]']").val(prezzo.round(2).toFixed(2));
        else currentrow.find("input[name='prezzo[]']").val(0);
      }
    }
  }
</script>