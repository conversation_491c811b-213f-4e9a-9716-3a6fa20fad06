    <!-- Main content -->
    <section class="content no-padding-xs">
      <?php if ($this->session->userdata["ruolo"]=='Admin'): ?>
      <a href="<?php echo base_url('admin/cataloghicarica/') ?>" class="btn btn-primary">Nuovo catalogo</a>
      <?php endif; ?>
      <div class="row">
        <div class="col-xs-12">
          <div class="box">

            <!-- /.box-header -->
            <div class="box-body">
              <table id="tabelladati" class="table table-bordered table-hover">
                <thead>
<?php
  if (count($data)>0) :
  $campi=array_keys(get_object_vars($data[0]));
  // var_dump($data);
  // $campi=array(
  //   'id' => '#',
  //   'titolo'=>'titolo',
  //   'descrizione'=>'descrizione',
  //   'nomefile'=>'file',
  //   'datains'=>'data',
  //   'pubblicato'=>'pubblicato',
  //   'dimensione'=>'dimensione',
  //   );
  echo '<tr>';
  foreach ($campi as $campo) {
    echo '<th name="'.$campo.'">'.$campo.'</th>';
  }
  echo '<td></td>';
  echo '</tr>';
?>
                </thead>
                <tbody>
<?php foreach ($data as $row) {
    $filename=CATALOGHIPATH.$row->nomefile;
      if (is_file($filename) || $this->session->ruolo=='Admin') {
      echo '<tr>';
      foreach ($campi as $campo) {
        $dato=$row->{$campo};
        echo '<td>'.$dato.'</td>';
      }
      echo '<td>';
      if (is_file($filename)) echo '<a href="'.base_url(CATALOGHIPATH.$row->nomefile).'" class="btn btn-primary " target="_blank">Visualizza</a> ';
      if ($this->session->ruolo=='Admin') {
        echo '<a onclick="return confirm(\'Sicuro di eliminare questo catalogo?\');" href="'.base_url('admin/cataloghi_elimina/'.$row->id).'" class="btn btn-default ">Elimina</a> ';
      }
      echo '</td>';
      echo '</tr>';
    }
}
endif;
?>

                </tbody>

              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
        </div>
        <!-- /.col -->
      </div>
      <?php if ($this->session->userdata["ruolo"]=='Admin') echo "Spazio utilizzato: ".$spazio.' (max 100Mb)'; ?>
    </section>
    <!-- /.content -->

<link rel="stylesheet" href="<?php echo asset_url('plugins/iCheck/all.css') ?>">

<script src="<?php echo asset_url('plugins/iCheck/icheck.min.js') ?>"></script>
<script type="text/javascript">
  var table;
$(function () {
  table=$("#tabelladati").DataTable({
    "language": {
      "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
    },
    responsive: true,
    // "orderFixed": [[0, 'asc']],

    // stateSave: false,
    // "order": [[2, "desc"]],
    "columnDefs": [
        {
          "targets": [4],
          "render" : $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY HH:mm')
        },
        {
        "targets"  : [5],
        // "orderable": false,
        "render": function (data, type, row) {
          // console.log(row[0]);
          <?php if ($this->session->ruolo=='Admin') : ?>
          if (data==0) return '<a href="<?php echo base_url('admin/cataloghi_pubblicato/') ?>'+row[0]+'/1"><i class="fa fa-fw fa-times-circle text-danger"></i></a><span style="display: none">0</span>';
          if (data==1) return '<a href="<?php echo base_url('admin/cataloghi_pubblicato/') ?>'+row[0]+'/0"><i class="fa fa-fw fa-check-circle text-success"></i></a><span style="display: none">1</span>';
          <?php else : ?>
          if (data==0) return '<i class="fa fa-fw fa-times-circle text-danger"></i><span style="display: none">0</span>';
          if (data==1) return '<i class="fa fa-fw fa-check-circle text-success"></i><span style="display: none">1</span>';
          <?php endif; ?>

          },
        <?php if ($this->session->ruolo=='Agente') : ?>
        "visible": false
        <?php endif; ?>
        },
        {
          "targets"  : [6],
          "render": function (data, type, row) {
            if (data<1024) return (data/1024).toFixed(2) + 'M';
            else return (data/1024).toFixed(1) + 'M';
          }
        },
        {
          "targets": [0],
          "visible": false
        },
      ]
  });
});
</script>

