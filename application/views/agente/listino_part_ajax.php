<div class="box">
  <div class="box-body table-responsive">
    <p>Listino</p> <br>
    <table id="tabellaprodotti" class="table table-bordered table-hover">
      <thead>
        <?php
        $campi = array(
          'codice' => 'codice',
          'descrizione' => 'descrizione',
          'prezzo' => 'prezzo',
          'um' => 'um',
          'dataordine' => 'dataordine',
          'ultimoprezzo' => 'ultimoprezzo',
          'ultimaqta' => 'ultimaqta',
          'ultimosconto' => 'ultimosconto',
          'datasospeso' => 'datasospeso',
          'note' => 'note'
        );
        echo '<tr>';
        foreach ($campi as $campo) {
          switch ($campo) {
            case 'codice':
            case 'descrizione':
              $rp = 15;
              break;
            case 'prezzo':
            case 'dataordine':
              $rp = 20;
              break;
            default:
              $rp = 10000;
              break;
          }
          echo '<th class="' . $campo . '" data-priority="' . $rp . '">' . $campo . '</th>';
        }
        echo '<th data-name="bottoni" data-priority="10"></th>';
        echo '</tr>';
        ?>
      </thead>
      <tbody>
      </tbody>
    </table>
    <input type="hidden" name="ordinatocliente" id="ordinatocliente" value="0">
  </div>
</div>

<script type="text/javascript">
  var table;
  var numprodotti = 0;
  var idbtndel = 0;
  Number.prototype.round = function(decimals) {
    return Number((Math.round(this + "e" + decimals) + "e-" + decimals));
  }
  $(function() {
    $('#ordinetemp').find('tr').each(function() {
      numriga = $(this).attr('id');
      attiva_tiporiga(numriga);
      numprodotti++;
    });
    idbtndel = (numprodotti > 0) ? numprodotti - 1 : 0;

    attiva_btn_rimuoviprodotto();
    sconti();
    updatenumprod();
    enablesalva();

    $("input[name='sconto[]']").each(function() {
      calcolosconti(this);
    });

    $("#formordine").submit(function() {
      var erroreqta = false;
      $("input[name='quantita[]']").each(function() {
        if ($(this).val() < 1) {
          erroreqta = true;
          $(this).css('border', '2px solid red');
        } else {
          $(this).css('border', '');
        }
      });
      return !erroreqta;
    });

    table = $("#tabellaprodotti").DataTable({
      dom: 'Bfrtip',
      "orderClasses": false,
      'processing': true,
      'serverSide': true,
      'serverMethod': 'post',
      'ajax': {
        'url': '<?= base_url('ajax/getlistino/') ?>',
        'data': function(d) {
          d.idcliente = "<?= $cliente->idcliente ?>";
          d.ordinatocliente = $('#ordinatocliente').val();
        }
      },
      'columns': [{
          data: 'codice',
          className: 'codice'
        },
        {
          data: 'descrizione',
          className: 'descrizione'
        },
        {
          data: 'prezzo',
          className: 'prezzo'
        },
        {
          data: 'um',
          className: 'um'
        },
        {
          data: 'dataordine',
          className: 'dataordine'
        },
        {
          data: 'ultimoprezzo',
          className: 'ultimoprezzo'
        },
        {
          data: 'ultimaqta',
          className: 'ultimaqta'
        },
        {
          data: 'ultimosconto',
          className: 'ultimosconto'
        },
        {
          data: 'datasospeso',
          className: 'datasospeso'
        },
        {
          data: 'note',
          className: 'note'
        },
        {
          defaultContent: ''
        },
      ],
      buttons: [{
          text: "Ordinati dal cliente",
          className: "btn btn-secondary btn-sm btn_filtraordinati",
          action: function(e, dt, node, config) {
            $('#ordinatocliente').val(1)
            dt.draw();
          }
        },
        {
          text: "Tutti prodotti",
          className: "btn btn-primary btn-sm btn_filtratutti",
          action: function(e, dt, node, config) {
            $('#ordinatocliente').val(0)
            dt.draw();
          }
        },
      ],
      "columnDefs": [{
          "targets": "codice",
          "visible": true
        },
        {
          "targets": "dataordine",
          "render": $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY'),
          "visible": true
        },
        {
          "targets": "bottoni",
          "visible": true
        },
        {
          "targets": "ultimosconto",
          "visible": true
        },
        {
          "targets": "tags",
          "visible": false
        },
        {
          "targets": "descrizione",
          "width": "20%"
        },
        {
          "targets": "datasospeso",
          "visible": false
        },
        {
          "targets": "note",
          "visible": true
        },
        {
          "targets": 10,
          "orderable": false,
          "render": function(data, type, row) {
            if (row.datasospeso == '' || row.datasospeso == null) {
              return '<a href="#" title="Aggiungi" class="btn btn-primary aggiungi" onClick="aggiungi(this)"> + </a>';
            } else {
              return '<a href="#" title="Aggiungi" class="btn btn-default aggiungi" onClick="aggiungi(this,\'personalizzato\')"> + </a>';
            }
          }
        },
      ],
      "language": {
        "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
      },
      responsive: true,
      cache: false,
      stateSave: false,
      "order": [
        [0, "asc"],
        [4, "desc"],
      ],
    }).on('draw', function() {
      if ($("#ordinatocliente").val() == 1) {
        $(".btn_filtraordinati").removeClass('btn-secondary').addClass('btn-primary');
        $(".btn_filtratutti").removeClass('btn-primary').addClass('btn-secondary');
      } else {
        $(".btn_filtraordinati").removeClass('btn-primary').addClass('btn-secondary');
        $(".btn_filtratutti").removeClass('btn-secondary').addClass('btn-primary');
      }
    });
  });

  $('#addcustprod').click(function() {
    numprodotti++;
    idbtndel++;
    var htmlrow = '';
    htmlrow += '<tr id="' + idbtndel + '">';
    htmlrow += '<td><select name="tiporiga[]" class="form-control"><option value="01">Vendita</option><option value="04">Omaggio</option><option value="07">Comodato d\'uso</option><option value="08">Sconto incondizionato</option></select></td>';
    htmlrow += '<td><input type="text" name="codice[]" value="00 001" class="form-control inputcodice" id="codice' + idbtndel + '" readonly></td>';
    htmlrow += '<td><input type="text" name="descrizione[]" id="descrizione' + idbtndel + '" value="" size="40" maxlength="40" class="form-control" required></td>';
    htmlrow += '<td></td>';
    htmlrow += '<td><input type="number" step="0.01" name="prezzoordine[]" value="" class="form-control inputprezzoordine" required><input type="hidden" name="prezzolistino[]" value=""></td>';
    htmlrow += '<td><input type="text" name="sconto[]" value="" class="form-control inputsconto"></td>';
    htmlrow += '<td><input type="number" step="0.01" name="prezzo[]" value="" class="form-control inputprezzo" readonly></td>';
    htmlrow += '<td><input type="number" step="1" name="quantita[]" size="4" class="form-control" required min="1"></td>';
    htmlrow += '<td><input type="text" name="note[]" class="form-control"></td>';
    htmlrow += '<td><a href="#" class="btn btn-danger" id="rimuoviprodotto' + idbtndel + '">-</a></td>';
    htmlrow += '</tr>';
    $('#ordinetemp').append(htmlrow);
    setrimuoviprodotto(idbtndel);
    updatenumprod();
    sconti();
    $('#descrizione' + idbtndel).focus();
  });

  function aggiungi(el, stato = 'attivo') {
    var parenttr = $(el).closest('tr');
    var codice = parenttr.find('.codice').text();
    var readonly = 'readonly';
    var sospeso_bg = '';
    var note = parenttr.find('.note').text();
    if (stato == 'personalizzato') {
      var descrizione = codice + ' ' + parenttr.find('.descrizione').text();
      var codice = '00 001';
      readonly = '';
      sospeso_bg = ' style="background-color: orange" ';
      // note = "ARTICOLO SOSPESO";
    } else {
      var descrizione = parenttr.find('.descrizione').text();
    }
    var prezzolistino = parenttr.find('.prezzo').text();
    var ultimoprezzo = parenttr.find('.ultimoprezzo').text();
    var ultimosconto = parenttr.find('.ultimosconto').text();
    if (ultimoprezzo > 0) {
      prezzoordine = ultimoprezzo;
    } else {
      prezzoordine = prezzolistino;
      ultimoprezzo = prezzolistino;
    }
    var prezzo = prezzoordine;
    var um = parenttr.find('.um').text();
    numprodotti++;
    idbtndel++;

    var htmlrow = '';
    htmlrow += '<tr id="' + idbtndel + '" ' + sospeso_bg + '>';
    htmlrow += '<td><select name="tiporiga[]" class="form-control">';
    <?php
    foreach ($this->tiporiga as $key => $value) {
      echo 'htmlrow += \'<option value="' . $key . '">' . addslashes($value) . '</option>\';' . "\n";
    }
    ?>
    htmlrow += '<td><input type="text" name="codice[]" value="' + codice + '" class="form-control inputcodice" readonly></td>';
    htmlrow += '<td><input type="text" name="descrizione[]" value="' + convertiapici(descrizione) + '" size="50" class="form-control" ' + readonly + '></td>';
    htmlrow += '<td>' + prezzolistino + '</td>';
    htmlrow += '<td><input type="number" step="0.01" prezzolistino="' + prezzolistino + '" ultimoprezzo="' + ultimoprezzo + '" name="prezzoordine[]" value="' + prezzoordine + '" class="form-control inputprezzoordine" required><input type="hidden" name="prezzolistino[]" value="' + prezzolistino + '"></td>';
    htmlrow += '<td><input type="text" ultimosconto="' + ultimosconto + '" name="sconto[]" value="' + ultimosconto + '" class="form-control inputsconto"></td>';
    htmlrow += '<td><input type="number" step="0.01" name="prezzo[]" value="' + prezzo + '" class="form-control inputprezzo" readonly></td>';
    htmlrow += '<td nowrap><input type="text" name="quantita[]" size="4" class="form-control" style="display:inline-block" placeholder="' + um + '" required min="1"><small>' + um + '</small></td>';
    htmlrow += '<td><input type="text" name="note[]" value="' + note + '" class="form-control"></td>';
    htmlrow += '<td><a href="#" class="btn btn-danger" id="rimuoviprodotto' + idbtndel + '">-</a></td>';
    htmlrow += '<input type="hidden" name="um[]" value="' + um + '">';
    htmlrow += '</tr>';
    $('#ordinetemp').append(htmlrow);
    setrimuoviprodotto(idbtndel, prezzo);
    updatenumprod();
    attiva_tiporiga(idbtndel);
    sconti();
    $('#' + idbtndel).find("input[name='prezzoordine[]']").focus();
    $('#' + idbtndel).find("input[name='sconto[]']").focus();
    $('#' + idbtndel).find("input[name='quantita[]']").focus();
  };

  function attiva_tiporiga(numriga) {
    var obj_riga = $('tr[id="' + numriga + '"]');
    var obj_tiporiga = obj_riga.find("[name='tiporiga[]']");

    elabora();

    obj_tiporiga.change(function() {
      elabora();
    });

    function elabora() {
      const objPrezzoOrdine = obj_riga.find("input[name='prezzoordine[]']");
      const objSconto = obj_riga.find("input[name='sconto[]']");
      const objQuantita = obj_riga.find("input[name='quantita[]']");
      const prezzoListino = objPrezzoOrdine.attr('prezzolistino');
      const ultimoPrezzo = objPrezzoOrdine.attr('ultimoprezzo');
      const ultimoSconto = objSconto.attr('ultimosconto');
      const tipoRiga = obj_tiporiga.val();

      if (tipoRiga === '01') { // Vendita
        objPrezzoOrdine.attr({
          min: 0.01,
          'data-msg-min': 'Per tipo riga Vendita deve essere >0',
        });
      } else {
        objPrezzoOrdine.removeAttr('min data-msg-min');
      }

      if (tipoRiga === '07') { // Comodato
        objPrezzoOrdine.val(0).prop("readonly", true);
        objSconto.val(0).prop("readonly", true);
        objQuantita.focus();
      } else if (tipoRiga === '04') { // Omaggio
        objPrezzoOrdine.val(prezzoListino).prop("readonly", true);
        obj_riga.find("input[name='prezzo[]']").val(prezzoListino);
        objSconto.val(0).prop("readonly", true);
        objQuantita.focus();
      } else {
        objPrezzoOrdine.val(ultimoPrezzo).prop("readonly", false).focus();
        objSconto.val(ultimoSconto).prop("readonly", false).focus();
        objPrezzoOrdine.focus();
      }
    };

  }

  function setrimuoviprodotto(n, prezzo) {
    $("#rimuoviprodotto" + n).click(function() {
      $(this).parent().parent().remove();
      numprodotti--;
      updatenumprod();
    });

  };

  function updatenumprod() {
    $('#numprodotti').text(numprodotti).attr('title', numprodotti + ' prodotti');
    $('#cart').effect("shake", {
      times: 1
    }, 300);
    enablesalva();
  }

  function enablesalva() {
    if (numprodotti == 0) {
      $('#salvaordine').attr('disabled', '');
    } else {
      $('#salvaordine').removeAttr('disabled');
    }
  }

  function attiva_btn_rimuoviprodotto() {
    $("a[id^='rimuoviprodotto']").click(function() {
      $(this).parent().parent().remove();
      numprodotti--;
      updatenumprod();
    });
  }

  function sconti() {
    $("input[name='prezzoordine[]'], input[name='sconto[]']").focusout(function() {
      calcolosconti(this);
    });
    $("input[name='quantita[]']").focusin(function() {
      calcolosconti(this);
    });
  }

  function calcolosconti(elem) {
    var currentrow = $(elem).closest('tr');

    var sconto = currentrow.find("input[name='sconto[]']").val();
    // console.log(sconto);
    var prezzolistino = currentrow.find("input[name='prezzolistino[]']").val();
    var prezzoordine = currentrow.find("input[name='prezzoordine[]']").val();
    if (sconto >= 100) {
      currentrow.find("input[name='prezzo[]']").val(0);
      currentrow.find("input[name='sconto[]']").val('');
      currentrow.find("input[name='prezzoordine[]']").val(0);
    } else {
      if (prezzoordine != '') {
        currentrow.find("input[name='prezzo[]']").val(prezzoordine);
      } else {
        currentrow.find("input[name='prezzo[]']").val(prezzolistino);
        currentrow.find("input[name='prezzoordine[]']").val(prezzolistino);
        currentrow.find("input[name='sconto[]']").val('');
      }
      if (prezzoordine != '' && sconto != '') {
        prezzo = prezzoordine - (prezzoordine * sconto / 100);
        if (prezzo > 0) currentrow.find("input[name='prezzo[]']").val(prezzo.round(2).toFixed(2));
        else currentrow.find("input[name='prezzo[]']").val(0);
      }
    }
  }

  function convertiapici(string) {
    return string.replace(/"/g, "&quot;");
  }
</script>