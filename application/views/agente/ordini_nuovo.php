<section class="content no-padding-xs">
  <div class="box box-warning">
    <div class="box-header with-border">
      <h3 class="box-title">Cliente: <?php echo $cliente->ragsoc ?></h3>

      <div class="box-tools pull-right">
        <span id="cart">
          <span data-toggle="tooltip" title="0 Prodotti" data-selector="true" class="badge bg-yellow" id="numprodotti">0</span>
          <span class="glyphicon glyphicon-shopping-cart" style="color: orange"></span></span>
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
        </button>
      </div>
    </div>
    <div class="box-body table-responsive">
      <form action="<?php echo base_url('agente/ordini_salva') ?>" method="post" id="formordine">
        <table class="table" style="min-width: 1200px !important;">
          <thead>
            <tr>
              <th>Tipo riga</th>
              <th>Codice</th>
              <th>Descrizione</th>
              <th>Prezzo listino</th>
              <th>Prezzo ordine</th>
              <th>Sconto</th>
              <th>Prezzo finale</th>
              <th>Quantità</th>
              <th>Note</th>
              <th></th>
            </tr>
          </thead>
          <tbody id="ordinetemp">

          </tbody>
          <tfoot>
            <tr>
              <td colspan="7">
                <input type="submit" id="salvaordine" name="salvaordine" class="btn btn-success pull-right" value="Salva ordine">
                <a href="#" class="btn btn-primary" id="addcustprod">Aggiungi prodotto personalizzato</a>
              </td>
            </tr>
          </tfoot>
        </table>
        <?php
        echo form_hidden('idcliente', $cliente->idcliente);
        echo form_label('Note aggiuntive per questo ordine:');
        echo form_textarea('noteagente', '', 'class="form-control" style="height:100px"');
        ?>
      </form>
    </div>
  </div>


  <div class="row">
    <div class="col-xs-12">
      <?php
      $this->load->view('agente/listino_part_ajax'); // i=0 serve quando si modifica, qui i prod iniziali sono 0
      ?>
    </div>
    <!-- /.col -->
  </div>
  <!-- /.row -->
</section>
<!-- /.content -->