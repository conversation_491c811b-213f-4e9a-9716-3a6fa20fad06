    <!-- Main content -->
    <section class="content no-padding-xs">
        <?php if ($this->session->userdata["ruolo"] == 'Agente') : ?>
            <a href="<?php echo base_url('agente/ordini_nuovo/' . $idcliente) ?>" class="btn btn-primary">Nuovo ordine</a>
        <?php endif; ?>
        <div class="row">
            <div class="col-xs-12">
                <div class="box">

                    <!-- /.box-header -->
                    <div class="box-body table-responsive">
                        <table id="tabelladati" class="table table-bordered table-hover">
                            <thead>
                                <?php
                                $campi = array(
                                    'idordine' => '# ordine',
                                    // 'idcliente'=>'# cliente',
                                    'ragsoc' => 'ragione sociale',
                                    'dataordine' => 'data',
                                    // 'numprodotti'=>'numero prodotti',
                                    'totaleordine' => 'totale',
                                    'datainviato' => 'inviato',
                                    'datamodifica' => 'doc',
                                );
                                echo '<tr>';
                                foreach ($campi as $campo => $label) {
                                    echo '<th name="' . $campo . '">' . $label . '</th>';
                                }
                                echo '<th></th>';
                                echo '</tr>';
                                ?>
                            </thead>
                            <tbody>
                            </tbody>
                        </table>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </section>
    <!-- /.content -->

    <link rel="stylesheet" href="<?php echo asset_url('plugins/iCheck/all.css') ?>">

    <script src="<?php echo asset_url('plugins/iCheck/icheck.min.js') ?>"></script>
    <script type="text/javascript">
        var table;
        $(function() {
            table = $("#tabelladati").DataTable({
                "language": {
                    "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
                },
                // responsive: true,
                'processing': true,
                'serverSide': true,
                'serverMethod': 'post',
                'ajax': {
                    'url': '<?= base_url('agente/get_ordini_ajax/') ?>',
                    'data' : {
                        'idcliente': "<?= $idcliente ?>"
                    }
                },
                'columns': [{
                        data: 'idordine'
                    },
                    {
                        data: 'ragsoc'
                    },
                    {
                        data: 'dataordine'
                    },
                    {
                        data: 'totaleordine'
                    },
                    {
                        data: 'datainviato'
                    },
                    {
                        data: 'datamodifica'
                    },
                    {
                        defaultContent: ''
                    },
                ],

                stateSave: false,
                "order": [
                    [2, "desc"]
                ],
                "columnDefs": [{
                        "targets": [2, 4],
                        "render": $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY HH:mm')
                    },
                    {
                        "targets": 3,
                        "render": function(data, type, row) {
                            return '€' + data;
                        }
                    },
                    {
                        "targets": 6,
                        "orderable":  false,
                        "render": function(data, type, row) {
                            out = '<a href="<?= base_url('agente/ordini_scheda/') ?>' + row.idordine + '" class="btn btn-primary ">Visualizza</a>';
                            <?php if ($this->session->ruolo == 'Agente') { ?>
                                out += ' <a href="<?= base_url('agente/ordini_duplica/') ?>' + row.idordine + '" class="btn btn-default ">Duplica</a> ';
                            <?php } ?>
                            return out;
                        }
                    },
                ]
            });
        });
    </script>