<?php
// var_dump($ordine);
// var_dump($prodotti);
// var_dump($cliente);
// var_dump($agente);
?>
<!-- Main content -->
<section class="invoice">
  <!-- title row -->
  <div class="row">
    <div class="col-xs-12">
      <h2 class="page-header">
        <i class="fa fa-globe"></i> <?php echo $cliente->ragsoc ?>
      </h2>
    </div>
    <!-- /.col -->
  </div>
  <!-- info row -->
  <div class="row invoice-info">
    <div class="col-sm-4 invoice-col">
      <h4>Agente</h4>
      <address>
        <?php if ($agente) : ?>
          <?php echo $agente->nome . ' ' . $agente->cognome ?><br>
          <?php echo $agente->email ?>
        <?php endif; ?>
      </address>
    </div>
    <!-- /.col -->
    <div class="col-sm-4 invoice-col">
      <h4>Cliente</h4>
      <address>
        <?php
        if ($cliente->ragsoc != '') echo $cliente->ragsoc . "<br>";
        if ($cliente->codice != '') echo 'codice: ' . $cliente->codice . "<br>";
        if ($cliente->piva != '') echo 'piva: ' . $cliente->piva . "<br>";
        if ($cliente->cf != '') echo 'CF: ' . $cliente->cf . "<br>";
        if ($cliente->indirizzo != '') echo 'via: ' . $cliente->indirizzo . "<br>";
        if ($cliente->cap != '' || $cliente->citta != '') echo 'cap: ' . $cliente->cap . ' città: ' . $cliente->citta;
        if ($cliente->provincia != '') echo ' (' . $cliente->provincia . ')';
        echo "<br>";
        if (trim($cliente->altradestinazione) != '') echo 'altra destinazione: ' . nl2br($cliente->altradestinazione) . "<br>";
        if ($cliente->esentesdipec == 1) echo "Esente sdi/pec<br>";
        else {
          if ($cliente->sdi != '') echo 'sdi: ' . $cliente->sdi . "<br>";
          if ($cliente->pec != '') echo 'pec: ' . $cliente->pec . "<br>";
        }
        if ($cliente->pagamento != '') echo "pagamento: " . $cliente->pagamento . "<br>";
        ?>
      </address>
    </div>
    <!-- /.col -->
    <div class="col-sm-4 invoice-col">
      <h4>Ordine</h4>
      <b>Numero ordine web:</b> <?php echo $ordine->idordine ?><br>
      <b>Numero ordine ERP:</b> <?php echo $DOC_ERP ?><br>
      <b>Data ordine:</b> <?php echo date('d-m-Y H:i:s', strtotime($ordine->dataordine)) ?><br>
      <?php if ($ordine->datainviato != '') : ?>
        <b>Data inviato:</b> <?php echo date('d-m-Y H:i:s', strtotime($ordine->datainviato)) ?><br>
      <?php endif; ?>
      <?php if ($ordine->datamodifica != '') : ?>
        <b>Data modifica:</b> <?php echo date('d-m-Y H:i:s', strtotime($ordine->datamodifica)) ?><br>
      <?php endif;
      if ($cliente->tipodocumento != '') echo '<p><b>Tipo documento:</b> ' . $cliente->tipodocumento . "</p>";
      ?>
    </div>
    <!-- /.col -->
  </div>
  <!-- /.row -->

  <!-- Table row -->
  <div class="row">
    <div class="col-xs-12 table-responsive">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>Tipo riga</th>
            <th>Codice #</th>
            <th>Quantità</th>
            <th>Descrizione</th>
            <th>Note</th>
            <th>Prezzo listino</th>
            <th>Prezzo ordine</th>
            <th>Sconto</th>
            <th>Prezzo</th>
            <th>Totale</th>
          </tr>
        </thead>
        <tbody>
          <?php
          $totale = 0;
          foreach ($prodotti as $p) {
            $totale += $p->prezzo * $p->quantita;
            echo "<tr>";
            echo "<td>" . $this->tiporiga[$p->tiporiga] . "</td>";
            echo "<td>" . $p->codice . "</td>";
            echo "<td>" . $p->quantita . ' ' . $p->um . "</td>";
            echo "<td>" . $p->descrizione . "</td>";
            echo "<td>" . $p->note . "</td>";
            echo "<td>€" . $p->prezzolistino . "</td>";
            echo "<td>€" . $p->prezzoordine . "</td>";
            echo "<td>" . $p->sconto . "</td>";
            echo "<td>€" . $p->prezzo . "</td>";
            echo "<td>€" . number_format($p->prezzo * $p->quantita, 2) . "</td>";
            echo "</tr>";
          }
          ?>


        </tbody>
      </table>
    </div>
    <!-- /.col -->
  </div>
  <!-- /.row -->

  <div class="row">
    <!-- accepted payments column -->
    <div class="col-xs-6">
      <p class="lead">Note agente:</p>

      <p class="text-muted well well-sm no-shadow" style="margin-top: 10px;">
        <?php echo nl2br($ordine->noteagente) ?>
      </p>
    </div>
    <!-- /.col -->
    <div class="col-xs-6">
      <p class="lead pull-right">Totale: €<?php echo number_format($totale, 2) ?></p>
    </div>
    <!-- /.col -->
  </div>
  <!-- /.row -->

  <?php if (!$nobtns) : ?>
    <!-- this row will not appear when printing -->
    <div class="row no-print">
      <div class="col-xs-12">
        <!-- <a href="invoice-print.html" target="_blank" class="btn btn-default"><i class="fa fa-print"></i> Print</a> -->
        <a onclick="return confirm('Sicuro di eliminare questo ordine?');" href="<?php echo base_url('agente/ordini_elimina/' . $ordine->idordine . '/' . $ordine->idcliente) ?>" class="btn btn-danger pull-right"><i class="fa fa-edit"></i> Elimina </a>
        <a href="<?php echo base_url('agente/ordini_modifica/' . $ordine->idordine) ?>" class="btn btn-success pull-right" style="margin-right: 5px;"><i class="fa fa-edit"></i> Modifica </a>

        <?php if ($ordine->datainviato == '') : ?>
          <a href="<?php echo base_url('agente/ordini_invia/' . $ordine->idordine) ?>" class="btn btn-primary pull-right" style="margin-right: 5px;">
            <i class="fa fa-envelope"></i> Invia </a>
        <?php endif; ?>
      </div>
    </div>
  <?php endif; ?>
</section>
<!-- /.content -->
<div class="clearfix"></div>