<!DOCTYPE html>
<html>

<head>
  <meta charset="utf-8">
  <style>
    .tabella {
      font-size: 10pt;
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .tabella th {
      padding-top: 12px;
      padding-bottom: 12px;
      text-align: left;
      background-color: #26a766;
      color: white;
      vertical-align: middle;
    }

    .tabella th.totale {
      /*text-align: right;*/
      font-size: 12pt;
      font-weight: bold;
    }

    .tabella td {
      font-size: 8pt;
    }
  </style>
</head>

<body>
  <?php
  if ($colonnaqta) $ppalign = 'align=""';
  else $ppalign = 'align="right"';
  ?>
  <table class="tabella">
    <thead>
      <tr>
        <th>Codice #</th>
        <?php if ($colonnaqta) :  ?>
          <th>Quantità</th>
        <?php else : ?>
          <th>UM</th>
        <?php endif ?>
        <!-- <th>Note</th> -->
        <!-- <th>Prezzo listino</th> -->
        <th <?php echo $ppalign ?>>
          Prezzo preventivo</th>
        <?php if ($colonnesconto[1] == 1) :  ?>
          <th>Sconto</th>
        <?php endif ?>
        <?php if ($colonnesconto[2] == 1) :  ?>
          <th>Sconto2</th>
        <?php endif ?>
        <?php if ($colonnesconto[3] == 1) :  ?>
          <th>Sconto3</th>
        <?php endif ?>
        <?php if ($colonnesconto[1] == 1) :  ?>
          <th <?php echo $ppalign ?>>Prezzo scontato</th>
        <?php endif ?>
        <?php if ($colonnaqta) :  ?>
          <th style="text-align:right">Totale</th>
        <?php endif ?>
      </tr>
    </thead>
    <tbody>
      <?php
      $totale = 0;
      foreach ($prodotti as $p) {
        $colspan = 2;
        if ($p->quantita == 0) $p->quantita = 1;
        $totale += $p->prezzo * $p->quantita;
        echo "<tr>";
        echo "<td>" . $p->codice . " " . $p->descrizione;
        if ($p->note) echo '<br>Nota: ' . $p->note;
        echo "</td>";
        if ($colonnaqta) {
          echo "<td>" . $p->quantita . ' ' . $p->um . "</td>";
          $colspan++;
        } else {
          echo "<td>" . $p->um . "</td>";
          $colspan++;
        }
        // echo "<td>".$p->note."</td>";
        // echo "<td>€".$p->prezzolistino."</td>";
        echo '<td ' . $ppalign . '>';
        echo "€ " . $p->prezzopreventivo . "</td>";
        if ($colonnesconto[1] == 1) {
          echo "<td>" . $p->sconto . "</td>";
          $colspan++;
        }
        if ($colonnesconto[2] == 1) {
          echo "<td>" . $p->sconto2 . "</td>";
          $colspan++;
        }
        if ($colonnesconto[3] == 1) {
          echo "<td>" . $p->sconto3 . "</td>";
          $colspan++;
        }
        if ($colonnesconto[1] == 1) {
          echo "<td " . $ppalign . ">€ " . $p->prezzo . "</td>";
          $colspan++;
        }
        if ($colonnaqta) {
          echo '<td style="text-align: right">€ ' . number_format($p->prezzo * $p->quantita, 2) . "</td>";
          $colspan++;
        }
        echo "</tr>";
        // echo '<tr>';
        // echo '<td colspan="'.$colspan.'">'.$p->descrizione."</td>";
        // echo "</tr>";
        echo '<tr>';
        echo '<td colspan="' . $colspan . '"><hr></td>';
        echo "</tr>";
      }
      if ($colonnaqta) :
      ?>
    <tfoot>
      <tr>
        <th class="totale" colspan="<?php echo $colspan ?>">Totale preventivo: € <?php echo number_format($totale, 2) ?></th>
      </tr>
    </tfoot>
  <?php endif ?>
  </tbody>
  </table>
  <?php if ($preventivo->noteagente != '') : ?>
    <br>
    <br>
    <br>
    <table>
      <tr>
        <th>Note</th>
      </tr>
      <tr>
        <td><?php echo nl2br($preventivo->noteagente) ?></td>
      </tr>
    </table>
  <?php endif ?>
  <br>
  <br>
  <br>
  <h4>Agente</h4>
  <?php
  if ($agente) {
    echo $agente->nome . ' ' . $agente->cognome . "<br>";
    echo $agente->email . "<br>";
    echo $agente->telefono;
  }
  ?>
</body>

</html>