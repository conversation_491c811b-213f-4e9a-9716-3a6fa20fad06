    <!-- Main content -->
    <section class="content no-padding-xs">
      <div class="row">
        <div class="col-xs-12">
          <div class="box">

            <!-- /.box-header -->
            <div class="box-body">
              <table id="tabelladati" class="table table-bordered table-hover">
                <thead>
<?php
  if (count($data)>0) :
  $campi=array_keys(get_object_vars($data[0]));
    $campi=array(
      'idpreventivo' => '# preventivo',
      // 'idcliente'=>'# cliente',
      'cliente'=>'cliente',
      'agente'=>'agente',
      'datapreventivo'=>'data',
      // 'numprodotti'=>'numero prodotti',
      'totalepreventivo'=>'totale',
      );
  echo '<tr>';
  foreach ($campi as $campo=>$label) {
    echo '<th name="'.$campo.'">'.$label.'</th>';
  }
  echo '<td></td>';
  echo '</tr>';
?>
                </thead>
                <tbody>
<?php foreach ($data as $row) {
    echo '<tr>';
    foreach ($campi as $campo=>$label) {
      $dato=$row->{$campo};
      echo '<td>'.$dato.'</td>';
    }
    echo '<td>';
    echo '<a href="'.base_url('preventivi/preventivi_scheda/'.$row->idpreventivo).'" class="btn btn-primary ">Visualizza</a> ';
    // if ($this->session->ruolo=='Agente') {
    //   echo '<a href="'.base_url('preventivi/preventivi_duplica/'.$row->idpreventivo).'" class="btn btn-default ">Duplica</a> ';
    // }
    echo '</td>';
    echo '</tr>';
}
endif;
?>

                </tbody>

              </table>
            </div>
            <!-- /.box-body -->
          </div>
          <!-- /.box -->
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->

<link rel="stylesheet" href="<?php echo asset_url('plugins/iCheck/all.css') ?>">

<script src="<?php echo asset_url('plugins/iCheck/icheck.min.js') ?>"></script>
<script type="text/javascript">
  var table;
$(function () {
  table=$("#tabelladati").DataTable({
    "language": {
      "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
    },
    responsive: true,
    // "orderFixed": [[0, 'asc']],

    stateSave: false,
    "order": [[3, "desc"]],
    "columnDefs": [
        {
          "targets": [3],
          "render" : $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY HH:mm')
        },
        {
          "targets": 4,
          "render": function (data, type, row) {
             return '€'+data;
            }
        },
      ]
  });
});
</script>

