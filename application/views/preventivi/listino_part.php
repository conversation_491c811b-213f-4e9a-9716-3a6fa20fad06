<div class="box">
  <div class="overlay">
    <i class="fa fa-refresh fa-spin"></i>
  </div>
  <!-- /.box-header -->
  <div class="box-body">
    <p>Listino</p> <br>
    <table id="tabellaprodotti" class="table table-bordered table-hover">
      <thead>
        <?php
        if (count($listino) > 0) :
          $campi = array_keys(get_object_vars($listino[0]));
          echo '<tr>';
          foreach ($campi as $campo) {
            echo '<th class="' . $campo . '">' . $campo . '</th>';
          }
          echo '<th data-name="bottoni"></th>';
          echo '</tr>';
        ?>
      </thead>
      <tbody>
      <?php foreach ($listino as $row) {
            echo '<tr>';
            foreach ($campi as $campo) {
              $dato = $row->{$campo};

              if ($campo == 'um') {
                $ordinato = 'data-search=""';
              } elseif ($campo == 'codice') {
                $ordinato = 'data-search="' . str_replace(' ', '', $dato) . '"';
              } else $ordinato = '';
              echo '<td ' . $ordinato . ' class="' . $campo . '">' . $dato . '</td>';
            }
            echo '<td><a href="#" title="Aggiungi" class="btn btn-primary aggiungi" onClick="aggiungi(this)"> + </a></td>';
            echo '</tr>';
          }
        endif;
      ?>
      </tbody>
    </table>
  </div>
  <!-- /.box-body -->
</div>
<!-- /.box -->

<script type="text/javascript">
  var table;
  var numprodotti = $('#ordinetemp').children('tr').length;
  var idbtndel = <?php echo $i ?>;
  $(function() {

    $('#addcustprod').click(function() {
      numprodotti++;
      idbtndel++;
      $('#ordinetemp').append('<tr id="' + idbtndel + '"><td><input type="text" name="codice[]" value="" class="form-control inputcodice" id="codice' + idbtndel + '"></td><td><input type="text" name="descrizione[]" value="" size="50" class="form-control" required></td><td></td><td><input type="text" name="prezzopreventivo[]" value="" class="form-control inputprezzopreventivo" required><input type="hidden" name="prezzolistino[]" value=""></td><td><input type="text" name="sconto[]" value="" class="form-control inputsconto"></td><td><input type="text" name="sconto2[]" value="" class="form-control inputsconto2"></td><td><input type="text" name="sconto3[]" value="" class="form-control inputsconto3"></td><td><input type="text" name="prezzo[]" value="" class="form-control inputprezzo" readonly></td><td><input type="text" name="quantita[]" size="4" class="form-control"></td><td><input type="text" name="note[]" class="form-control"></td><td><a href="#" class="btn btn-danger" id="rimuoviprodotto' + idbtndel + '">-</a></td></tr>');
      setrimuoviprodotto(idbtndel);
      updatenumprod();
      sconti();
      $('#codice' + idbtndel).focus();
    })

    attiva();
    sconti();
    updatenumprod();
    enablesalva();

    table = $("#tabellaprodotti").on('processing.dt', function(e, settings, processing) {
      $('#processingIndicator').css('display', processing ? 'block' : 'none');
    }).on('init.dt', function() {
      $(".overlay").remove();
      // console.log( 'Table initialisation complete: '+new Date().getTime() );
    }).DataTable({
      dom: 'frtip',
      "orderClasses": false,
      "deferRender": true,
      "columnDefs": [{
          "targets": "dataordine",
          "render": $.fn.dataTable.render.moment('YYYY-MM-DD H:m:s', 'DD-MM-YYYY'),
          "visible": true
        },
        {
          "targets": "bottoni",
          "responsivePriority": 1,
          "visible": true
        },
        {
          "targets": "ultimosconto",
          "visible": true
        },
      ],
      "language": {
        "url": "<?php echo asset_url('plugins/datatables/') ?>it_IT.json"
      },
      responsive: true,
      cache: true,
      // "orderFixed": [[0, 'asc']],

      stateSave: false,
      "order": [
        [0, "asc"]
      ],

    });
  });

  function aggiungi(el) {
    var parenttr = $(el).closest('tr');
    // if (typeof $(parenttr).html()=='undefined') {
    //   parenttr = $(el).closest('tr').prev();
    // }
    var codice = parenttr.find('.codice').text();
    var descrizione = parenttr.find('.descrizione').text().replace(/"/g, "&quot;");
    var prezzolistino = parenttr.find('.prezzo').text();
    var ultimosconto = parenttr.find('.ultimosconto').text();
    var ultimosconto2 = parenttr.find('.ultimosconto2').text();
    var ultimosconto3 = parenttr.find('.ultimosconto3').text();
    var prezzopreventivo = prezzolistino;
    var prezzo = prezzopreventivo;
    var um = parenttr.find('.um').text();
    numprodotti++;
    idbtndel++;

    $('#ordinetemp').append('<tr id="' + idbtndel + '"><td><input type="text" name="codice[]" value="' + codice + '" class="form-control inputcodice" readonly></td><td><input type="text" name="descrizione[]" value="' + descrizione + '" size="50" class="form-control" readonly></td><td>' + prezzolistino + '</td><td><input type="text" name="prezzopreventivo[]" value="' + prezzopreventivo + '" class="form-control inputprezzopreventivo" required><input type="hidden" name="prezzolistino[]" value="' + prezzolistino + '"></td><td><input type="text" name="sconto[]" value="' + ultimosconto + '" class="form-control inputsconto"></td><td><input type="text" name="sconto2[]" value="' + ultimosconto2 + '" class="form-control inputsconto"></td><td><input type="text" name="sconto3[]" value="' + ultimosconto3 + '" class="form-control inputsconto"></td><td><input type="text" name="prezzo[]" value="' + prezzo + '" class="form-control inputprezzo" readonly></td><td nowrap><input type="text" name="quantita[]" size="4" class="form-control" style="display:inline-block" placeholder="' + um + '"><small>' + um + '</small></td><td><input type="text" name="note[]" class="form-control"></td><td><a href="#" class="btn btn-danger" id="rimuoviprodotto' + idbtndel + '">-</a></td></tr><input type="hidden" name="um[]" value="' + um + '">');
    setrimuoviprodotto(idbtndel);
    updatenumprod();
    sconti();
    $('#' + idbtndel).find("input[name='prezzopreventivo[]']").focus();
    $('#' + idbtndel).find("input[name='sconto[]']").focus();
    $('#' + idbtndel).find("input[name='sconto2[]']").focus();
    $('#' + idbtndel).find("input[name='sconto3[]']").focus();
    $('#' + idbtndel).find("input[name='quantita[]']").focus();
  }

  function setrimuoviprodotto(n) {
    $("#rimuoviprodotto" + n).click(function() {
      $(this).parent().parent().remove();
      numprodotti--;
      updatenumprod();
    });
  }

  function updatenumprod() {
    $('#numprodotti').text(numprodotti).attr('title', numprodotti + ' prodotti');
    $('#cart').effect("shake", {
      times: 1
    }, 300);
    enablesalva();
  }

  function enablesalva() {
    if (numprodotti == 0) {
      $('#salvaordine').attr('disabled', '');
    } else {
      $('#salvaordine').removeAttr('disabled');
    }
  }

  function attiva() {
    $("a[id^='rimuoviprodotto']").click(function() {
      $(this).parent().parent().remove();
      numprodotti--;
      updatenumprod();
    })
  }

  function sconti() {
    Number.prototype.round = function(decimals) {
      return Number((Math.round(this + "e" + decimals) + "e-" + decimals));
    }
    $("input[name='prezzopreventivo[]'], input[name^='sconto']").focusout(function() {
      var currentrow = $(this).closest('tr');

      var sconto = currentrow.find("input[name='sconto[]']").val();
      var sconto2 = currentrow.find("input[name='sconto2[]']").val();
      var sconto3 = currentrow.find("input[name='sconto3[]']").val();
      var prezzolistino = currentrow.find("input[name='prezzolistino[]']").val();
      var prezzopreventivo = currentrow.find("input[name='prezzopreventivo[]']").val();

      if (prezzopreventivo != '') {
        currentrow.find("input[name='prezzo[]']").val(prezzopreventivo);
      } else {
        currentrow.find("input[name='prezzo[]']").val(prezzolistino);
        currentrow.find("input[name='prezzopreventivo[]']").val(prezzolistino);
        currentrow.find("input[name='sconto[]']").val('');
        sconto = '';
        currentrow.find("input[name='sconto2[]']").val('');
        sconto2 = '';
        currentrow.find("input[name='sconto3[]']").val('');
        sconto3 = '';
      }

      if (sconto == '') {
        currentrow.find("input[name='sconto2[]']").val('');
        sconto2 = '';
      }

      if (sconto2 == '') {
        currentrow.find("input[name='sconto3[]']").val('');
        sconto3 = '';
      }

      if (prezzopreventivo != '') {
        if (sconto != '') {
          var prezzo = prezzopreventivo - (prezzopreventivo * sconto / 100);
          if (sconto2 != '') {
            prezzo = prezzo - (prezzo * sconto2 / 100);
            if (sconto3 != '') {
              prezzo = prezzo - (prezzo * sconto3 / 100);
            }
          }
          currentrow.find("input[name='prezzo[]']").val(prezzo.round(2).toFixed(2));
        }
      }
    })
  }
</script>