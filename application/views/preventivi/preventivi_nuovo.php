<section class="content no-padding-xs">
  <div id="processingIndicator">caricamento dati...</div>
  <div class="box box-warning">
    <div class="box-header with-border">
      <div class="box-tools pull-right">
        <span id="cart">
        <span data-toggle="tooltip" title="0 Prodotti" data-selector="true" class="badge bg-yellow" id="numprodotti">0</span>
        <span class="glyphicon glyphicon-shopping-cart" style="color: orange"></span></span>
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
        </button>
      </div>
    </div>
    <div class="box-body">
        <form action="<?php echo base_url('preventivi/preventivi_salva') ?>" method="post" id="formpreventivo">
          <div class="col-lg-3 col-sm-6">
            <label>Cliente: </label>
            <?php echo form_textarea('cliente', '', 'class="form-control" style="height:100px"'); ?>
          </div>
        <table class="table">
          <thead>
            <tr>
              <th>Codice</th>
              <th>Descrizione</th>
              <th>Prezzo listino</th>
              <th>Prezzo preventivo</th>
              <th>Sconto</th>
              <th>Sconto2</th>
              <th>Sconto3</th>
              <th>Prezzo finale</th>
              <th>Quantità</th>
              <th>Note</th>
              <th></th>
            </tr>
          </thead>
          <tbody id="ordinetemp">

          </tbody>
          <tfoot>
            <tr>
              <td colspan="10">
                <input type="submit" id="salvaordine" name="salvaordine" class="btn btn-success pull-right" value="Salva preventivo">
                <a href="#" class="btn btn-primary" id="addcustprod">Aggiungi prodotto personalizzato</a>
              </td>
            </tr>
          </tfoot>
        </table>
        <?php
          echo form_label('Note aggiuntive per questo preventivo:');
          echo form_textarea('noteagente', '', 'class="form-control" style="height:100px"');
        ?>
      </form>
    </div>
    <div class="overlay">
      <i class="fa fa-refresh fa-spin"></i>
    </div>
  </div>


      <div class="row">
        <div class="col-xs-12">
          <?php
            $this->load->view('preventivi/listino_part', ['i'=>0]); // i=0 serve quando si modifica, qui i prod iniziali sono 0
            ?>
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->
    </section>
    <!-- /.content -->

<script>
  $(function() {
    $('#formpreventivo').submit(function(){
      var numrighe = $("input[name='quantita[]']").length;
      var numvuoti=0;
      var numpieni=0;
      $("input[name='quantita[]']").each(function(){
        if($(this).val()=='') {
          numvuoti++;
        }
        else {
          numpieni++;
        }
      })
      // console.log('V: '+numvuoti+' P: '+numpieni);
      if (numpieni>0 && numpieni!=numrighe) {
        alert ('Controlla le quantità:\ncompila tutte o nessuna')
        return false;
      }
      else return true;
    })
  })
</script>

