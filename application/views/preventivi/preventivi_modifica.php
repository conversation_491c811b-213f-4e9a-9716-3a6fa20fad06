<section class="content no-padding-xs">
  <div id="processingIndicator">caricamento dati...</div>
  <div class="box box-warning">
    <div class="box-header with-border">
      <div class="box-tools pull-right">
        <span id="cart">
          <span data-toggle="tooltip" title="0 Prodotti" data-selector="true" class="badge bg-yellow" id="numprodotti">0</span>
          <span class="glyphicon glyphicon-shopping-cart" style="color: orange"></span></span>
        <button type="button" class="btn btn-box-tool" data-widget="collapse"><i class="fa fa-minus"></i>
        </button>
      </div>
    </div>
    <div class="box-body">
      <form action="<?php echo base_url('preventivi/preventivi_modifica_ex') ?>" method="post" id="formpreventivo">
        <div class="row">
          <div class="col-lg-3 col-sm-6">
            <label>Cliente: </label>
            <?php echo form_textarea('cliente', (isset($preventivo)) ? $preventivo->cliente : '', 'class="form-control" style="height:100px"'); ?>
          </div>
          <div class="col-lg-6 col-sm-6">
            <div class="row">
              <div class="col-md-2">
                Seleziona cliente
              </div>
              <div class="col-md-6">
                <?php echo form_dropdown('idcliente', '', '', 'class="form-control" id="select-cliente"'); ?>
              </div>
              <div class="col-md-2">
                <input type="submit" id="creaordine" name="creaordine" class="btn btn-success pull-right" value="Crea ordine">
              </div>
            </div>
          </div>
        </div>
        <div class="table-responsive">
          <table class="table">
            <thead>
              <tr>
                <th>Codice</th>
                <th>Descrizione</th>
                <th>Prezzo listino</th>
                <th>Prezzo preventivo</th>
                <th>Sconto</th>
                <th>Sconto2</th>
                <th>Sconto3</th>
                <th>Prezzo finale</th>
                <th>Quantità</th>
                <th>Note</th>
                <th></th>
              </tr>
            </thead>
            <tbody id="ordinetemp">
              <?php
              $i = 0; // se non si entra rimane zero
              if (isset($preventivo)) {
                foreach ($prodotti as $i => $p) {
                  $readonly = 'readonly';
                  if ($p->prodotto == '') $readonly = ''; // se non c'è json significa prodotto custom
                  echo '<tr id="' . $i . '">';
                  echo '<td><input type="text" name="codice[]" value="' . $p->codice . '" class="form-control inputcodice" ' . $readonly . '></td><td><input type="text" name="descrizione[]" value="' .
                    htmlentities($p->descrizione) . '" size="50" class="form-control" ' . $readonly . '></td><td>' .
                    $p->prezzolistino . '</td><td><input type="text" name="prezzopreventivo[]" value="' .
                    $p->prezzopreventivo . '" class="form-control inputprezzopreventivo" required><input type="hidden" name="prezzolistino[]" value="' .
                    $p->prezzolistino . '"></td><td><input type="text" name="sconto[]" value="' .
                    $p->sconto . '" class="form-control inputsconto"></td><td><input type="text" name="sconto2[]" value="' .
                    $p->sconto2 . '" class="form-control inputsconto2"></td><td><input type="text" name="sconto3[]" value="' .
                    $p->sconto3 . '" class="form-control inputsconto3"></td><td><input type="text" name="prezzo[]" value="' .
                    $p->prezzo . '" class="form-control inputprezzo" readonly></td><td nowrap><input type="text" name="quantita[]" size="4" value="' .
                    $p->quantita . '" style="display:inline-block" class="form-control" placeholder="' . $p->um . '"><small>' . $p->um . '</small></td><td><input type="text" name="note[]" value="' .
                    $p->note . '" class="form-control"></td><td><a href="#" class="btn btn-danger" id="rimuoviprodotto' . $i . '">-</a></td><input type="hidden" name="um[]" value="' .
                    $p->um . '">';
                  echo "</tr>";
                }
              }
              ?>
            </tbody>
            <tfoot>
              <tr>
                <td colspan="10">
                  <input type="submit" id="salvaordine" name="salvaordine" class="btn btn-success pull-right" value="Salva preventivo">
                  <a href="#" class="btn btn-primary" id="addcustprod">Aggiungi prodotto personalizzato</a>
                </td>
              </tr>
            </tfoot>
          </table>
        </div>
        <?php
        echo form_label('Note aggiuntive per questo preventivo:');
        echo form_textarea('noteagente', (isset($preventivo)) ? $preventivo->noteagente : '', 'class="form-control" style="height:100px"');
        if (isset($preventivo)) {
          echo form_hidden('idpreventivo', $preventivo->idpreventivo);
        }
        ?>
      </form>
    </div>
    <div class="overlay">
      <i class="fa fa-refresh fa-spin"></i>
    </div>
  </div>


  <div class="row">
    <div class="col-xs-12">
      <?php
      $this->load->view('preventivi/listino_part', ['i' => $i]); // i serve quando si modifica
      ?>
    </div>
    <!-- /.col -->
  </div>
  <!-- /.row -->
</section>
<!-- /.content -->

<script>
  $(function() {
    $('#formpreventivo').areYouSure();
    $('#select-cliente').select2({
      ajax: {
        url: '<?php echo base_url() ?>Ajax/fetchclienti/json',
        dataType: 'json',
        delay: 250 // wait 250 milliseconds before triggering the request
      }
    });
    $('#creaordine').click(function() {
      if ($('#select-cliente').val() == 0) {
        alert('Seleziona cliente');
        return false;
      } else {
        $('#formpreventivo').attr("action", "<?php echo base_url('preventivi/preventivo_ordine_ex') ?>");
        return true;
      }
      return false;
    })
  });
</script>