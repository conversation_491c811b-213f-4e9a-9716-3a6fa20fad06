<?php
// var_dump($preventivo);
// var_dump($prodotti);
// var_dump($cliente);
// var_dump($agente);
 ?>
    <!-- Main content -->
    <section class="invoice">
      <div class="row invoice-info">
        <div class="col-sm-4 invoice-col">
          <h4>Agente</h4>
          <address>
            <?php if ($agente) : ?>
            <?php echo $agente->nome.' '.$agente->cognome ?><br>
            <?php echo $agente->email ?><br>
            <?php echo $agente->telefono; ?>
            <?php endif; ?>
          </address>
        </div>
        <!-- /.col -->
        <div class="col-sm-4 invoice-col">
          <h4>Cliente</h4>
          <address>
            <?php
              echo (nl2br($preventivo->cliente));
            ?>
          </address>
        </div>
        <!-- /.col -->
        <div class="col-sm-4 invoice-col">
          <h4>Preventivo N° <?php echo $preventivo->idpreventivo ?></h4>
          <b>Data preventivo:</b> <?php echo date('d-m-Y H:i:s', strtotime($preventivo->datapreventivo)) ?><br>
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->

      <!-- Table row -->
      <div class="row">
        <div class="col-xs-12 table-responsive">
          <table class="table table-striped">
            <thead>
            <tr>
              <th>Codice #</th>
              <?php if ($colonnaqta) :  ?>
              <th>Quantità</th>
              <?php else : ?>
              <th>UM</th>
              <?php endif ?>
              <th>Descrizione</th>
              <th>Note</th>
              <!-- <th>Prezzo listino</th> -->
              <!-- <th>Prezzo preventivo</th> -->
              <?php if ($colonnesconto[1]==1) :  ?>
              <th>Sconto</th>
              <?php endif ?>
              <?php if ($colonnesconto[2]==1) :  ?>
              <th>Sconto2</th>
              <?php endif ?>
              <?php if ($colonnesconto[3]==1) :  ?>
              <th>Sconto3</th>
              <?php endif ?>
              <th>Prezzo</th>
              <?php if ($colonnaqta) :  ?>
              <th>Totale</th>
              <?php endif ?>
            </tr>
            </thead>
            <tbody>
            <?php
              $totale=0;
              foreach ($prodotti as $p) {
                if ($p->quantita==0) $p->quantita=1;
                $totale+=$p->prezzo*$p->quantita;
                echo "<tr>";
                echo "<td>".$p->codice."</td>";
                if ($colonnaqta)
                  echo "<td>".$p->quantita.' '.$p->um."</td>";
                else echo "<td>".$p->um."</td>";
                echo "<td>".$p->descrizione."</td>";
                echo "<td>".$p->note."</td>";
                // echo "<td>€".$p->prezzolistino."</td>";
                // echo "<td>€".$p->prezzopreventivo."</td>";
                if ($colonnesconto[1]==1) 
                  echo "<td>".$p->sconto."</td>";
                if ($colonnesconto[2]==1) 
                  echo "<td>".$p->sconto2."</td>";
                if ($colonnesconto[3]==1) 
                  echo "<td>".$p->sconto3."</td>";
                echo "<td>€".$p->prezzo."</td>";
                if ($colonnaqta)
                  echo "<td>€".number_format($p->prezzo*$p->quantita,2)."</td>";
                echo "</tr>";
              }
            ?>


            </tbody>
          </table>
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->

      <div class="row">
        <!-- accepted payments column -->
        <div class="col-xs-6">
          <p class="lead">Note agente:</p>

          <p class="text-muted well well-sm no-shadow" style="margin-top: 10px;">
            <?php echo nl2br($preventivo->noteagente) ?>
          </p>
        </div>
        <!-- /.col -->
        <div class="col-xs-6">
          <?php if ($colonnaqta) : ?>
          <p class="lead pull-right">Totale: €<?php echo number_format($totale,2) ?></p>
          <?php endif ?>
        </div>
        <!-- /.col -->
      </div>
      <!-- /.row -->

    <?php if (!$nobtns) : ?>
      <!-- this row will not appear when printing -->
      <div class="row no-print">
        <div class="col-xs-12">
          <!-- <a href="invoice-print.html" target="_blank" class="btn btn-default"><i class="fa fa-print"></i> Print</a> -->
          <a onclick="return confirm('Sicuro di eliminare questo preventivo?');" href="<?php echo base_url('preventivi/preventivi_elimina_ex/'.$preventivo->idpreventivo) ?>" class="btn btn-danger pull-right"><i class="fa fa-edit"></i> Elimina </a>
          <a href="<?php echo base_url('preventivi/preventivi_modifica/'.$preventivo->idpreventivo) ?>" class="btn btn-success pull-right" style="margin-right: 5px;"><i class="fa fa-edit"></i> Modifica </a>
          <a href="<?php echo base_url('preventivi/preventivi_scheda/'.$preventivo->idpreventivo.'/pdf') ?>" target="_blank" class="btn btn-info pull-right" style="margin-right: 5px;"><i class="fa fa-file-pdf-o"></i> PDF </a>
        </div>
      </div>
    <?php endif; ?>
    </section>
    <!-- /.content -->
    <div class="clearfix"></div>

