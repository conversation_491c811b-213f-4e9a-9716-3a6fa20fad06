body {
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  /* font-weight: 600; */
  overflow-x: hidden;
  overflow-y: auto;
}

/* breakpoint */


/* breakpoint */

.wrapper {
    height: 100%;
    position: relative;
    overflow-x: hidden;
    overflow-y: hidden;
}

.query {
	display: none;
}

.thumbnail {
	display: initial;
	border: 0;
	background:none;
	width: 250px;
}

.login .panel {
	max-width: 450px;
	text-align: left;
	position: relative;
	margin: 20px auto;
}


.login {
	background: #337AB7;
	background-image: radial-gradient(circle, #337AB7, #1C4364); /* Standard syntax (must be last) */
	height: 100vh;
}

.login  .panel-heading {
	padding: 21px 25px;
	border-bottom: 0;
	border-top-left-radius: 0;
    border-top-right-radius:0;
	border-radius:0;
}

.login  .panel {
 	border-radius:0;
	background: #FBFBFB;
	box-shadow: 0 0 20px rgba(0,0,0,0.3);
}


.login .input-group .input-group-addon {
    border-radius: 0;
    border-color: #d2d6de;
    background-color: #337ab7;
    color: #fff;
}

.login .panel-body {
	margin:30px;
}


.login .form-control {

    height: 53px;
}

.login .input-group-addon {
    padding: 6px 12px;
    font-size: 20px;
}


.logo-credits a img{
	width: 150px !important;
}

@media  only screen and (max-width: 1000px) {
	.credits {
		position: relative !important;
		text-align: center !important;
		display: block;

	}
}

@media only screen and (min-width: 1000px) {
	.credits {
		position: fixed;
		bottom: 0;
		display: flex;
		right: 0;
	}


}

	.underlogin {
		color:white;
		text-align:center;
	}
.user-panel .img-circle, .user-header .img-circle{
	filter: invert(100%);
}

.breadcrumb {

	float:right;
	margin-right:25px;
}

.content {
    min-height: 250px;

    padding-left: 25px;
    padding-right:25px;
    top: 23px;
    position: relative;
}


#tabelladati_wrapper .form-group {
	color: #3C8DBC;
}
#tabelladati_wrapper .icheckbox_flat-green {margin: 0 6px 0 15px;}
#tabelladati_wrapper .icheckbox_flat-blue {margin: 0 6px 0 15px;}
#tabelladati_wrapper .icheckbox_flat-orange {margin: 0 6px 0 15px;}

.pulsantefiltri {
	border: 1px solid #BC5C3C;
	border-radius: 5px;
	display: inherit;
	padding: 10px 14px 10px 0px;
	margin: 10px 0px 0 20px;
	color:#BC5C3C;
}
.pulsanteraggruppa {
	border: 1px solid #3C8DBC;
	border-radius: 5px;
	display: inherit;
	padding: 10px 14px 10px 0px;

	margin: 10px 0px 0 20px;
}

.disabled {
    cursor: not-allowed !important;
}

.alert {
    padding: 20px;
    margin: 25px;
}

.inputprezzo {
	min-width: 70px;
}
.inputcodice {
	min-width: 80px;
}

.formspanlabel {
	min-width: 150px;
	background-color: #eee !important;
}

.tags {
	font-size: xx-small;
	display: none;
}
#tabellaprodotti {
	font-size: small;
	/* width: auto !important; */
}

.table {
	width: auto !important;
}

@media (max-width: 576px) {
    .no-padding-xs {
        padding-left: 0;
        padding-right: 0;
    }
}

/* Chat Specific Styles */
.chat-container {
    max-width: 800px;
    margin: 0 auto;
}

.chat-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.chat-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px;
}

.chat-header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chat-title {
    font-weight: bold;
    font-size: 1.2em;
}

.chat-body {
    padding: 0;
}

.chat-messages {
    height: 500px;
    overflow-y: auto;
    padding: 15px;
    background-color: #f0f2f5;
}

.message-wrapper {
    margin-bottom: 15px;
    display: flex;
}

.message-wrapper.sent {
    justify-content: flex-end;
}

.message-wrapper.received {
    justify-content: flex-start;
}

.message-box {
    max-width: 70%;
    padding: 10px;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.message-wrapper.sent .message-box {
    background-color: #dcf8c6;
}

.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.8em;
    color: #666;
}

.message-content {
    word-wrap: break-word;
    white-space: pre-wrap;
}

.chat-footer {
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.chat-input-group {
    display: flex;
    align-items: center;
}

.chat-textarea {
    flex-grow: 1;
    margin-right: 10px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    padding: 10px;
    resize: none;
}

.chat-send-btn {
    background-color: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 45px;
    height: 45px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.chat-send-btn:hover {
    background-color: #0056b3;
}

/* Stile per input di ricerca */
.input-group .input-group-addon {
    background-color: #f5f5f5;
    border-color: #d2d6de;
    color: #999;
}

.input-group .input-group-btn .btn {
    border-color: #d2d6de;
    background-color: #f5f5f5;
    color: #999;
}

.input-group .input-group-btn .btn:hover {
    background-color: #e6e6e6;
}

.message-date-separator {
    text-align: center;
    margin: 20px 0;
    position: relative;
}
.message-date-separator span {
    background: #f8f9fa;
    padding: 5px 15px;
    border-radius: 20px;
    font-size: 0.9em;
    color: #6c757d;
    position: relative;
    z-index: 1;
}
.message-date-separator::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #dee2e6;
    z-index: 0;
}
.email-container { max-width: 600px; margin: 20px auto; font-family: Arial, sans-serif; }

 /* Auto-dismiss alerts */
 .alert.alert-success.alert-dismissible {
    animation: fadeOut 1s ease-in 1s forwards;
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
        display: none;
    }
}